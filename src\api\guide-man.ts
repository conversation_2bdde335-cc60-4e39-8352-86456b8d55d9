import request from "@/utils/request";

const API_VERSION = 'v2';

// 新建作业指导
export function guideAdd(params: Record<string, any>) {
  return request.post({ url: `/${API_VERSION}/guide`, params });
}

// 作业指导列表
export function guideList(params: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/guide`, params });
}

// 删除作业指导
export function guideDelete(params: Record<string, any>) {
  return request.delete({ url: `/${API_VERSION}/guide/${params.id}` });
}

// 编辑作业指导
export function guideEdit(params: Record<string, any>) {
  return request.put({ url: `/${API_VERSION}/guide/${params.id}`, params });
}

// 作业指导详情
export function guideDetail(params: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/guide/${params.id}` });
}

// 作业指导项新建
export function guideItemAdd(params: Record<string, any>) {
  return request.post({ url: `/${API_VERSION}/guide/${params.guideId}/item`, params });
}

// 作业指导项编辑
export function guideItemEdit(params: Record<string, any>) {
  return request.put({ url: `/${API_VERSION}/guide/${params.guideId}/item/${params.id}`, params });
}

// 作业指导项删除
export function guideItemDelete(params: Record<string, any>) {
  return request.delete({ url: `/${API_VERSION}/guide/${params.guideId}/item/${params.id}` });
}