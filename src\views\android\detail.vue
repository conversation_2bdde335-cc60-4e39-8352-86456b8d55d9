<template>
  <div class="edit-popup">
    <el-card class="!border-none" shadow="never">
      <el-page-header class="mb-3 mt-2" :icon="ArrowLeft" @back="goBack">
        <template #content>
          <div class="flex items-center">
            <span class="text-large font-600 mr-3"> {{ `${vehicleDetail.vehicle_name}操作台` || "操作台名称" }} </span>
          </div>
        </template>
        <template #extra>
          <div class="flex items-center">
            <el-button type="primary" @click="handleSubmit">保存并应用</el-button>
          </div>
        </template>
      </el-page-header>
      <el-divider class="!mt-4" />
      <el-scrollbar wrap-class="app-wrap" height="calc(100vh - 224px)">
        <el-backtop target=".app-wrap" :right="60" :bottom="40" />
        <el-form ref="formRef" :inline="true" :model="androidForm" label-width="auto">
          <div class="border-l-2 border-solid border-primary text-xl pl-3 mb-4">基本信息</div>
          <el-form-item label="项目名称" required prop="projectName">
            <el-input v-model="androidForm.projectName" />
          </el-form-item>
          <el-form-item label="设备名称">
            <el-input v-model="androidForm.modelName" />
          </el-form-item>
          <el-form-item label="播放流地址" required prop="easyPlayUrl1">
            <el-input v-model="androidForm.easyPlayUrl1" />
          </el-form-item>
          <el-form-item label="音频流地址" required prop="easyPlayUrl6">
            <el-input v-model="androidForm.easyPlayUrl6" />
          </el-form-item>
          <el-form-item label="车辆ID">
            <el-input v-model="androidForm.vehicleId" />
          </el-form-item>
          <el-form-item label="米文ID">
            <el-input v-model="androidForm.miwenId" />
          </el-form-item>
          <el-form-item label="登录页面名">
            <el-input v-model="androidForm.loginPage" />
          </el-form-item>
          <el-form-item label="NTP校时地址">
            <el-input v-model="androidForm.ntpUrl" />
          </el-form-item>
          <el-form-item label="内网服务器地址">
            <el-input v-model="androidForm.innerNetUrl" />
          </el-form-item>
          <el-form-item label="内网ping地址">
            <el-input v-model="androidForm.innerPingUrl" />
          </el-form-item>
          <el-form-item label="内网mqtt地址">
            <el-input v-model="androidForm.innerNetMqttHost" />
          </el-form-item>
          <br />
          <el-form-item label="视频类型" required prop="videoType">
            <el-radio-group v-model="androidForm.videoType">
              <el-radio :value="0">RTSP</el-radio>
              <el-radio :disabled="true" :value="1">声网</el-radio>
            </el-radio-group>
          </el-form-item>
          <br />
          <el-form-item label="铲端类型" required prop="excavatorType">
            <el-radio-group v-model="androidForm.excavatorType" @change.self="excavatorTypeChange">
              <el-radio v-for="item in VEHICLE_TYPE_LIST()" :key="item.value" :value="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="border-l-2 border-solid border-primary text-xl pl-3 mb-5">MQTT 设置</div>
          <el-form-item label="连接地址" required prop="mqttHost">
            <el-input v-model="androidForm.mqttHost" />
          </el-form-item>
          <el-form-item label="ClientID" required prop="mqttClientID">
            <el-input v-model="androidForm.mqttClientID" />
          </el-form-item>
          <el-form-item label="用户名" required prop="mqttName">
            <el-input v-model="androidForm.mqttName" />
          </el-form-item>
          <el-form-item label="密码" required prop="mqttPassword">
            <el-input v-model="androidForm.mqttPassword" />
          </el-form-item>

          <div class="border-l-2 border-solid border-primary text-xl pl-3 mb-5">MQTT Topic</div>
          <el-form-item label="铲端->Android" required prop="mqttToAndroid">
            <el-input v-model="androidForm.mqttToAndroid" />
          </el-form-item>
          <el-form-item label="Android->铲端" required prop="mqttToExcavator">
            <el-input v-model="androidForm.mqttToExcavator" />
          </el-form-item>
          <el-form-item label="远程上电->Android" :required="androidForm.needPowerOn" prop="mqttToAndroidPowerON">
            <el-input v-model="androidForm.mqttToAndroidPowerON" />
          </el-form-item>
          <el-form-item label="Android->远程上电" :required="androidForm.needPowerOn" prop="androidToMqttPowerON">
            <el-input v-model="androidForm.androidToMqttPowerON" />
          </el-form-item>
          <el-form-item label="心跳" required prop="mqttKeepLive">
            <el-input v-model="androidForm.mqttKeepLive" />
          </el-form-item>
          <el-form-item label="第三方->Android(数据信息)">
            <el-input v-model="androidForm.mqttToAndroidTripartite" placeholder="现有天津水泥院配置" />
          </el-form-item>
          <el-form-item label="第三方->Android(按钮状态)">
            <el-input v-model="androidForm.mqttToAndroidTripartiteEx001" placeholder="现有天津水泥院配置" />
          </el-form-item>
          <el-form-item label="Android->Ros(工作时长)">
            <el-input v-model="androidForm.mqttToTripartite" placeholder="现有天津水泥院配置" />
          </el-form-item>

          <el-form-item v-if="androidForm.excavatorType == 4" label="营口港发送窗口">
            <el-input v-model="androidForm.paramsSetting" />
          </el-form-item>

          <div class="border-l-2 border-solid border-primary text-xl pl-3 mb-5">上电设置</div>
          <el-form-item label="远程启动" @click.prevent="">
            <el-switch v-model="androidForm.needPowerOn" />
          </el-form-item>
          <el-form-item label="泥人上电" @click.prevent="">
            <el-switch v-model="androidForm.niRenDTO" />
          </el-form-item>
          <br />
          <el-form-item label="允许上电" @click.prevent="">
            <el-switch v-model="androidForm.allowPowerOn" />
          </el-form-item>
          <el-form-item label="允许下电" @click.prevent="">
            <el-switch v-model="androidForm.allowPowerOff" />
          </el-form-item>
          <el-form-item label="强制下电" @click.prevent="">
            <el-switch v-model="androidForm.showPowerOffSwitch" />
          </el-form-item>
          <el-form-item label="需要上电状态查询" @click.prevent="">
            <el-switch v-model="androidForm.powerQuery" />
          </el-form-item>

          <div class="border-l-2 border-solid border-primary text-xl pl-3 mb-5">数据展示</div>
          <el-form-item label="展示燃油油位" @click.prevent="">
            <el-switch v-model="androidForm.showOilLevel" />
          </el-form-item>
          <el-form-item label="展示发动机转速" @click.prevent="">
            <el-switch v-model="androidForm.showEngineSpeed" />
          </el-form-item>
          <el-form-item label="展示水温" @click.prevent="">
            <el-switch v-model="androidForm.showWaterTemp" />
          </el-form-item>
          <el-form-item label="展示电池电压" @click.prevent="">
            <el-switch v-model="androidForm.showBatteryVoltage" />
          </el-form-item>
          <el-form-item label="展示机油压力" @click.prevent="">
            <el-switch v-model="androidForm.showOilPressure" />
          </el-form-item>
          <el-form-item label="展示液压油温度" @click.prevent="">
            <el-switch v-model="androidForm.showHydraulicOilTemp" />
          </el-form-item>
          <el-form-item label="展示瞬时油耗" @click.prevent="">
            <el-switch v-model="androidForm.showOilConsumption" />
          </el-form-item>

          <el-form-item v-if="androidForm.excavatorType == 4" label="展示转向灯" @click.prevent="">
            <el-switch v-model="androidForm.showTurnSignal" />
          </el-form-item>

          <div class="border-l-2 border-solid border-primary text-xl pl-3 mb-5">手柄设置</div>

          <el-form-item label="展示手柄指示器" @click.prevent="">
            <el-switch v-model="androidForm.showRockerView" />
          </el-form-item>

          <el-form-item label="XY轴切换(左手柄)" @click.prevent="">
            <el-switch v-model="androidForm.leftHandleReversal" />
          </el-form-item>

          <el-form-item label="模拟脚踏板" @click.prevent="">
            <el-switch v-model="androidForm.simulatePedal" />
          </el-form-item>

          <div class="border-l-2 border-solid border-primary text-xl pl-3 mb-5">功能设置</div>

          <el-form-item v-if="androidForm.excavatorType == 2" label="自动启动" @click.prevent="">
            <el-switch v-model="androidForm.selfStart" />
          </el-form-item>

          <el-form-item label="调试模式" @click.prevent="">
            <el-switch v-model="androidForm.debugBool" />
          </el-form-item>
          <el-form-item label="支持内外网切换" @click.prevent="">
            <el-switch v-model="androidForm.changeInsideNetwork" />
          </el-form-item>

          <el-form-item label="异步监控" @click.prevent="">
            <el-switch v-model="androidForm.isAsynchronous" />
          </el-form-item>

          <el-form-item label="支持混流设置" @click.prevent="">
            <el-switch v-model="androidForm.mixedFlowSetting" />
          </el-form-item>

          <el-form-item v-if="androidForm.excavatorType == 4" label="支持双闪灯" @click.prevent="">
            <el-switch v-model="androidForm.doubleFlashTurnLamp" />
          </el-form-item>

          <el-form-item v-if="androidForm.excavatorType == 4" label="调焦" @click.prevent="">
            <el-switch v-model="androidForm.haveHaiKang" />
          </el-form-item>
          <el-form-item v-if="androidForm.excavatorType == 4" label="发送窗口参数" @click.prevent="">
            <el-switch v-model="androidForm.videoParamsSetting" />
          </el-form-item>
          <el-form-item v-if="androidForm.excavatorType == 4" label="使用左手柄当方向盘" @click.prevent="">
            <el-switch v-model="androidForm.usesTheLeftHandle" />
          </el-form-item>

          <el-form-item label="发送效率" @click.prevent="">
            <el-switch v-model="androidForm.sendExtendParam" />
          </el-form-item>

          <el-form-item label="发送点火最低转速">
            <el-input-number
              v-model="androidForm.noIgnitionMinSpeedValue"
              :min="0"
              :max="9999"
              size="small"
              controls-position="right"
            />
          </el-form-item>

          <div class="border-l-2 border-solid border-primary text-xl pl-3 mb-5">传感器设置</div>

          <el-form-item label="姿态仪不透明度">
            <el-input-number
              v-model="androidForm.balancerViewTrans"
              :min="0"
              :max="100"
              size="small"
              controls-position="right"
            />
          </el-form-item>

          <br />

          <el-form-item label="倾斜报警" @click.prevent="">
            <el-switch v-model="androidForm.isTiltAlarm" @change="tiltAlarmChange" />
          </el-form-item>
          <el-form-item v-if="androidForm.isTiltAlarm" label="报警提示音" @click.prevent="">
            <el-switch v-model="androidForm.isTiltAlarmVoice" :disabled="!androidForm.isTiltAlarm" />
          </el-form-item>
          <!-- <el-form-item label="支持双倾角设置" @click.prevent="">
            <el-switch v-model="androidForm.doubleDipAngle" />
          </el-form-item> -->
          <br />
          <el-form-item v-if="androidForm.isTiltAlarm" label="方案列表" @click.prevent="">
            <div class="flex flex-col">
              <div class="mb-2" v-for="(item, index) in androidForm.tiltAlarmPlatform">
                <span>方案名称:{{ item.name }}</span>
                <el-switch class="mx-2" v-model="item.isUse" @change="(val:any) => tiltPlanChange(val, index)" />
                <span class="inline-block mx-2 w-[126px]">倾斜报警值(X轴) {{ item.warmXValue }}</span>
                <div class="inline-block w-[180px]">
                  <el-slider v-model="item.warmXValue" :min="item.minXValue" :max="item.maxXValue" />
                </div>
                <!-- <el-input-number
                  v-model="item.warmXValue"
                  :min="item.minXValue"
                  :max="item.maxXValue"
                  size="small"
                  controls-position="right"
                /> -->
                <span class="inline-block mx-2 w-[126px]">倾斜报警值(Y轴) {{ item.warmYValue }}</span>
                <div class="inline-block w-[180px]">
                  <el-slider v-model="item.warmYValue" :min="item.minYValue" :max="item.maxYValue" />
                </div>
              </div>
            </div>
          </el-form-item>
          <div class="border-l-2 border-solid border-primary text-xl pl-3 mb-5">智能化功能设置</div>
          <el-form-item label="智能化功能总开关" @click.prevent="">
            <el-switch v-model="androidForm.intelligent.have_intelligent" @change="intelligentChange" />
          </el-form-item>
          <br />
          <el-form-item label="铲斗景深落点" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              :active-value="1"
              :inactive-value="0"
              v-model="androidForm.intelligent.bucket_landing_point.switch"
            />
          </el-form-item>
          <el-form-item label="颜色选择" @click.prevent="">
            <el-color-picker v-model="androidForm.intelligent.bucket_landing_point.color" />
          </el-form-item>
          <el-form-item label="透明度" @click.prevent="">
            <el-input-number
              class="w-28"
              v-model="androidForm.intelligent.bucket_landing_point.transparency"
              :min="0"
              :max="100"
            />
          </el-form-item>
          <el-form-item label="形态" @click.prevent="">
            <el-input-number
              class="w-28"
              v-model="androidForm.intelligent.bucket_landing_point.form"
              :min="0"
              :max="100"
            />
          </el-form-item>
          <br />
          <el-form-item label="斗齿识别" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              :active-value="1"
              :inactive-value="0"
              v-model="androidForm.intelligent.bucket_tooth.switch"
            />
          </el-form-item>
          <el-form-item label="颜色选择" @click.prevent="">
            <el-color-picker v-model="androidForm.intelligent.bucket_tooth.color" />
          </el-form-item>
          <el-form-item label="透明度" @click.prevent="">
            <el-input-number
              class="w-28"
              v-model="androidForm.intelligent.bucket_tooth.transparency"
              :min="0"
              :max="100"
            />
          </el-form-item>
          <el-form-item label="形态" @click.prevent="">
            <el-input-number class="w-28" v-model="androidForm.intelligent.bucket_tooth.form" :min="0" :max="100" />
          </el-form-item>
          <br />
          <el-form-item label="地面起伏网格" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              :active-value="1"
              :inactive-value="0"
              v-model="androidForm.intelligent.ground_level.switch"
            />
          </el-form-item>
          <el-form-item label="颜色选择" @click.prevent="">
            <el-color-picker v-model="androidForm.intelligent.ground_level.color" />
          </el-form-item>
          <el-form-item label="透明度" @click.prevent="">
            <el-input-number
              class="w-28"
              v-model="androidForm.intelligent.ground_level.transparency"
              :min="0"
              :max="100"
            />
          </el-form-item>
          <el-form-item label="形态" @click.prevent="">
            <el-input-number class="w-28" v-model="androidForm.intelligent.ground_level.form" :min="0" :max="100" />
          </el-form-item>
          <br />
          <el-form-item label="行人识别" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              :active-value="1"
              :inactive-value="0"
              v-model="androidForm.intelligent.has_person_flag.switch"
            />
          </el-form-item>
          <el-form-item label="颜色选择" @click.prevent="">
            <el-color-picker v-model="androidForm.intelligent.has_person_flag.color" />
          </el-form-item>
          <el-form-item label="透明度" @click.prevent="">
            <el-input-number
              class="w-28"
              v-model="androidForm.intelligent.has_person_flag.transparency"
              :min="0"
              :max="100"
            />
          </el-form-item>
          <el-form-item label="形态" @click.prevent="">
            <el-input-number class="w-28" v-model="androidForm.intelligent.has_person_flag.form" :min="0" :max="100" />
          </el-form-item>
          <br />
          <el-form-item label="侧剖面雷达" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              :active-value="1"
              :inactive-value="0"
              v-model="androidForm.intelligent.side_profile_radar.switch"
            />
          </el-form-item>
          <el-form-item label="颜色选择" @click.prevent="">
            <el-color-picker v-model="androidForm.intelligent.side_profile_radar.color" />
          </el-form-item>
          <el-form-item label="透明度" @click.prevent="">
            <el-input-number
              class="w-28"
              v-model="androidForm.intelligent.side_profile_radar.transparency"
              :min="0"
              :max="100"
            />
          </el-form-item>
          <el-form-item label="形态" @click.prevent="">
            <el-select class="!w-28" v-model="androidForm.intelligent.side_profile_radar.form">
              <el-option label="全部显示" :value="1"></el-option>
              <el-option label="显示姿态感知" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <br />
          <el-form-item label="展示铲斗景深落点" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              v-model="androidForm.intelligent.have_bucket_landing_point"
            />
          </el-form-item>
          <el-form-item label="展示斗齿识别" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              v-model="androidForm.intelligent.have_bucket_tooth"
            />
          </el-form-item>
          <el-form-item label="展示地面起伏网格" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              v-model="androidForm.intelligent.have_ground_level"
            />
          </el-form-item>
          <el-form-item label="展示侧剖面雷达" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              v-model="androidForm.intelligent.have_side_profile_radar"
            />
          </el-form-item>
          <el-form-item label="预测的回转停止位置">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              v-model="androidForm.showPredictedTurningStopPositionSwitch"
            />
          </el-form-item>
          <el-form-item label="卡车车帮高度提示" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              v-model="androidForm.showTruckSideHeightSwitch"
            />
          </el-form-item>
          <el-form-item label="环视视角(仅支持模版)" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              v-model="androidForm.showLookAroundSwitch"
            />
          </el-form-item>
          <el-form-item label="地形坡面图-车辆倾斜状态" @click.prevent="">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              v-model="androidForm.showTopographicProfileSwitch"
            />
          </el-form-item>
          <el-form-item label="展示一键复位" @click.prevent="">
            <el-switch :disabled="!androidForm.intelligent.have_intelligent" v-model="androidForm.showAuto" />
          </el-form-item>
          <el-form-item label="是否一键复位" @click.prevent="">
            <el-switch :disabled="!androidForm.intelligent.have_intelligent" v-model="androidForm.haveAuto" />
          </el-form-item>
          <br />
          <el-form-item label="展示距离报警功能">
            <el-switch
              :disabled="!androidForm.intelligent.have_intelligent"
              v-model="androidForm.showRadarWarn"
              @change="radarChange"
            />
          </el-form-item>
          <el-form-item label="雷达距离报警" @click.prevent="">
            <el-switch :disabled="!androidForm.showRadarWarn" v-model="androidForm.isRadarAlarm" />
          </el-form-item>
          <el-form-item label="雷达距离报警提示声音" @click.prevent="">
            <el-switch :disabled="!androidForm.showRadarWarn" v-model="androidForm.isRadarAlarmVoice" />
          </el-form-item>

          <br />
          <!-- <el-form-item label="雷达数量">
            <el-input-number
              v-model="androidForm.radarNum"
              :max="4"
              :disabled="!androidForm.showRadarWarn"
              size="small"
              controls-position="right"
            />
          </el-form-item> -->

          <el-form-item label="雷达距离(红色值)">
            <el-input-number
              v-model="androidForm.radarRedValue"
              :disabled="!androidForm.showRadarWarn"
              :min="0"
              :max="androidForm.radarYellowValue - 2"
              size="small"
              controls-position="right"
            />
          </el-form-item>
          <el-form-item label="雷达距离(黄色值)">
            <el-input-number
              v-model="androidForm.radarYellowValue"
              :disabled="!androidForm.showRadarWarn"
              :min="androidForm.radarRedValue + 2"
              :max="androidForm.radarGreenValue - 2"
              size="small"
              controls-position="right"
            />
          </el-form-item>
          <el-form-item label="雷达距离(绿色值)">
            <el-input-number
              v-model="androidForm.radarGreenValue"
              :disabled="!androidForm.showRadarWarn"
              :min="androidForm.radarYellowValue + 2"
              :max="20"
              size="small"
              controls-position="right"
            />
          </el-form-item>

          <div class="border-l-2 border-solid border-primary text-xl pl-3 mb-5">特殊功能</div>
          <el-form-item label="使用两个Android" @click.prevent="">
            <el-switch v-model="androidForm.hasAntherAndroid" />
          </el-form-item>
          <el-form-item v-if="androidForm.hasAntherAndroid" label="副安卓的IP">
            <el-input v-model="androidForm.anotherAndroidIP" />
          </el-form-item>
        </el-form>
      </el-scrollbar>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import feedback from "@/utils/feedback";
import type { FormInstance } from "element-plus";
import { ArrowLeft, RefreshRight, VideoPause, VideoPlay, Setting, Plus } from "@element-plus/icons-vue";
import router from "@/router";
import { ANDROID_RESTART_PARAMS } from "@/utils/constants";
import { vehiclesDetail, vehicleUpdate } from "@/api/device";
import { VEHICLE_TYPE_LIST } from "@/utils/constants";

const formRef = shallowRef<FormInstance>();

const query = useRoute().query;
const carId = query?.id as string;
const vehicleDetail = reactive({
  vehicle_name: "",
  vehicle_type: "",
  description: "",
  restart_time: 0,
  android: {},
  handleConfig: {},
});

const androidForm: any = reactive({
  /* 以下重要 不需要重启*/
  projectName: "", // 项目名称
  modelName: "", // 设备名称
  hasAntherAndroid: false, // 操作台是否使用两个Android=--两台3399// 操作台是否使用两个Android=--两台3399
  /* 以下重要 需要重启 */
  easyPlayUrl1: "", // 播放流地址  三竖屏的视频流
  easyPlayUrl6: "", // 音频流地址 三竖屏的音频流
  mqttClientID: "", // mqtt连接的ClientID，一个连接唯一一个值，多个连接用一个值只有一个连上，其他会被挤掉
  mqttHost: "", // mqtt 连接地址
  mqttName: "", // mqtt 连接的用户名
  mqttPassword: "", // mqtt 连接的密码
  mqttToAndroid: "", // 铲端给Android正常数据的 mqtt topic名
  mqttToExcavator: "", // 安卓发送给铲端数据的 mqtt topic名
  mqttToAndroidPowerON: "", // 远程上电模块发送给安卓状态的 mqtt topic名
  androidToMqttPowerON: "", // 安卓发送给服务远程上电的 mqtt topic名
  useArduino: true, // 是否使用STM32版子
  niRenDTO: true, // 是否泥人上电
  excavatorType: 1, //铲端类型 1:挖掘机 2:电铲 太重 3:装载机 瓮福 4:装载机 营口、沈阳院、厦工 5:钻机 泰业 6:徐工700吨
  needPowerOn: true, // 是否需要上电
  powerQuery: false, //是否需要上电状态查询
  // doubleDipAngle: false, // 是否支持双倾角设置
  doubleFlashTurnLamp: false, // 是否支持双闪灯

  /* 以下不重要 需要重启 */
  useUSBHandler: false, // 是否使用USB手柄
  mqttToAndroidTripartite: "", // 第三方给Android展示数据的 mqtt topic名 目前天津水泥院使用
  mqttToAndroidTripartiteEx001: "", // 第三方给Android展示数据的 mqtt topic名 目前天津水泥院使用  --按钮状态
  mqttKeepLive: "", // mqtt保持心跳的 mqtt topic名
  mqttToTripartite: "", // 安卓发送给ros工作时长的mqtt topic名
  simulatePedal: false, //是否有模拟脚踏板 --日本有台设备有
  haveHaiKang: false, //是否有海康摄像头需要Android调焦
  videoParamsSetting: false, // 营口港是否发送左视右视窗口参数
  allowPowerOff: true, //允许下电（厦工） 默认为true
  allowPowerOn: true, //允许上电
  showPowerOffSwitch: false, // 展示上下电开关
  usesTheLeftHandle: false, // 使用左手柄当方向盘
  videoType: 0, // 视频样式默认为0, 0:rtsp流 1:声网rtsp
  innerNetUrl: "", // 内网服务器地址，视频流推到哪的地址
  innerPingUrl: "", // 内网ping地址
  innerNetMqttHost: "", // 内网mqtt地址

  /* 以下不重要 不需要重启 */
  intelligent: {
    bucket_landing_point: {
      color: "#ff0017",
      form: 1,
      switch: 1,
      transparency: 57,
    },
    bucket_tooth: {
      color: "#fefe3e",
      form: 0,
      switch: 0,
      transparency: 0,
    },
    ground_level: {
      color: "#00fffe",
      form: 0,
      switch: 1,
      transparency: 0,
    },
    has_person_flag: {
      color: "#fefe3e",
      form: 0,
      switch: 1,
      transparency: 0,
    },
    side_profile_radar: {
      color: "#fefe3e",
      form: 1,
      switch: 1,
      transparency: 100,
    },
    have_bucket_landing_point: true,
    have_bucket_tooth: true,
    have_ground_level: true,
    have_intelligent: true,
    have_side_profile_radar: true,
  },
  noIgnitionMinSpeedValue: 600, // 不发点火最低转速值
  loginPage: "", // 登录页面名--暂时不用
  paramsSetting: "", // 营口港发送左视右视窗口的 mqtt topic名
  ntpUrl: "", // ntp校时地址，暂时未使用
  anotherAndroidIP: "", // 另外一台Android的IP
  showOilLevel: true, //是否燃油油位
  showEngineSpeed: false, //展示发动机转速
  showWaterTemp: false, // 是否水温 (冷却液温度)
  showBatteryVoltage: false, // 是否电池电压
  showOilPressure: false, //是否展示机油压力
  showHydraulicOilTemp: false, //是否展示液压油温度
  showOilConsumption: false, // 是否展示瞬时油耗
  showBucketTooth: false, //是否展示斗齿数
  showTurnSignal: true, //是否展示展示转向灯
  showAuto: false, //是否展示展示一键复位
  haveAuto: false, //是否展示展示一键复位
  selfStart: false, // 是否自动启动
  mixedFlowSetting: false, // 支持混流设置 打开网页设置
  vehicleId: "", // 通一参数平台token
  miwenId: "", // 通一参数平台设备ID
  changeInsideNetwork: false, // 支持内外网切换
  sendExtendParam: false, // 发送拓展参数 目前包含遥控效率
  debugBool: false, // 是否是调试模式--暂时不用
  showRadarWarn: false, // 距离报警开关
  isTiltAlarmVoice: false, // 倾斜提示音
  easyPlayUrl2: "",
  easyPlayUrl3: "",
  easyPlayUrl4: "",
  easyPlayUrl5: "",
  easyPlayUrl7: "",
  easyPlayUrl8: "",
  easyPlayUrl9: "",
  easyPlayUrl13: "",
  easyPlayUrl14: "",
  easyPlayUrl15: "",
  easyPlayUrl16: "",
  easyPlayUrl10: "",
  easyPlayUrl11: "",
  easyPlayUrl12: "",

  // 新增传感器
  // haveIntelligent: false, //否是有智能化功能
  showBucketGroundPointSwitch: false, // 是否显示铲斗地面落点开关
  showGroundReliefGridSwitch: false, // 是否显示地面起伏网络开关
  showTopographicProfileSwitch: false, // 是否显示地形坡面图-车辆倾斜状态开关
  showPredictedTurningStopPositionSwitch: false, // 是否显示预测的回转停止位置开关
  showTruckSideHeightSwitch: false, // 是否显示卡车车帮高度提示开关
  showLookAroundSwitch: false, // 是否显示环视开关
  showRockerView: false, //是否展示手柄指示器
  isAsynchronous: false, //是否是异步监控--不操作只看视频与数据
  leftHandleReversal: false, //是否左手柄XY轴切换
  isTiltAlarm: false, //是否倾斜报警
  isRadarAlarm: false, //是否雷达距离报警
  isRadarAlarmVoice: false, //雷达距离报警提示声音
  balancerViewTrans: 100, //姿态仪不透明度
  radarNum: 3, //雷达数量
  radarRedValue: 3, //雷达距离红色值 最小值是0，必须比黄色值小于等于2
  radarYellowValue: 6, //雷达距离黄色值  必须比绿色色值小于等于2， 比绿色值大于等于2
  radarGreenValue: 10, //雷达距离绿色值 值不能超过20  比黄色色值大于等于2
  // 倾斜报警设置
  tiltAlarmPlatform: [
    {
      name: "A",
      minXValue: 5,
      maxXValue: 45,
      minYValue: 5,
      maxYValue: 45,
      warmXValue: 14,
      warmYValue: 14,
      isUse: true,
    },
    {
      name: "B",
      minXValue: 5,
      maxXValue: 45,
      minYValue: 5,
      maxYValue: 45,
      warmXValue: 14,
      warmYValue: 14,
      isUse: false,
    },
  ],
});

// const formRules = reactive({
//   easyPlayUrl1: [{ required prop="": true, message: "请输入", trigger: ["blur"] }],
//   easyPlayUrl6: [{ required prop="": true, message: "请输入", trigger: ["blur"] }],
// });

const powerParams = ["niRenDTO", "allowPowerOn", "allowPowerOff", "showPowerOffSwitch", "powerQuery"];

const intelligentParams = [
  "showBucketGroundPointSwitch",
  "showGroundReliefGridSwitch",
  "showTopographicProfileSwitch",
  "showPredictedTurningStopPositionSwitch",
  "showTruckSideHeightSwitch",
  "showLookAroundSwitch",
  "showRadarWarn",
  "isRadarAlarm",
  "isRadarAlarmVoice",
  "showBucketTooth",
  "showAuto",
  "haveAuto",
];

const radarParams = ["showRadarWarn", "isRadarAlarm", "isRadarAlarmVoice"];

let androidBackup: any = {};

/**
 * @description 铲端类型联动 如果是挖掘机需要上电默认为开
 * @param { Number } val
 */
const excavatorTypeChange = (val: any) => {
  if (val === 1) {
    androidForm.needPowerOn = true;
  } else {
    androidForm.needPowerOn = false;
  }
  if (val !== 2) {
    androidForm.selfStart = false;
  }
};

// /**
//  * @description 使用STM32版子和使用USB手柄互斥
//  * @param  { Boolean } val
//  * @param { String } key
//  */
// const controlTypeChange = (val: any, key: string) => {
//   if (key === "useArduino") {
//     androidForm.useUSBHandler = !val;
//   } else if (key === "useUSBHandler") {
//     androidForm.useArduino = !val;
//   }
// };

/**
 * @description 倾斜报警方案互斥
 */
const powerChange = (val: any) => {
  if (val === false) {
    for (let i = 0; i < powerParams.length; i++) {
      const key = powerParams[i];
      androidForm[key] = false;
    }
  }
};

/**
 * @description 倾斜报警方案互斥
 * @param  { Boolean } val
 */
const tiltPlanChange = (val: boolean, index: number) => {
  androidForm.tiltAlarmPlatform.forEach((item: any, i: number) => {
    if (i === index) {
      item.isUse = val;
    } else {
      item.isUse = !val;
    }
  });
};

/**
 * @description 倾斜报警
 * @param  { Boolean } val
 */
const tiltAlarmChange = (val: any) => {
  if (val === false) androidForm.isTiltAlarmVoice = false;
};

/**
 * @description 智能功能开关，如果关，则所有智能化功能关
 */
const intelligentChange = (val: any) => {
  if (val === false) {
    for (let i = 0; i < intelligentParams.length; i++) {
      const key = intelligentParams[i];
      androidForm[key] = false;
    }
  }
};

/**
 * @description 防碰撞功能开关，如果关，则所有防碰撞功能关
 */
const radarChange = (val: any) => {
  if (val === false) {
    for (let i = 0; i < radarParams.length; i++) {
      const key = radarParams[i];
      androidForm[key] = false;
    }
  }
};

const checkChange = () => {
  let isChange = false;
  for (let i = 0; i < ANDROID_RESTART_PARAMS.length; i++) {
    const key = ANDROID_RESTART_PARAMS[i];
    if (androidForm[key] !== androidBackup[key]) {
      isChange = true;
      break;
    }
  }
  return isChange;
};

const handleSubmit = async () => {
  await formRef.value?.validate(async (val, errorParams: any) => {
    if (val) {
      let params = {
        id_: carId,
        android: androidForm,
        handleConfig: vehicleDetail.handleConfig,
        vehicle_name: vehicleDetail.vehicle_name,
        vehicle_type: vehicleDetail.vehicle_type,
        description: vehicleDetail.description,
        restart_time: checkChange() ? Math.floor(Date.now() / 1000) : vehicleDetail.restart_time,
      };
      await vehicleUpdate(params);
      feedback.msgSuccess("操作成功");
      goBack();
    } else {
      return formRef.value?.scrollToField(Object.keys(errorParams)[0]);
    }
  });
};

const goBack = () => {
  router.push({ path: "/device" });
};

const getVehiclesDetail = async () => {
  const res = await vehiclesDetail(carId);
  Object.assign(vehicleDetail, res);
  Object.assign(androidForm, vehicleDetail.android);
  androidBackup = JSON.parse(JSON.stringify(androidForm));
};

onMounted(() => {
  getVehiclesDetail();
});

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px 20px;
}
.el-input {
  width: 280px;
}
</style>
