import asyncio
import datetime
from bson import ObjectId
from apps.db.mongo_db import MongoUtil

async def generate_data():
    """生成并插入模拟数据"""
    db = MongoUtil.get_db()
    collection = db["prod_count_log"]

    await collection.delete_many({})
    print("已清空 prod_count_log 集合")
    
    start_time = datetime.datetime.now() - datetime.timedelta(days=7)
    vehicle_id = ObjectId("66f135aa8ea96a77201f53e9")
    
    teams = [
        {"group_name": "班组A", "driver_name": "司机A"},
        {"group_name": "班组B", "driver_name": "司机B"},
        {"group_name": "班组C", "driver_name": "司机C"}
    ]
    
    documents = []
    total_records_to_generate = 500
    records_per_shift = int(8 * 60 / 40)  # 8 hours, 4 minutes per record
    num_shifts = total_records_to_generate // records_per_shift

    current_time = start_time
    for shift_index in range(num_shifts):
        team = teams[shift_index % 3]
        for _ in range(records_per_shift):
            doc = {
                "timestamp": int(current_time.timestamp() * 1000),
                "vehicle_id": vehicle_id,
                "vehicle_name": "车辆001",
                "group_name": team["group_name"],
                "driver_name": team["driver_name"],
                "receive_timestamp" : current_time,
                "mode": "simple",
                "event": "finish"
            }
            documents.append(doc)
            current_time += datetime.timedelta(minutes=40)
    
    if documents:
        await collection.insert_many(documents)
    print(f"已成功插入 {len(documents)} 条模拟数据")

async def main():
    await generate_data()
    MongoUtil.close()

if __name__ == "__main__":
    asyncio.run(main())