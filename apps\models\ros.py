from enum import Enum
from typing import Optional

from pydantic import BaseModel


class Action(str, Enum):
    """程序节点动作"""

    START = "start"
    STOP = "stop"
    RESTART = "restart"
    DELETE = "delete"


class State(int, Enum):
    """程序节点状态"""

    DISABLED = 0  # 禁用
    ENABLE = 1  # 启动


class NodeState(BaseModel):
    """程序节点状态模型"""

    auto_start: Optional[State] = State.ENABLE


class NodeCrontrol(BaseModel):
    """节点控制，启停"""

    node_name: str  # 节点名称
    action: Action  # 控制动作 start, stop


class Node(NodeState):
    """ROS 节点模型"""

    node_name: str
    package_name: str
    node_type: str
    auto_start: Optional[State] = State.ENABLE
    params: Optional[dict] = {}


class ControlNode(BaseModel):
    """控制 ROS 节点"""

    action: str
    node_name: str
    type: str


class UpdateParams(BaseModel):
    """ROS 节点更新参数模型"""

    node_name: str
    auto_start: int = 0
    params: dict
