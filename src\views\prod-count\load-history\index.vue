<!-- 历史装车记录 -->
<template>
  <div class="load-history">
    <el-card id="search" class="!border-none" shadow="never">
      <el-form class="ls-form" :model="formData" inline>
        <el-form-item label="车辆ID">
          <el-input
            class="w-[180px]"
            placeholder="请输入"
            v-model="formData.vehicle_id"
            clearable
          />
        </el-form-item>

        <el-form-item label="记录时间">
          <daterange-picker
            style="width: 300px"
            v-model:startTime="startTime"
            v-model:endTime="endTime"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="resetPageFormat">查询</el-button>
          <el-button @click="resetParams">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="!border-none mt-4" shadow="never" v-loading="pager.loading">
      <div>
        <el-table :height="tableHeight" :data="pager.lists" size="large">
          <el-table-column label="车辆ID" prop="vehicle_id" />
          <el-table-column label="车辆名称" prop="vehicle_name" />
          <el-table-column label="司机名称" prop="driver_name" />
          <el-table-column label="班组名称" prop="group_name" />
          <el-table-column label="事件类型" prop="event">
            <template #default="{ row }">
              <el-tag :type="eventTypeMap[row.event].type">{{
                eventTypeMap[row.event].label
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="timestamp">
            <template #default="{ row }">
              {{ dayjs(row.timestamp).local().format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="loadHistory">
import { getHistoryLoad } from "@/api/prod-count";
import { usePaging } from "@/hooks/usePaging";
import { formatDate2ISO } from "@/utils/util";
import dayjs from "dayjs";

// 查询表单
const formData = reactive({
  vehicle_id: "",
  start_time: "",
  end_time: "",
});

// 事件类型 arrive / start / bucket / finish
const eventTypeMap: any = {
  arrive: {
    type: "info",
    label: "到达",
  },
  start: {
    type: "warning",
    label: "开始",
  },
  bucket: {
    type: "primary",
    label: "斗数+1",
  },
  finish: {
    type: "success",
    label: "完成装车",
  },
};

const startTime = ref("");
const endTime = ref("");
const tableHeight = ref(0);

const calcTableHeight = () => {
  const searchHeight = document.getElementById("search")?.clientHeight || 0;
  tableHeight.value = window.innerHeight - searchHeight - 50 - 64 - 48 - 16;
};

onMounted(() => {
  calcTableHeight();
  window.addEventListener("resize", calcTableHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", calcTableHeight);
});

const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: getHistoryLoad,
  params: formData,
});

const resetPageFormat = () => {
  if (startTime.value) {
    formData.start_time = formatDate2ISO(startTime.value);
    formData.end_time = formatDate2ISO(endTime.value);
  }
  resetPage();
};

getLists();
</script>

<style lang="scss" scoped></style>
