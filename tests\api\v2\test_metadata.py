"""
元数据管理相关接口测试
"""

import json
import faker
from starlette.testclient import TestClient


def build_metadata() -> dict:
    """构建元数据"""
    fake = faker.Faker("zh_CN")
    key = f"test_key_{fake.word()}".upper()
    return {"key": key, "value": fake.sentence(), "desc": f"测试描述 {fake.sentence()}", "type": "string"}


def assert_response_success(res):
    """断言响应成功"""
    res_json = res.json()
    assert res_json["code"] == 1000
    return res_json


def test_metadata_crud(client: TestClient, admin_header, mdb):
    """测试元数据增删改查"""
    # 创建元数据
    metadata = build_metadata()
    res = client.post("/metadata", json=metadata, headers=admin_header)
    res_json = assert_response_success(res)

    # 查询元数据列表
    res = client.get("/metadata", headers=admin_header)
    res_json = assert_response_success(res)
    meta_list = res_json["data"]
    is_found = False
    for meta in meta_list["lists"]:
        if meta["key"] == metadata["key"]:
            is_found = True
            assert meta["value"] == metadata["value"]
            assert meta["desc"] == metadata["desc"]
            break
    assert is_found

    # 更新元数据
    fake = faker.Faker("zh_CN")
    new_value = fake.sentence()
    new_description = f"更新的描述 {fake.sentence()}"
    update_data = metadata.copy()
    update_data["value"] = new_value
    update_data["desc"] = new_description
    res = client.put(f"/metadata/{metadata['key']}", content=json.dumps(update_data), headers=admin_header)
    assert_response_success(res)

    # 验证更新成功
    res = client.get("/metadata", headers=admin_header)
    res_json = assert_response_success(res)
    meta_list = res_json["data"]
    is_updated = False
    for meta in meta_list["lists"]:
        if meta["key"] == metadata["key"]:
            is_updated = True
            assert meta["value"] == new_value
            assert meta["desc"] == new_description
            break
    assert is_updated

    # 删除元数据
    res = client.delete(f"/metadata/{metadata['key']}", headers=admin_header)
    assert_response_success(res)

    # 验证删除成功
    res = client.get("/metadata", headers=admin_header)
    res_json = assert_response_success(res)
    meta_list = res_json["data"]
    is_deleted = True
    for meta in meta_list["lists"]:
        if meta["key"] == metadata["key"]:
            is_deleted = False
            break
    assert is_deleted
