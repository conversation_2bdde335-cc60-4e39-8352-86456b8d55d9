<template>
  <el-date-picker
    v-model="content"
    :type="type"
    range-separator="-"
    :format="format"
    :value-format="valueFormat"
    start-placeholder="开始时间"
    end-placeholder="结束时间"
    clearable
  ></el-date-picker>
</template>

<script lang="ts" setup>
import { computed } from "vue";

const props = defineProps({
  startTime: {
    type: String,
    default: "",
  },
  endTime: {
    type: String,
    default: "",
  },
  type: {
    type: String as () => 'year' | 'years' | 'month' | 'months' | 'date' | 'dates' | 'datetime' | 'week' | 'datetimerange' | 'daterange' | 'monthrange' | 'yearrange',
    default: "datetimerange",
  },
  format: {
    type: String,
    default: "YYYY-MM-DD HH:mm:ss",
  },
  valueFormat: {
    type: String,
    default: "YYYY-MM-DD HH:mm:ss",
  }

});
const emit = defineEmits(["update:startTime", "update:endTime"]);

const content = computed<any>({
  get: () => {
    return [props.startTime, props.endTime];
  },
  set: (value: Event | any) => {
    if (value === null) {
      emit("update:startTime", "");
      emit("update:endTime", "");
    } else {
      emit("update:startTime", value[0]);
      emit("update:endTime", value[1]);
    }
  },
});
</script>
