<template>
  <div class="edit-popup">
    <popup
      ref="popupRef"
      :title="$t('common.确认连接')"
      :async="true"
      width="550px"
      @confirm="handleSubmit"
      @close="handleClose"
    >
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item :label="$t('common.操作台选择')" prop="id">
          <el-select v-model="formData.id" filterable :placeholder="$t('common.请选择操作台')">
            <el-option v-for="item in vehicle_list" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-button link type="primary" size="small" @click="handleAdd">{{ $t("common.未找到对应操作台") }}</el-button>
    </popup>
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>
<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import { consolesList } from "@/api/android";
import { unRegisterDeviceUpdate } from "@/api/android";

import feedback from "@/utils/feedback";
import EditPopup from "@/views/android/edit.vue";

const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();

const formData = reactive({
  id: "",
  device_id: "",
});

const formRules = reactive({
  id: [
    {
      required: true,
      message: "请选择操作台",
      trigger: ["blur"],
    },
  ],
});

const vehicle_list: any = ref([]);
const editRef = shallowRef<InstanceType<typeof EditPopup>>();
const showEdit = ref(false);

const handleAdd = async () => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("add");
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  await unRegisterDeviceUpdate(formData);
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = () => {
  popupRef.value?.open();
  getLists();
};

const getLists = async () => {
  const { lists } = await consolesList({ page_no: 1, page_size: 100 });
  vehicle_list.value = lists;
};

const setFormData = async (row: any) => {
  for (const key in formData) {
    if (row[key] != null && row[key] != undefined) {
      //@ts-ignore
      formData[key] = row[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
