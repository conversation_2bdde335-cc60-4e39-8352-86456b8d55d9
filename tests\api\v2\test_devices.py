"""
设备管理相关接口测试
"""

from starlette.testclient import TestClient


def assert_response_success(res):
    """断言响应成功"""
    res_json = res.json()
    assert res_json["code"] == 1000
    return res_json


def test_unregister_device_list(client: TestClient, admin_header):
    """测试获取未注册设备列表"""
    # 测试获取未注册车辆列表
    res = client.get("/devices/vehicles/un_register_list", headers=admin_header)
    assert_response_success(res)
    # 只验证接口能正常返回，不验证具体内容，因为未注册设备可能不存在

    # 测试获取未注册操作台列表
    res = client.get("/devices/op_consoles/un_register_list", headers=admin_header)
    assert_response_success(res)
    # 只验证接口能正常返回，不验证具体内容，因为未注册设备可能不存在
