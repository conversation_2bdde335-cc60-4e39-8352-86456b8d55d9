<template>
  <div class="edit-popup">
    <el-drawer v-model="drawerShow" title="操作台参数" :size="650">
      <div class="h-full flex flex-col">
        <JsonEditorVue
          v-if="drawerShow"
          class="w-[580px] ml-4"
          style="height: calc(100% - 50px)"
          v-model="params"
          mode="text"
          :mainMenuBar="false"
          :onChange="onJsonSave"
        />
        <div class="flex flex-grow justify-end items-end mt-4 h-8">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
          <el-button @click="handleClose">取消</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import { vehiclesDetail, vehicleUpdate } from "@/api/device";

import feedback from "@/utils/feedback";
import JsonEditorVue from "json-editor-vue";

const props = defineProps({
  carId: {
    type: String,
    require: true,
    default: "",
  },
});

const vehicleDetail: any = reactive({
  id: "",
  android: {},
  handleConfig: {},
  vehicle_name: "",
  vehicle_type: "",
  restart_time: 0,
  description: "",
});

const emit = defineEmits(["success", "close"]);
const drawerShow = ref(false);
const params: any = ref({});

const onJsonSave = (value: any) => {
  vehicleDetail.handleConfig = JSON.parse(value.text);
};

const handleSubmit = async () => {
  if (typeof vehicleDetail.handleConfig !== "object") {
    vehicleDetail.handleConfig = JSON.parse(vehicleDetail.handleConfig);
  }

  let param = {
    id_: props.carId,
    android: vehicleDetail.android,
    handleConfig: vehicleDetail.handleConfig,
    vehicle_name: vehicleDetail.vehicle_name,
    vehicle_type: vehicleDetail.vehicle_type,
    description: vehicleDetail.description,
    restart_time: vehicleDetail.restart_time,
  };
  await vehicleUpdate(param);
  feedback.msgSuccess("操作成功");
  handleClose();
};

const open = () => {
  drawerShow.value = true;
  getVehiclesDetail();
};

const handleClose = () => {
  params.value = {};
  drawerShow.value = false;
};

const getVehiclesDetail = async () => {
  const res = await vehiclesDetail(props.carId);
  Object.assign(params.value, res.handleConfig);
  Object.assign(vehicleDetail, res);
};

defineExpose({
  open,
});
</script>
