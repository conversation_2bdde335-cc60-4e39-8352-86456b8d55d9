import logging
from typing import Dict, Any

from pydantic import ValidationError
from fastapi import FastAP<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import J<PERSON><PERSON>esponse
from starlette.exceptions import HTTPException as StarletteHTTPException
from redis.exceptions import ConnectionError as RedisConnectionError

from .app_exc import AppException
from .http_base import HttpCode, HttpResp
from .depends import ApiVersion, Multilingual


__all__ = ["configure_exception"]

logger = logging.getLogger(__name__)


def gen_fail_resp(req: Request, resp_code: HttpCode, data: Any = None) -> Dict[str, Any]:
    # 根据版本和语言特性生成响应内容
    try:
        req.state.api_version
    except AttributeError:
        req.state.api_version = ApiVersion.get(req)
    try:
        req.state.accept_language
    except AttributeError:
        req.state.accept_language = Multilingual.get(req)

    content = resp_code.content(req.state.api_version, req.state.accept_language)

    if data is not None:
        content["data"] = data

    try:
        # 根据变量风格特性重构响应内容
        style_ = req.headers.get("X-Variable-Naming-Style", "snake_case")
        assert style_ == "CamelCase"
        new_content = {}
        new_content["Code"] = content["code"]
        new_content["Msg"] = content["msg"]
        new_content["Data"] = content["data"]
        content = new_content
    except AssertionError:
        pass

    req.state.track_info = content
    return content


def configure_exception(app: FastAPI):
    """配置全局异常处理"""

    @app.exception_handler(RequestValidationError)
    async def request_validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
        """处理请求参数验证的异常
        code: 310 311
        """
        resp_code = HttpResp.PARAM_INVALID
        errs = exc.errors()
        if errs and errs[0].get("type", "").startswith("type_error."):
            resp_code = HttpResp.PARAM_TYPE_ERROR
        logger.warning("validation_exception_handler: url=[%s], errs=[%s]", request.url.path, errs)

        resp_content = gen_fail_resp(request, resp_code, errs[0])
        return JSONResponse(
            content=resp_content,
            media_type="application/json;charset=utf-8",
        )

    @app.exception_handler(ValidationError)
    async def validation_exception_handler(req: Request, exc: ValidationError) -> JSONResponse:
        """处理参数验证的异常 (除请求参数验证之外的)
        code: 500
        """
        resp_code = HttpResp.PARAM_INVALID
        errs = exc.errors()
        if errs and errs[0].get("type", "").startswith("type_error."):
            resp_code = HttpResp.PARAM_TYPE_ERROR
        logger.warning("validation_exception_handler: url=[%s], errs=[%s]", req.url.path, errs)

        resp_content = gen_fail_resp(req, resp_code, errs[0])
        return JSONResponse(
            content=resp_content,
            media_type="application/json;charset=utf-8",
        )

    @app.exception_handler(StarletteHTTPException)
    async def http_exception_handler(req: Request, exc: StarletteHTTPException) -> JSONResponse:
        """处理客户端请求异常
        code: 312 404
        """
        msg_ = f"http_exception_handler: url=[{req.url.path}], status_code=[{exc.status_code}]"
        logger.warning(msg_)

        resp_code = HttpResp.SYSTEM_ERROR
        if exc.status_code == 404:
            resp_code = HttpResp.RESOURCE_NOT_FOUND
        elif exc.status_code == 405:
            resp_code = HttpResp.REQUEST_METHOD_ERROR
        elif exc.status_code == 335:
            resp_code = HttpResp.PROGRAM_NODE_EXISTS
        elif exc.status_code == 334:
            resp_code = HttpResp.DEVICE_ID_EXISTS

        resp_content = gen_fail_resp(req, resp_code)
        return JSONResponse(content=resp_content)

    @app.exception_handler(AssertionError)
    async def assert_exception_handler(req: Request, exc: AssertionError) -> JSONResponse:
        """处理断言异常
        code: 313
        """
        errs = ",".join(exc.args) if exc.args else HttpResp.ASSERT_ARGUMENT_ERROR.msg
        logger.warning("app_exception_handler: url=[%s], errs=[%s]", req.url.path, errs)

        res_content = gen_fail_resp(req, HttpResp.ASSERT_ARGUMENT_ERROR)
        return JSONResponse(content=res_content)

    @app.exception_handler(AppException)
    async def app_exception_handler(req: Request, exc: AppException) -> JSONResponse:
        """处理自定义异常
        code: .
        """
        if exc.echo_exc:
            logger.error("app_exception_handler: url=[%s]", req.url.path)
            logger.error(exc, exc_info=True)

        resp_content = gen_fail_resp(req, exc.exc_code)
        return JSONResponse(
            content=resp_content,
            media_type="application/json;charset=utf-8",
        )

    @app.exception_handler(RedisConnectionError)
    async def db_redis_error_handler(req: Request, exc: RedisConnectionError) -> JSONResponse:
        """处理连接异常
        code: 500
        """
        logger.error("db_opr_error_handler: url=[%s]", req.url.path)
        logger.error(exc, exc_info=True)

        resp_content = gen_fail_resp(req, HttpResp.SYSTEM_ERROR)
        return JSONResponse(
            content=resp_content,
            media_type="application/json;charset=utf-8",
        )

    @app.exception_handler(Exception)
    async def global_exception_handler(req: Request, exc: Exception) -> JSONResponse:
        """处理服务端异常, 全局异常处理
        code: 500
        """
        logger.error("global_exception_handler: url=[%s]", req.url.path)
        logger.error(exc, exc_info=True)

        resp_content = gen_fail_resp(req, HttpResp.UNKNOWN_ERROR)
        return JSONResponse(
            content=resp_content,
            media_type="application/json;charset=utf-8",
        )
