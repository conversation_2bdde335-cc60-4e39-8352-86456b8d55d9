<template>
  <el-scrollbar :height="scrollHeight">
    <div class="gmsl-wrap">
      <div
        class="gmsl-item flex flex-col items-center justify-center relative"
        v-for="(item, index) in canList"
        :key="index"
      >
        <Icon
          v-if="index === canList.length - 1 && canList.length > 1"
          class="absolute right-2 top-2 w-6 cursor-pointer"
          :size="18"
          name="el-icon-Delete"
          @click="handleDeleteCan(item.id)"
        />
        <div class="r-8 leading-8 mb-2">
          {{ item.id }}
        </div>
        <el-form label-width="auto">
          <el-form-item :label="$t('vehicle.比特率')">
            <el-input-number v-model="item.bitrate" :placeholder="$t('vehicle.请输入比特率')" />
          </el-form-item>
          <el-form-item :label="$t('vehicle.拓展命令')">
            <el-input type="textarea" :rows="2" v-model="item.ext_fd" :placeholder="$t('vehicle.请输入(选填)')" />
          </el-form-item>
          <el-form-item :label="$t('vehicle.是否开启')">
            <el-switch v-model="item.is_enable" />
          </el-form-item>
        </el-form>
      </div>
      <div
        v-if="canList.length < 4"
        class="gmsl-item flex flex-col items-center justify-center cursor-pointer"
        @click="addCanItem"
      >
        {{ $t("vehicle.添加") }}
      </div>
      <div class="w-full flex justify-end mt-4">
        <el-button type="primary" @click="handleSave"> {{ $t("vehicle.保存") }} </el-button>
      </div>
    </div>
  </el-scrollbar>
</template>
<script lang="ts" setup camera_name="gmsl">
import feedback from "@/utils/feedback";

import { canListApi, canEdit } from "@/api/device";

const props = defineProps<{
  vehicleId: string;
}>();

const scrollHeight = computed(() => {
  return document.documentElement.clientHeight - 360;
});

const emit = defineEmits(["getDetail"]);

const handleSave = async () => {
  const flag = checkObj();
  if (!flag) return;
  await canEdit({
    vehicle_id: props.vehicleId,
    can_list: canList.value,
  });
  emit("getDetail");
  feedback.msgSuccess("保存成功");
};

const checkObj = () => {
  let flag = true;
  canList.value.forEach((item) => {
    if (!item.name && !item.bitrate) {
      feedback.msgError(`${item.id} 名称或比特率不能为空`);
      flag = false;
    }
  });
  return flag;
};

const canList = ref([
  {
    id: "CAN0",
    name: "",
    bitrate: 500000,
    ext_fd: "",
    is_enable: false,
  },
]);

const getCameraList = async () => {
  const { lists: cList } = await canListApi({
    vehicle_id: props.vehicleId,
  });
  if (cList.length > 0) canList.value = cList;
};

const addCanItem = () => {
  canList.value.push({
    id: `CAN${canList.value.length}`,
    name: "",
    bitrate: 500000,
    ext_fd: "",
    is_enable: false,
  });
};

const handleDeleteCan = (id: string) => {
  const flag = canList.value.findIndex((item: any) => item.id == id);
  if (flag !== -1) canList.value.splice(flag, 1);
};

onMounted(() => {
  getCameraList();
});
</script>

<style lang="scss" scoped>
.gmsl-wrap {
  display: flex;
  flex-wrap: wrap;
  .gmsl-item {
    flex-basis: 25%;
    padding: 4px;
    background-color: #fcfcfc;
    border: 1px solid #e7e7e7;
  }
}
</style>
