from contextlib import asynccontextmanager

from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware

from apps.utils.wsbroadcast import WBroadCast
from apps.config import get_settings
from apps.common import configure_exception, AuditMiddleware
from apps.router import configure_router
from apps.db import RedisDB, MongoDB, MongoUtil
from apps.services.internal import InitDataBase


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期 开始&关闭"""
    app.state.rdb = RedisDB
    app.state.mdb = MongoDB
    # init_lock = FileLock(f"/tmp/init_db_lock_{get_settings().mongo_db_name}")
    # if init_lock.acquire():
    #     await MongoUtil.init_index()
    #     await InitDataBase().init_data()
    #     await MongoUtil.revised_data()
    await WBroadCast.connect()
    yield
    await RedisDB.close()
    await WBroadCast.disconnect()
    MongoUtil.close()
    # init_lock.release()


def configure_static(app: FastAPI):
    """配置静态资源"""
    pass


def configure_cors(app: FastAPI):
    """配置跨域"""
    if get_settings().mode == "prod":
        return
    origins = [
        "http://localhost",
        "http://localhost:3000",
        "http://localhost:3001",
    ]
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )


def create_app() -> FastAPI:
    """创建FastAPI后台应用,并初始化"""
    app = FastAPI(lifespan=lifespan)
    app.add_middleware(AuditMiddleware)
    configure_exception(app)
    configure_cors(app)
    configure_router(app)
    configure_static(app)
    return app
