<template>
  <el-dropdown 
    class="notification-dropdown h-full cursor-pointer flex items-center px-2" 
    @command="handleCommand"
    trigger="hover"
    placement="bottom-end"
  >
    <div class="flex items-center">
      <el-badge 
        :value="unreadCount" 
        :hidden="unreadCount === 0" 
        class="item"
      >
        <icon name="el-icon-Bell" :size="16" />
      </el-badge>
    </div>
    
    <template #dropdown>
      <el-dropdown-menu class="notification-menu">
        <div class="notification-header">
          <span class="text-sm font-medium">通知消息</span>
          <span class="text-xs text-gray-500">({{ unreadCount }}条未读)</span>
        </div>
        
        <el-divider class="my-2" />
        
        <div class="notification-list" v-if="notifications.length > 0">
          <div 
            v-for="notification in notifications.slice(0, 5)" 
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.read }"
          >
            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">{{ formatTime(notification.time) }}</div>
            </div>
            <div v-if="!notification.read" class="unread-dot"></div>
          </div>
        </div>
        
        <div v-else class="notification-empty">
          <el-empty description="暂无消息" :image-size="60" />
        </div>
        
        <el-divider class="my-2" />
        
        <div class="notification-footer">
          <el-button 
            type="primary" 
            link 
            size="small" 
            @click="handleCommand('more')"
          >
            更多消息
          </el-button>
          <el-button 
            type="primary" 
            link 
            size="small" 
            @click="handleCommand('markAllRead')"
            :disabled="unreadCount === 0"
          >
            一键已读
          </el-button>
        </div>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'

interface NotificationItem {
  id: string
  title: string
  message: string
  time: string
  read: boolean
  type?: 'info' | 'warning' | 'error' | 'success'
}

const router = useRouter()

// 模拟通知数据
const notifications = ref<NotificationItem[]>([
  {
    id: '1',
    title: '系统通知',
    message: '车辆001离线，请及时检查',
    time: '2024-06-23 10:30:00',
    read: false,
    type: 'warning'
  },
  {
    id: '2',
    title: '数据同步',
    message: '车辆002数据同步完成',
    time: '2024-06-23 09:15:00',
    read: false,
    type: 'success'
  },
  {
    id: '3',
    title: '设备告警',
    message: '操作台003连接异常',
    time: '2024-06-23 08:45:00',
    read: true,
    type: 'error'
  }
])

// 计算未读消息数量
const unreadCount = computed(() => {
  return notifications.value.filter(item => !item.read).length
})

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'more':
      // 跳转到事件列表页面
      try {
        router.push('/notification/events')
      } catch (error) {
        console.warn('无法跳转到事件列表页面，请检查路由配置')
      }
      break
    case 'markAllRead':
      markAllAsRead()
      break
  }
}

// 标记所有消息为已读
const markAllAsRead = () => {
  notifications.value.forEach(item => {
    item.read = true
  })
}

// 模拟接收新消息
const simulateNewMessage = () => {
  const newMessage: NotificationItem = {
    id: Date.now().toString(),
    title: '新消息',
    message: `模拟消息 - ${dayjs().format('HH:mm:ss')}`,
    time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    read: false,
    type: 'info'
  }
  notifications.value.unshift(newMessage)
  
  // 保持最多10条消息
  if (notifications.value.length > 10) {
    notifications.value = notifications.value.slice(0, 10)
  }
}

// 模拟定时接收消息（仅用于演示）
let messageInterval: NodeJS.Timeout | null = null

onMounted(() => {
  // 每30秒模拟接收一条新消息（仅用于演示）
  messageInterval = setInterval(simulateNewMessage, 30000)
})

onUnmounted(() => {
  if (messageInterval) {
    clearInterval(messageInterval)
  }
})
</script>

<style lang="scss" scoped>
.notification-dropdown {
  &:hover {
    background-color: var(--el-color-primary-light-9);
  }
}

.notification-menu {
  width: 320px;
  max-height: 400px;
  
  .notification-header {
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .notification-list {
    max-height: 250px;
    overflow-y: auto;
  }
  
  .notification-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    position: relative;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
    
    &.unread {
      background-color: var(--el-color-info-light-9);
    }
    
    &:last-child {
      border-bottom: none;
    }
    
    .notification-content {
      .notification-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        margin-bottom: 4px;
      }
      
      .notification-message {
        font-size: 12px;
        color: var(--el-text-color-regular);
        margin-bottom: 4px;
        line-height: 1.4;
      }
      
      .notification-time {
        font-size: 11px;
        color: var(--el-text-color-placeholder);
      }
    }
    
    .unread-dot {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 6px;
      height: 6px;
      background-color: var(--el-color-primary);
      border-radius: 50%;
    }
  }
  
  .notification-empty {
    padding: 20px;
    text-align: center;
  }
  
  .notification-footer {
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--el-bg-color-page);
  }
}
</style>
