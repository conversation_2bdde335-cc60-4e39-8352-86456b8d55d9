<template>
  <div class="post-lists">
    <el-card class="!border-none" shadow="never">
      <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" :inline="true">
        <el-form-item :label="$t('vehicle.操作台名称')">
          <el-input class="w-[280px]" v-model="queryParams.name" clearable @keyup.enter="resetPage" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="resetPage">{{ $t("vehicle.查询") }}</el-button>
          <el-button @click="resetParams">{{ $t("vehicle.重置") }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="!border-none mt-4 table-card" shadow="never">
      <div>
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          {{ $t("vehicle.添加操作台") }}
        </el-button>
        <el-button @click="handleJump">{{ $t("vehicle.未连接操作台") }} </el-button>
      </div>
      <div>
        <el-table
          class="mt-4 table-warp"
          size="large"
          :height="calcTableHeight()"
          v-loading="pager.loading"
          :data="pager.lists"
          table-layout="auto"
        >
          <el-table-column type="index" width="50" />
          <el-table-column align="center" :label="$t('vehicle.操作台名称')" prop="name" min-width="100" />
          <el-table-column align="center" :label="$t('vehicle.可操作车辆类型')" prop="vehicle_type" min-width="100">
            <template #default="{ row }">
              <el-tag class="mx-1" size="normal" v-if="row.vehicle_type.length" v-for="item in row.vehicle_type">{{
                pageData.vehicleTypeList[item]
              }}</el-tag>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column align="center" :label="$t('vehicle.操作')" fixed="right">
            <template #default="{ row }">
              <div class="flex flex-row justify-center items-center">
                <el-button class="margin-left0" type="primary" link @click="handleEdit(row)">
                  {{ $t("vehicle.编辑") }}
                </el-button>
                <el-button class="margin-left0" type="danger" link @click="handleDelete(row.id)">
                  {{ $t("vehicle.删除") }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-end mt-4">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </div>
    </el-card>

    <EditPopup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>
<script lang="ts" setup name="post">
import { consoleDelete, consolesList } from "@/api/android";
import useMetadataStore from "@/stores/modules/metadata";
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import EditPopup from "./edit.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const router = useRouter();
const metadataStore = useMetadataStore();

const editRef = shallowRef<InstanceType<typeof EditPopup>>();
const showEdit = ref(false);

const queryParams = reactive({
  name: "",
  vehicle_type: "",
});

const { pager, getLists, resetPage, resetParams } = usePaging({
  fetchFun: consolesList,
  params: queryParams,
});

const pageData: any = reactive({
  vehicleTypeList: {},
});

onMounted(() => {
  getLists();
  getVehicleTypeList();
});

const getVehicleTypeList = async () => {
  pageData.vehicleTypeList = await metadataStore.fetchMetadata("VEHICLE_TYPE", "kv");
};

const calcTableHeight = () => {
  return window.innerHeight - 308;
};

const handleJump = async () => {
  router.push({ path: "/link/android" });
};

const handleAdd = async () => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("add");
};

const handleEdit = async (row: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(row);
};

const handleDelete = async (id: string) => {
  await feedback.confirm(t("vehicle.确定要删除"));
  await consoleDelete(id);
  feedback.msgSuccess(t("vehicle.删除成功"));
  getLists();
};
</script>
<style lang="scss" scoped>
.margin-left0 {
  margin-left: 0 !important;
}
.text_hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
</style>
