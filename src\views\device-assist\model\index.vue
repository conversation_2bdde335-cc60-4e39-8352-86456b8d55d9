<template>
  <div class="model-lists">
    <el-card class="!border-none" shadow="never">
      <div>
        <el-button type="primary" @click="handleAdd()">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          新增
        </el-button>
        <el-button @click="handleExpand"> 展开/折叠 </el-button>
      </div>
      <el-table
        v-loading="loading"
        ref="tableRef"
        class="mt-4"
        size="large"
        :data="lists"
        row-key="id"
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="型号" prop="name" min-width="180" show-overflow-tooltip />
        <!-- <template #default="{ row: { name, model } }">
            <div>{{ name }}{{ model ? `(${model})` : "" }}</div>
          </template>
        </el-table-column> -->
        <el-table-column label="更新时间" prop="updateTime" min-width="100"></el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button v-if="!row.pid" type="primary" link @click="handleAdd(row.id)"> 新增 </el-button>
            <el-button v-if="row.pid" type="primary" link @click="handleEdit(row)"> 编辑 </el-button>
            <el-button v-if="row.pid" type="danger" link @click="handleDelete(row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>
<script lang="ts" setup name="model">
import { deviceModelLists, deviceModelDelete } from "@/api/device";
import type { ElTable } from "element-plus";
import EditPopup from "./edit-model.vue";
import feedback from "@/utils/feedback";
const tableRef = shallowRef<InstanceType<typeof ElTable>>();
const editRef = shallowRef<InstanceType<typeof EditPopup>>();
let isExpand = true;
const loading = ref(false);
const showEdit = ref(false);
const lists = ref([]);

const getLists = async () => {
  loading.value = true;
  try {
    const data = await deviceModelLists();
    lists.value = data;
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};

const handleAdd = async (id?: number) => {
  showEdit.value = true;
  await nextTick();
  if (id) {
    editRef.value?.setFormData({
      pid: Number(id),
    });
  }
  editRef.value?.open("add");
};

const handleEdit = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.getDetail(data);
};

const handleDelete = async (id: number) => {
  await feedback.confirm("确定要删除？");
  await deviceModelDelete({ id });
  feedback.msgSuccess("删除成功");
  getLists();
};

const handleExpand = () => {
  isExpand = !isExpand;
  toggleExpand(lists.value, isExpand);
};

const toggleExpand = (children: any[], unfold = true) => {
  for (const key in children) {
    tableRef.value?.toggleRowExpansion(children[key], unfold);
    if (children[key].children) {
      toggleExpand(children[key].children!, unfold);
    }
  }
};

getLists();
</script>
