"""
审计日志相关接口测试
"""

from starlette.testclient import TestClient


def assert_response_success(res):
    """断言响应成功"""
    assert res.status_code == 200
    res_json = res.json()
    assert res_json["code"] == 1000
    return res_json


def test_audit_list(client: TestClient, admin_header):
    """测试获取审计日志列表"""
    # 基本查询
    res = client.get("/audit", headers=admin_header, params={"page": 1, "page_size": 10})
    res_json = assert_response_success(res)
    assert "lists" in res_json["data"]
    assert "count" in res_json["data"]

    # 按时间范围查询
    params = {"page": 1, "page_size": 10, "start_time": "2023-01-01 00:00:00", "end_time": "2023-12-31 23:59:59"}
    res = client.get("/audit", headers=admin_header, params=params)
    assert_response_success(res)

    # 按用户查询
    params = {"page": 1, "page_size": 10, "username": "admin"}
    res = client.get("/audit", headers=admin_header, params=params)
    assert_response_success(res)

    # 按操作类型查询
    params = {"page": 1, "page_size": 10, "operation": "login"}
    res = client.get("/audit", headers=admin_header, params=params)
    assert_response_success(res)

    # 按IP地址查询
    params = {"page": 1, "page_size": 10, "ip": "127.0.0.1"}
    res = client.get("/audit", headers=admin_header, params=params)
    assert_response_success(res)
