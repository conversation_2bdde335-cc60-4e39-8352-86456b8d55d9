<template>
  <div class="role-lists">
    <el-card class="!border-none" shadow="never">
      <div>
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          新增
        </el-button>
      </div>
      <div class="mt-4">
        <div>
          <el-table :data="pager.lists" size="large" v-loading="pager.loading">
            <!-- <el-table-column prop="id" label="ID" min-width="100" /> -->
            <el-table-column prop="name" label="名称" min-width="150" />
            <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
            <el-table-column prop="sort" label="排序" min-width="100" />
            <el-table-column prop="create_time" label="创建时间" min-width="180">
              <template #default="{ row }">
                {{ dayjs(row.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="340" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleAuth(row)"> 菜单权限 </el-button>
                <el-button link type="primary" @click="handleApiPermission(row)"> API权限 </el-button>
                <el-button link type="primary" @click="handleResourcePermission(row)"> 资源权限 </el-button>
                <el-button link type="primary" @click="handleEdit(row)"> 编辑 </el-button>
                <el-button link type="danger" @click="handleDelete(row.id)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="flex justify-end mt-4">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </div>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
    <auth-popup v-if="showAuth" ref="authRef" @success="getLists" @close="showAuth = false" />

    <popup
      ref="apiPermissionRef"
      title="API权限"
      :async="true"
      width="550px"
      @confirm="handleApiPermissionSubmit"
      @close="handlePermissionClose"
    >
      <el-table
        ref="multipleTableRef"
        row-key="id"
        height="40vh"
        @selection-change="handleSelectionChange"
        :data="pageData.func_list"
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID"> </el-table-column>
        <el-table-column prop="name" label="名称"> </el-table-column>
      </el-table>
    </popup>

    <popup
      ref="resourcePermissionRef"
      title="资源权限"
      :async="true"
      width="550px"
      @confirm="handleResourcePermissionSubmit"
      @close="handlePermissionClose"
    >
      <div class="mb-2 text-xl">车辆</div>
      <el-select v-model="pageData.select_vehicle" placeholder="请选择" multiple clearable>
        <el-option v-for="item in pageData.vehicle_list" :key="item.id" :label="item.vehicle_name" :value="item.id" />
      </el-select>
      <div class="my-2 text-xl">操作台</div>
      <el-select v-model="pageData.select_console" placeholder="请选择" multiple clearable>
        <el-option v-for="item in pageData.console_list" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <div class="mb-2 text-xl">角色选择</div>
      <el-select v-model="pageData.select_role" placeholder="请选择" clearable>
        <el-option v-for="item in pageData.role_flag" :key="item.id" :label="item.name" :value="item.id"> </el-option>
      </el-select>
    </popup>
  </div>
</template>

<script lang="ts" setup name="role">
import {
  roleLists,
  roleDelete,
  apiPermissionsLists,
  setApiPermissions,
  setResourcePermissions,
} from "@/api/perms/role";
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import EditPopup from "./edit.vue";
import AuthPopup from "./auth.vue";
import Popup from "@/components/popup/index.vue";
import { vehiclesList } from "@/api/device";
import { consolesList } from "@/api/android";
import type { TableInstance } from "element-plus";

const multipleTableRef = ref<TableInstance>();
const editRef = shallowRef<InstanceType<typeof EditPopup>>();
const authRef = shallowRef<InstanceType<typeof AuthPopup>>();
const apiPermissionRef = shallowRef<InstanceType<typeof Popup>>();
const resourcePermissionRef = shallowRef<InstanceType<typeof Popup>>();

const showEdit = ref(false);
const showAuth = ref(false);

const pageData: any = reactive({
  func_list: [],
  role_flag: [],
  vehicle_list: [],
  console_list: [],
  select_role: "",
  select_api_permission: [],
  cur_role_id: "",
  select_vehicle: [],
  select_console: [],
});
const { pager, getLists } = usePaging({
  fetchFun: roleLists,
});

onMounted(async () => {
  const { apis, resources_flag } = await apiPermissionsLists();
  pageData.func_list = apis;
  pageData.role_flag = resources_flag;
});

const handleAdd = async () => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("add");
};

const handleEdit = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(data);
};

const handleAuth = async (data: any) => {
  showAuth.value = true;
  await nextTick();
  authRef.value?.open();
  authRef.value?.setFormData(data);
};

const handleApiPermission = async (row: any) => {
  apiPermissionRef.value?.open();
  pageData.cur_role_id = row.id;
  pageData.select_api_permission = row.apis || [];

  const selectData = pageData.func_list.filter((item: any) => pageData.select_api_permission.includes(item.id));
  await nextTick();
  multipleTableRef.value!.clearSelection();
  selectData.forEach((item: any) => {
    multipleTableRef.value!.toggleRowSelection(item, true);
  });
};

const handleResourcePermission = async (row: any) => {
  resourcePermissionRef.value?.open();
  pageData.cur_role_id = row.id;

  pageData.select_role = row.resource_flag;
  pageData.select_vehicle = row.vehicles || [];
  pageData.select_console = row.opconsoles || [];

  const { lists: vehicle_list } = await vehiclesList({ page_no: 1, page_size: 99 });
  const { lists: console_list } = await consolesList({ page_no: 1, page_size: 99 });

  pageData.vehicle_list = vehicle_list;
  pageData.console_list = console_list;
};

const handleSelectionChange = (val: any) => {
  pageData.select_api_permission = val.map((item: any) => item.id);
};

const handleApiPermissionSubmit = async () => {
  const params = {
    id: pageData.cur_role_id,
    apis: pageData.select_api_permission,
  };
  await setApiPermissions(params);
  handlePermissionClose();
  apiPermissionRef.value?.close();
  feedback.msgSuccess("操作成功");
  getLists();
};

const handleResourcePermissionSubmit = async () => {
  const params = {
    id: pageData.cur_role_id,
    opconsoles: pageData.select_console,
    vehicles: pageData.select_vehicle,
    resource_flag: pageData.select_role,
  };
  await setResourcePermissions(params);
  handlePermissionClose();
  resourcePermissionRef.value?.close();
  feedback.msgSuccess("操作成功");
  getLists();
};

const handlePermissionClose = () => {
  pageData.select_api_permission = [];
  pageData.cur_role_id = "";
  pageData.select_role = "";
};

// 删除角色
const handleDelete = async (id: string) => {
  await feedback.confirm("确定要删除？");
  await roleDelete(id);
  feedback.msgSuccess("删除成功");
  getLists();
};

getLists();
</script>
