from bson import ObjectId

from apps.db.redis_db import <PERSON><PERSON>ache, Hash<PERSON>ache, SetCache, ListCache, SortSetCache


class MenuMaxId(StringCache):
    """菜单最大ID"""

    def __init__(self) -> None:
        self.key_name = "menu:max_id"


class UserInfo(StringCache):
    """用户缓存信息"""

    def __init__(self, user_id: str | ObjectId):
        self.key_name = f"user:info:{user_id}"


class UserToken(StringCache):
    """用户TOKEN缓存"""

    def __init__(self, token_value: str):
        self.key_name = f"user:token:{token_value}"


class ResetPassToken(StringCache):
    """用户TOKEN缓存"""

    def __init__(self, token_value: str):
        self.key_name = f"user:reset_pass_token:{token_value}"


class TriPartyToken(StringCache):
    """三方 token 验证信息"""

    def __init__(self, token_value: str):
        self.key_name = f"tparty:token:{token_value}"


class OPCDevice:
    """操作台设备状态"""

    un_register_set = SortSetCache("op_console:device:un_register")

    def __init__(self, id_: str | ObjectId):
        self.id: str = f"{id_}"
        self.status = HashCache(f"op_console:device:{id_}:status")
        self.unbound = StringCache(f"op_console:device:{id_}:unbound")


class OPC:
    """安卓设备相关的 redis key"""

    def __init__(self, id_: str | ObjectId):
        self.id: str = f"{id_}"
        self.prefix = f"op_console:{id_}"
        self.online_status = StringCache(f"{self.prefix}:status:online_status")
        self.locked_vehicles = SetCache(f"{self.prefix}:locked_vehicles")


class VehicleDevice:
    """米文设备相关的"""

    un_register_set = SortSetCache("vehicle:device:un_register")

    def __init__(self, id_: str | ObjectId):
        self.id: str = f"{id_}"
        self.status = HashCache(f"vehicle:device:{id_}:status")


class Vehicle:
    """车辆相关缓存数据"""

    def __init__(self, id_: str | ObjectId):
        self.id: str = f"{id_}"
        self.prefix = f"vehicle:{id_}"
        self.metadata = HashCache(f"{self.prefix}:metadata")
        self.locked_op_console = StringCache(f"{self.prefix}:locked_op_console")
        self.status = VehicleStatus(self.id)


class VehicleStatus:
    """车辆状态相关缓存数据"""

    def __init__(self, id_: str | ObjectId):
        self.id: str = f"{id_}"
        self.prefix = f"vehicle:{id_}:status"
        self.online_status = StringCache(f"{self.prefix}:online_status")
        self.params_sync_status = StringCache(f"{self.prefix}:params_sync_status")
        self.params_sync_detail = StringCache(f"{self.prefix}:params_sync_detail")
        self.online_time = ListCache(f"{self.prefix}:online_time")
        self.offline_time = ListCache(f"{self.prefix}:offline_time")


class VehicleTianJinVolvoCloud(StringCache):
    """天津沃尔沃云车辆相关缓存数据"""

    def __init__(self, id_: str | ObjectId):
        self.key_name = f"vehicle:{id_}:volvo_status"
