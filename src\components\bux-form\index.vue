<template>
  <div>
    <el-form
      :model="formModel"
      :rules="formConfig.rules"
      :inline="formConfig.inline"
      :label-position="formConfig.labelPosition"
      :label-width="formConfig.labelWidth"
    >
      <template v-for="item in widgetList">
        <div
          v-if="item.type == 'title' && item.display"
          class="border-l-2 border-solid border-primary text-xl pl-3 mb-4"
        >
          {{ item.title }}
        </div>

        <br v-if="item.type === 'newline' && item.display" />

        <el-form-item
          v-if="item.type === 'input' && item.display"
          :label="item.title"
          :prop="item.field"
          :required="item?.props?.required"
        >
          <el-input v-model="formModel[item.field]" :placeholder="item.info" />
        </el-form-item>

        <el-form-item
          v-if="item.type === 'inputNumber' && item.display"
          :label="item.title"
          :prop="item.field"
          :required="item?.props?.required"
        >
          <el-input-number
            v-model="formModel[item.field]"
            :max="item?.props?.maxValue"
            :min="item?.props?.minValue"
            size="small"
            controls-position="right"
          />
        </el-form-item>

        <el-form-item
          v-if="item.type === 'switch' && item.display"
          :label="item.title"
          :prop="item.field"
          :required="item?.props?.required"
          @click.prevent=""
        >
          <el-switch v-model="formModel[item.field]" />
        </el-form-item>

        <el-form-item
          v-if="item.type === 'radio' && item.display"
          :label="item.title"
          :prop="item.field"
          :required="item?.props?.required"
        >
          <el-radio-group v-model="formModel[item.field]">
            <el-radio v-for="option in item.options" :value="option.value" :disabled="option.disabled">
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="item.type === 'select' && item.display"
          :label="item.title"
          :prop="item.field"
          :required="item?.props?.required"
        >
          <el-select v-model="formModel[item.field]" :placeholder="item.info">
            <el-option
              v-for="option in item.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="item.type === 'slider' && item.display"
          :label="item.title"
          :prop="item.field"
          :required="item?.props?.required"
        >
          <div class="inline-block w-[180px]">
            <el-slider
              v-model="formModel[item.field]"
              :min="item?.props?.min"
              :max="item?.props?.max"
              :step="item?.props?.step"
            />
          </div>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script lang="ts" setup name="buxForm">
const formModel = reactive<any>({});

const props = defineProps({
  formConfig: {
    type: Object,
    required: false,
    default: () => ({
      rulesName: "rules",
      labelWidth: "auto",
      labelPosition: "left",
      inline: true,
      onFormDataChange: "",
    }),
  },
  widgetList: {
    type: Array<any>,
    required: false,
    default: () => [],
  },
});

const updateDefault = async () => {
  await nextTick();
  props.widgetList.forEach((item) => {
    // 获取item下的props有没有defaultValue属性
    if (item.props && item.props.defaultValue) {
      formModel[item.field] = JSON.parse(item.props.defaultValue).value;
    }
  });
};

onUpdated(() => {
  updateDefault();
});

onMounted(() => {
  updateDefault();
});

defineExpose({
  formModel,
});
</script>

<style lang="scss" scoped>
.el-input {
  width: 280px;
}
</style>
