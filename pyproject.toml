[project]
name = "oms-api"
version = "1.9.1"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiocsv==1.3.2",
    "aiohappyeyeballs==2.4.4",
    "aiohttp==3.11.11",
    "aiosignal==1.3.2",
    "annotated-types==0.7.0",
    "anyio==4.9.0",
    "arrow==1.3.0",
    "attrs==24.3.0",
    "broadcaster==0.3.1",
    "certifi==2025.7.14",
    "click==8.2.1",
    "colorama==0.4.6",
    "dnspython==2.7.0",
    "email-validator==2.2.0",
    "fastapi-cli[standard]==0.0.8",
    "fastapi[standard]==0.116.1",
    "frozenlist==1.5.0",
    "h11==0.16.0",
    "httpcore==1.0.9",
    "httptools==0.6.4",
    "httpx==0.28.1",
    "idna==3.10",
    "influxdb-client==1.49.0",
    "jinja2==3.1.6",
    "markdown-it-py==3.0.0",
    "markupsafe==3.0.2",
    "mdurl==0.1.2",
    "motor==3.7.1",
    "multidict==6.1.0",
    "orjson==3.10.15",
    "propcache==0.2.1",
    "pydantic[email]==2.11.7",
    "pydantic-core==2.33.2",
    "pydantic-settings==2.7.1",
    "pygments==2.19.2",
    "pymongo==4.13.2",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.1.1",
    "python-multipart==0.0.20",
    "pyyaml==6.0.2",
    "reactivex==4.0.4",
    "redis==5.2.1",
    "rich-toolkit==0.14.8",
    "rich==14.0.0",
    "setuptools==80.9.0",
    "shellingham==1.5.4",
    "six==1.17.0",
    "sniffio==1.3.1",
    "starlette==0.47.1",
    "typer==0.16.0",
    "types-python-dateutil==2.9.0.20241206",
    "typing-extensions==4.14.1",
    "urllib3==2.5.0",
    "uvicorn[standard]==0.35.0",
    "uvloop==0.21.0",
    "websockets==15.0.1",
    "yarl==1.18.3",
    "packaging==24.2",
    "pathspec==0.12.1",
    "platformdirs==4.3.6",
    "fastapi-cloud-cli==0.1.4",
    "rignore==0.6.2",
    "sentry-sdk==2.32.0",
    "typing-inspection==0.4.1",
    "watchfiles>=1.1.0",
]


[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true

[tool.ruff]
line-length = 120

[tool.ruff.lint.isort]
length-sort = true

[tool.black]
line-length = 120
target-version = ['py312']
include = '\.pyi?$'
exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[dependency-groups]
dev = [
    "faker>=37.4.0",
    "pytest>=8.4.1",
]
