<template>
  <div class="edit-popup">
    <el-card style="height: calc(100vh - 91px - 16px - 16px)" class="!border-none" shadow="never">
      <el-page-header class="mb-3 mt-2" :icon="ArrowLeft" @back="goBack">
        <template #content>
          <div class="flex items-center">
            <span class="text-large font-600 mr-3"> {{ `${vehicleDetail.vehicle_name}` || "操作台名称" }} </span>
          </div>
        </template>
        <template #extra>
          <div class="flex items-center">
            <el-button v-if="curPlanIndex !== pageData.selectPlanIndex" type="primary" @click="usePlan">
              应用方案{{ pageData.selectPlanIndex + 1 }}
            </el-button>
            <el-button type="primary" @click="handleSubmit">保存并应用</el-button>
          </div>
        </template>
      </el-page-header>
      <el-divider class="!mt-4" />
      <div class="handle-wrap">
        <div class="plan-wrap">
          <el-button
            :class="curPlanIndex === index ? 'plan-selected' : ''"
            :type="pageData.selectPlanIndex === index ? 'primary' : ''"
            v-for="(item, index) in 3"
            @click="changePlan(index)"
          >
            方案{{ index + 1 }}
          </el-button>
        </div>
        <div class="flex flex-row">
          <!-- 挖掘机 -->
          <div
            v-if="pageData.excavatorType == '1'"
            class="group-wrap flex-1 overflow-y-auto"
            style="height: calc(100vh - 91px - 91px - 16px - 16px - 50px)"
          >
            <el-checkbox-group
              class="flex flex-row flex-wrap justify-around items-center h-full"
              v-model="pageData.checkList"
            >
              <div class="group-item w-[280px]">
                <div class="line-one">
                  <div
                    class="item-wrap mx-auto p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'arm_up' }"
                    @click="selectItem('arm_up')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="小臂升" value="arm_up" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值: <span class="text-tx-primary font-bold">{{ curHandleForm.arm_up.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.arm_up.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度: <span class="text-tx-primary font-bold">{{ curHandleForm.arm_up.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
                <div class="line-two my-2 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'swing_left' }"
                    @click="selectItem('swing_left')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="左旋转" value="swing_left" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.swing_left.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.swing_left.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.swing_left.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div class="item-right">
                    <div
                      class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                      :class="{ 'item-selected': pageData.curSelectedKey == 'swing_right' }"
                      @click="selectItem('swing_right')"
                    >
                      <div class="item-name mx-auto">
                        <el-checkbox label="右旋转" value="swing_right" />
                      </div>
                      <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                        <p>
                          最小值:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.swing_right.low_limit }}</span>
                        </p>
                        <p>
                          最大值:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.swing_right.up_limit }}</span>
                        </p>
                        <p>
                          灵敏度:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.swing_right.sensitivity }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="line-three">
                  <div
                    class="item-wrap mx-auto p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'arm_down' }"
                    @click="selectItem('arm_down')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="小臂降" value="arm_down" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值: <span class="text-tx-primary font-bold">{{ curHandleForm.arm_down.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.arm_down.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.arm_down.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="group-item w-[280px]">
                <div class="line-one">
                  <div
                    class="item-wrap mx-auto p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'boom_down' }"
                    @click="selectItem('boom_down')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="大臂降" value="boom_down" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_down.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_down.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.boom_down.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
                <div class="line-two my-2 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'bucket_down' }"
                    @click="selectItem('bucket_down')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="铲斗收" value="bucket_down" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_down.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_down.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_down.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div class="item-right">
                    <div
                      class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                      :class="{ 'item-selected': pageData.curSelectedKey == 'bucket_up' }"
                      @click="selectItem('bucket_up')"
                    >
                      <div class="item-name mx-auto">
                        <el-checkbox label="铲斗开" value="bucket_up" />
                      </div>
                      <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                        <p>
                          最小值:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_up.low_limit }}</span>
                        </p>
                        <p>
                          最大值:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_up.up_limit }}</span>
                        </p>
                        <p>
                          灵敏度:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_up.sensitivity }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="line-three">
                  <div
                    class="item-wrap mx-auto p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'boom_up' }"
                    @click="selectItem('boom_up')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="大臂升" value="boom_up" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_up.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_up.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_up.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="group-item w-[260px] flex flex-col justify-between">
                <div class="line-one my-2 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'track_left_front' }"
                    @click="selectItem('track_left_front')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="左履前" value="track_left_front" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_left_front.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_left_front.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_left_front.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'track_right_front' }"
                    @click="selectItem('track_right_front')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="右履前" value="track_right_front" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_right_front.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_right_front.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_right_front.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>

                <div class="line-two mb-2 mt-20 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'track_left_after' }"
                    @click="selectItem('track_left_after')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="左履后" value="track_left_after" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_left_after.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_left_after.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_left_after.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'track_right_after' }"
                    @click="selectItem('track_right_after')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="右履后" value="track_right_after" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_right_after.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_right_after.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.track_right_after.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="vehicleDetail.android.simulatePedal === true || vehicleDetail.android.simulatePedal === 'true'" class="group-item w-[320px] flex flex-col justify-between">
                <div class="line-one my-2 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-36 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'simulate_left_front' }"
                    @click="selectItem('simulate_left_front')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="左履前(模拟)" value="simulate_left_front" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_left_front.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_left_front.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_left_front.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div
                    class="item-wrap p-2 w-36 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'simulate_right_front' }"
                    @click="selectItem('simulate_right_front')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="右履前(模拟)" value="simulate_right_front" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_right_front.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_right_front.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_right_front.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>

                <div class="line-two mb-2 mt-10 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-36 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'simulate_left_after' }"
                    @click="selectItem('simulate_left_after')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="左履后(模拟)" value="simulate_left_after" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_left_after.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_left_after.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_left_after.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div
                    class="item-wrap p-2 w-36 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'simulate_right_after' }"
                    @click="selectItem('simulate_right_after')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="右履后(模拟)" value="simulate_right_after" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_right_after.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_right_after.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.simulate_right_after.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </el-checkbox-group>
          </div>
          <!-- 装载机 -->
          <div
            v-if="pageData.excavatorType == '3' || pageData.excavatorType == '4'"
            class="group-wrap flex-1 overflow-y-auto"
            style="height: calc(100vh - 91px - 91px - 16px - 16px - 50px)"
          >
            <el-checkbox-group
              class="flex flex-row flex-wrap justify-around items-center h-full"
              v-model="pageData.checkList"
            >
              <div class="group-item w-[260px] flex flex-col justify-between">
                <div class="line-one my-2 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'left_turn' }"
                    @click="selectItem('left_turn')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="左转向" value="left_turn" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.left_turn.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.left_turn.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.left_turn.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'right_turn' }"
                    @click="selectItem('right_turn')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="右转向" value="right_turn" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.right_turn.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.right_turn.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.right_turn.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>

                <div class="line-two mb-2 mt-20 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'brake_control' }"
                    @click="selectItem('brake_control')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="制动控制" value="brake_control" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.brake_control.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.brake_control.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.brake_control.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'throttle_control' }"
                    @click="selectItem('throttle_control')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="油门控制" value="throttle_control" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.throttle_control.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.throttle_control.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.throttle_control.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="group-item w-[280px]">
                <div class="line-one">
                  <div
                    class="item-wrap mx-auto p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'boom_lowering' }"
                    @click="selectItem('boom_lowering')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="动臂降" value="boom_lowering" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.boom_lowering.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.boom_lowering.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.boom_lowering.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
                <div class="line-two my-2 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'bucket_retraction' }"
                    @click="selectItem('bucket_retraction')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="铲斗收" value="bucket_retraction" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_retraction.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_retraction.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_retraction.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div class="item-right">
                    <div
                      class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                      :class="{ 'item-selected': pageData.curSelectedKey == 'bucket_release' }"
                      @click="selectItem('bucket_release')"
                    >
                      <div class="item-name mx-auto">
                        <el-checkbox label="铲斗放" value="bucket_release" />
                      </div>
                      <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                        <p>
                          最小值:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_release.low_limit }}</span>
                        </p>
                        <p>
                          最大值:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_release.up_limit }}</span>
                        </p>
                        <p>
                          灵敏度:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_release.sensitivity }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="line-three">
                  <div
                    class="item-wrap mx-auto p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'boom_lifting' }"
                    @click="selectItem('boom_lifting')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="动臂升" value="boom_lifting" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.boom_lifting.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_lifting.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.boom_lifting.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </el-checkbox-group>
          </div>
          <!-- 电铲 -->
          <div
            v-if="pageData.excavatorType == '2'"
            class="group-wrap flex-1 overflow-y-auto"
            style="height: calc(100vh - 91px - 91px - 16px - 16px - 50px)"
          >
            <el-checkbox-group
              class="flex flex-row flex-wrap justify-around items-center h-full"
              v-model="pageData.checkList"
            >
              <div class="group-item w-[280px]">
                <div class="line-one">
                  <div
                    class="item-wrap mx-auto p-2 w-[200px] h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'arm_up' }"
                    @click="selectItem('arm_up')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="推压/左履带前" value="arm_up" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值: <span class="text-tx-primary font-bold">{{ curHandleForm.arm_up.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.arm_up.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度: <span class="text-tx-primary font-bold">{{ curHandleForm.arm_up.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
                <div class="line-two my-2 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'swing_left' }"
                    @click="selectItem('swing_left')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="左回转" value="swing_left" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.swing_left.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.swing_left.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.swing_left.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div class="item-right">
                    <div
                      class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                      :class="{ 'item-selected': pageData.curSelectedKey == 'swing_right' }"
                      @click="selectItem('swing_right')"
                    >
                      <div class="item-name mx-auto">
                        <el-checkbox label="右回转" value="swing_right" />
                      </div>
                      <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                        <p>
                          最小值:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.swing_right.low_limit }}</span>
                        </p>
                        <p>
                          最大值:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.swing_right.up_limit }}</span>
                        </p>
                        <p>
                          灵敏度:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.swing_right.sensitivity }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="line-three">
                  <div
                    class="item-wrap mx-auto p-2 w-[200px] h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'arm_down' }"
                    @click="selectItem('arm_down')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="收回/左履带后" value="arm_down" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值: <span class="text-tx-primary font-bold">{{ curHandleForm.arm_down.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.arm_down.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.arm_down.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="group-item w-[280px]">
                <div class="line-one">
                  <div
                    class="item-wrap mx-auto p-2 w-[200px] h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'boom_up' }"
                    @click="selectItem('boom_up')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="铲斗下降/右履带前" value="boom_up" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_up.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_up.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.boom_up.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
                <div class="line-two my-2 flex flex-row justify-between">
                  <div
                    class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'bucket_down' }"
                    @click="selectItem('bucket_down')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="开斗" value="bucket_down" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_down.low_limit }}</span>
                      </p>
                      <p>
                        最大值:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_down.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度:
                        <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_down.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                  <div class="item-right">
                    <div
                      class="item-wrap p-2 w-28 h-28 rounded flex flex-col bg-primary-light-9"
                      :class="{ 'item-selected': pageData.curSelectedKey == 'bucket_up' }"
                      @click="selectItem('bucket_up')"
                    >
                      <div class="item-name mx-auto">
                        <el-checkbox label="喇叭" value="bucket_up" />
                      </div>
                      <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                        <p>
                          最小值:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_up.low_limit }}</span>
                        </p>
                        <p>
                          最大值:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_up.up_limit }}</span>
                        </p>
                        <p>
                          灵敏度:
                          <span class="text-tx-primary font-bold">{{ curHandleForm.bucket_up.sensitivity }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="line-three">
                  <div
                    class="item-wrap mx-auto p-2 w-[200px] h-28 rounded flex flex-col bg-primary-light-9"
                    :class="{ 'item-selected': pageData.curSelectedKey == 'boom_down' }"
                    @click="selectItem('boom_down')"
                  >
                    <div class="item-name mx-auto">
                      <el-checkbox label="铲斗提升/右履带后" value="boom_down" />
                    </div>
                    <div class="item-params text-tx-primary tracking-[1px] flex flex-col justify-around">
                      <p>
                        最小值: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_down.low_limit }}</span>
                      </p>
                      <p>
                        最大值: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_down.up_limit }}</span>
                      </p>
                      <p>
                        灵敏度: <span class="text-tx-primary font-bold">{{ curHandleForm.boom_down.sensitivity }}</span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </el-checkbox-group>
          </div>
          <div
            v-if="pageData.excavatorType == ''"
            class="group-wrap flex-1 overflow-y-auto"
            style="height: calc(100vh - 91px - 91px - 16px - 16px - 50px)"
          ></div>
          <!-- 曲线图展示 参数调整 -->
          <div class="input-wrap flex flex-col ml-6 mt-1 w-[320px]">
            <div class="chart-wrap">
              <div ref="webChartRef" style="width: 320px; height: 300px"></div>
            </div>
            <div class="control-wrap pr-5 w-[320px]">
              <div class="text-2xl font-bold">参数调整</div>
              <div class="flex flex-row items-center mt-6">
                <span class="w-[120px]">最大最小值:</span>
                <el-slider
                  v-model="pageData.minMaxValue"
                  range
                  show-stops
                  :max="100"
                  :marks="marks"
                  @input="setChartData"
                />
              </div>
              <div class="flex flex-row items-center mt-6">
                <span class="w-[120px]">灵敏度:</span>
                <el-slider
                  v-model="pageData.sensitivity"
                  show-stops
                  :min="-1"
                  :max="1"
                  :step="0.1"
                  :marks="marks1"
                  @input="setChartData"
                />
              </div>
              <div class="flex flex-row items-center mt-10">
                <el-button class="w-[68px]" @click="resetAll">重置所有</el-button>
                <el-button class="w-[68px]" @click="resetCur">重置当前</el-button>
                <el-button class="w-[68px]" @click="syncAll">同步全部</el-button>
                <el-button class="w-[68px]" @click="selectAll">{{
                  pageData.checkList.length === Object.keys(curHandleForm).length ? "取消全选" : "选中全部"
                }}</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import * as echarts from "echarts";

import feedback from "@/utils/feedback";
import { ArrowLeft } from "@element-plus/icons-vue";
import router from "@/router";
import { vehiclesDetail, vehicleUpdate } from "@/api/device";

type Marks = Record<number, string>;
const marks = reactive<Marks>({
  0: "0",
  25: "25",
  50: "50",
  75: "75",
  100: "100",
});
const marks1 = reactive<Marks>({
  "-1": "-1.0",
  "-0.5": "-0.5",
  0: "0",
  0.5: "0.5",
  1: "1.0",
});

const query = useRoute().query;
const carId = query?.id as string;

const webChartRef = ref<any>(null);
let WebChart: any = null;

const curHandleForm: any = reactive({});

const pageData: any = reactive({
  checkList: [],
  curSelectedKey: "",
  sensitivity: 0,
  minMaxValue: [0, 100],
  selectPlanIndex: 0,
  excavatorType: "",
});

const vehicleDetail: any = reactive({
  vehicle_name: "",
  vehicle_type: "",
  description: "",
  restart_time: 0,
  android: {},
  handleConfig: {
    handleSettingList: [],
  },
});

const curPlanIndex = computed(() => {
  return vehicleDetail.handleConfig.handleSettingList.findIndex((item: any) => item.isUse);
});

const selectItem = (key: string) => {
  pageData.curSelectedKey = key;
  pageData.minMaxValue[0] = curHandleForm[key].low_limit;
  pageData.minMaxValue[1] = curHandleForm[key].up_limit;
  pageData.sensitivity = curHandleForm[key].sensitivity;
  WebChart.setOption({
    series: [{ data: chartCompute(pageData.minMaxValue[0], pageData.minMaxValue[1], pageData.sensitivity) }],
  });
};

const resetCur = () => {
  let key = pageData.curSelectedKey;
  if (!key) return feedback.msgWarning("请选择需要重置的操作项");
  curHandleForm[key].low_limit = 0;
  curHandleForm[key].up_limit = 100;
  curHandleForm[key].sensitivity = 0;
  resetChart();
};

const resetAll = () => {
  for (const key in curHandleForm) {
    if (Object.prototype.hasOwnProperty.call(curHandleForm, key)) {
      const item = curHandleForm[key];
      item.low_limit = 0;
      item.up_limit = 100;
      item.sensitivity = 0;
    }
  }
  resetChart();
};

// 同步全部
const syncAll = () => {
  if (!pageData.curSelectedKey) return feedback.msgWarning("请选择需要重置的操作项");
  for (const key in curHandleForm) {
    if (Object.prototype.hasOwnProperty.call(curHandleForm, key)) {
      const item = curHandleForm[key];
      item.low_limit = pageData.minMaxValue[0];
      item.up_limit = pageData.minMaxValue[1];
      item.sensitivity = pageData.sensitivity;
    }
  }
};

// 选中全部
const selectAll = () => {
  if (pageData.checkList.length === Object.keys(curHandleForm).length) {
    pageData.checkList = [];
  } else {
    pageData.checkList = Object.keys(curHandleForm);
  }
};

const resetChart = () => {
  pageData.minMaxValue = [0, 100];
  pageData.sensitivity = 0;

  WebChart.setOption({
    series: [{ data: chartCompute(0, 100, 0) }],
  });
};

const resetChartFrom = () => {
  if (!pageData.curSelectedKey) return feedback.msgWarning("请选择需要重置的操作项");
  const { low_limit, up_limit, sensitivity } = curHandleForm[pageData.curSelectedKey];

  pageData.minMaxValue = [low_limit, up_limit];
  pageData.sensitivity = sensitivity;
  WebChart.setOption({
    series: [{ data: chartCompute(low_limit, up_limit, sensitivity) }],
  });
};

const changePlan = (index: number) => {
  Object.assign(vehicleDetail.handleConfig.handleSettingList[pageData.selectPlanIndex], curHandleForm);
  const { isPublic, isUse, step_switch, ...items } = vehicleDetail.handleConfig.handleSettingList[index];
  Object.assign(curHandleForm, items);
  pageData.selectPlanIndex = index;
  resetChartFrom();
};

const usePlan = () => {
  vehicleDetail.handleConfig.handleSettingList.forEach((item: any, i: number) => {
    if (i === pageData.selectPlanIndex) {
      item.isUse = true;
    } else {
      item.isUse = false;
    }
  });

  feedback.msgSuccess("切换为方案" + (pageData.selectPlanIndex + 1));
};

const goBack = () => {
  router.push({ path: "/device" });
};

const getVehiclesDetail = async () => {
  const res = await vehiclesDetail(carId);
  pageData.excavatorType = res.android.excavatorType; //excavatorType: 1, //铲端类型 1:挖掘机 2:电铲 太重 3:装载机 瓮福 4:装载机 营口、沈阳院、厦工 5:钻机 泰业 6:徐工700吨
  Object.assign(vehicleDetail, res);
  pageData.curSelectedKey = Object.keys(vehicleDetail.handleConfig.handleSettingList[0])[0];
  const { isPublic, isUse, step_switch, ...items } = vehicleDetail.handleConfig.handleSettingList[curPlanIndex.value];
  Object.assign(curHandleForm, items);
  resetChartFrom();
};

const handleSubmit = async () => {
  Object.assign(vehicleDetail.handleConfig.handleSettingList[pageData.selectPlanIndex], curHandleForm);

  let params = {
    id_: carId,
    android: vehicleDetail.android,
    handleConfig: vehicleDetail.handleConfig,
    vehicle_name: vehicleDetail.vehicle_name,
    vehicle_type: vehicleDetail.vehicle_type,
    description: vehicleDetail.description,
    restart_time: vehicleDetail.restart_time,
  };
  await vehicleUpdate(params);
  feedback.msgSuccess("操作成功");
  goBack();
};

const chartCompute = (a = 0, b = 100, c = 0) => {
  let R_min = Math.abs((Math.pow(a, 2) - Math.pow(b, 2) + Math.pow(100, 2)) / (2 * a - 2 * b) - b);
  let R = R_min + 1000 * (1 - Math.abs(c));
  let m = (Math.pow(100, 2) + Math.pow(b, 2) - Math.pow(a, 2)) / 200;
  let n = (a - b) / 100;
  let p = (m * n - a) / Math.sqrt(Math.pow(n, 2) + 1);
  let y1 =
    (Math.sqrt(Math.pow(R, 2) - Math.pow(a, 2) - Math.pow(m, 2) + Math.pow(p, 2)) - p) / Math.sqrt(Math.pow(n, 2) + 1);
  let x1 = m + n * y1;
  let y2 =
    (-Math.sqrt(Math.pow(R, 2) - Math.pow(a, 2) - Math.pow(m, 2) + Math.pow(p, 2)) - p) / Math.sqrt(Math.pow(n, 2) + 1);
  let x2 = m + n * y2;

  let x, y: any[];

  if (c === 0) {
    // 直线
    x = linspace(0, 100, 1000);
    y = x.map((xi) => ((b - a) * xi) / 100 + a);
  } else if (c < 0) {
    const cx = x1,
      cy = y1;
    x = linspace(0, 100, 1000);
    y = x.map((xi) => -Math.sqrt(R ** 2 - (xi - cx) ** 2) + cy);
  } else {
    const cx = x2,
      cy = y2;
    x = linspace(0, 100, 1000);
    y = x.map((xi) => Math.sqrt(R ** 2 - (xi - cx) ** 2) + cy);
  }

  let data: any[][] = [];
  x.forEach((item, index) => {
    data.push([item, y[index]]);
  });

  return data;
};

const linspace = (startValue: number, stopValue: number, cardinality: number) => {
  const arr = [];
  const step = (stopValue - startValue) / (cardinality - 1);
  for (let i = 0; i < cardinality; i++) {
    arr.push(startValue + step * i);
  }
  return arr;
};

const setChartData = () => {
  if (pageData.curSelectedKey) {
    if (pageData.checkList.length > 1 && pageData.checkList.includes(pageData.curSelectedKey)) {
      pageData.checkList.forEach((item: string) => {
        curHandleForm[item].low_limit = pageData.minMaxValue[0];
        curHandleForm[item].up_limit = pageData.minMaxValue[1];
        curHandleForm[item].sensitivity = pageData.sensitivity;
      });
    } else {
      curHandleForm[pageData.curSelectedKey].low_limit = pageData.minMaxValue[0];
      curHandleForm[pageData.curSelectedKey].up_limit = pageData.minMaxValue[1];
      curHandleForm[pageData.curSelectedKey].sensitivity = pageData.sensitivity;
    }
  }

  WebChart.setOption({
    series: [{ data: chartCompute(pageData.minMaxValue[0], pageData.minMaxValue[1], pageData.sensitivity) }],
  });
};

onMounted(() => {
  WebChart = echarts.init(webChartRef.value);
  const options = {
    title: {
      text: "灵敏度可视展示",
    },
    grid: {
      bottom: 30,
    },
    animation: false, // 关闭动画效果
    legend: {},
    xAxis: {
      min: 0,
      max: 100,
    },
    yAxis: {
      min: 0,
      max: 100,
    },
    series: [
      {
        data: chartCompute(0, 100, -1),
        type: "line",
        smooth: true,
        symbol: "none", // 不显示图标形状
        lineStyle: {
          color: "#ff5f00", // 设置线条颜色为红色
        },
      },
    ],
  };
  options && WebChart.setOption(options);

  getVehiclesDetail();
});

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px 20px;
}
:deep(.el-checkbox__label) {
  font-size: 18px;
  color: #333333;
}
:deep(.el-slider) {
  .el-slider__runway {
    background-color: var(--el-color-primary-light-9);
  }
  --el-slider-stop-bg-color: var(--el-color-primary-light-9);
}
.el-checkbox-group {
  font-size: 14px;
}
.item-wrap {
  cursor: pointer;
  user-select: none;
  .item-name {
    flex: 1;
  }
  .item-params {
    flex: 3;
  }
}
.item-selected {
  background-color: var(--el-color-primary-light-7);
}
.plan-selected {
  // background-color: var(--el-color-primary-light-7);
  border-color: var(--el-color-primary);
}
.handle-wrap {
  position: relative;
  .plan-wrap {
    position: absolute;
  }
}
.group-item {
  margin-left: 1px;
  margin-top: 8px;
}
</style>
