import apps.models.websocket as WSMode<PERSON>


def test_run_data_sync():
    data1 = {
        "ros_nodes": [
            {
                "auto_start": 1,
                "node_name": "real_time_composite0",
                "package_name": "builderx_video",
                "node_type": "video_composite",
                "params": {
                    "camera_list": {
                        "camera_0": {
                            "device": "aif_ar_video",
                            "detail": "",
                            "left": 0,
                            "top": 0,
                            "width": 1920,
                            "height": 1080,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 0,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 1,
                            "border_width": 0,
                            "border_color": "#FF5F00",
                        },
                        "camera_1": {
                            "device": "tripartite_video",
                            "detail": "三方相机",
                            "left": 436,
                            "top": 24,
                            "width": 356,
                            "height": 200,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 0,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                        "camera_2": {
                            "device": "back_video",
                            "detail": "后视",
                            "left": 60,
                            "top": 60,
                            "width": 360,
                            "height": 202,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 2,
                            "flip": 2,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                        "camera_3": {
                            "device": "left_video",
                            "detail": "左视",
                            "left": 24,
                            "top": 344,
                            "width": 390,
                            "height": 220,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 1,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                        "camera_4": {
                            "device": "right_video",
                            "detail": "右视",
                            "left": 260,
                            "top": 344,
                            "width": 390,
                            "height": 220,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 1,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                        "camera_5": {
                            "device": "corner_video",
                            "detail": "切角",
                            "left": 30,
                            "top": 800,
                            "width": 410,
                            "height": 230,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 0,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                        "camera_6": {
                            "device": "dashboard_video",
                            "detail": "仪表盘",
                            "left": 568,
                            "top": 860,
                            "width": 390,
                            "height": 220,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 0,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                    },
                    "height": 1080,
                    "inter_mode": 5,
                    "name": "comp_video0",
                    "object_detection": {"class_name": ["Worker", "chunks"], "color": "#ffffff", "show": True},
                    "width": 1920,
                },
            },
            {
                "auto_start": 1,
                "node_name": "real_time_stream0",
                "package_name": "builderx_video",
                "node_type": "video_stream",
                "params": {
                    "width": 1920,
                    "detail": "混流01",
                    "device": "comp_video0",
                    "fps": 30,
                    "height": 1080,
                    "rtsp_uri": "rtsp://api.builderx.com/live/pro/xG6hkPL6UF/stream0",
                    "encoder": {
                        "bitrate": 4000000,
                        "control_rate": 0,
                        "fmt": "h265",
                        "iframeinterval": 60,
                        "idrinterval": 0,
                    },
                },
            },
        ],
        "gmsl_cameras": [
            {
                "id": "VIDEO0",
                "camera_id": "main_video",
                "camera_name": "主视角",
                "type": "MVG2CB-001D",
                "format": "UYVY",
                "is_enable": True,
            },
            {
                "id": "VIDEO1",
                "camera_id": "corner_video",
                "camera_name": "切角",
                "type": "MVG2CB-001K",
                "format": "UYVY",
                "is_enable": True,
            },
            {
                "id": "VIDEO2",
                "camera_id": "left_video",
                "camera_name": "左视",
                "type": "MVG2CB-001K",
                "format": "UYVY",
                "is_enable": True,
            },
            {
                "id": "VIDEO3",
                "camera_id": "",
                "camera_name": "",
                "type": "MVG2CB-NONE",
                "format": "",
                "is_enable": False,
            },
            {
                "id": "VIDEO4",
                "camera_id": "back_video",
                "camera_name": "后视",
                "type": "MVG2CB-001K",
                "format": "UYVY",
                "is_enable": True,
            },
            {
                "id": "VIDEO5",
                "camera_id": "right_video",
                "camera_name": "右视",
                "type": "MVG2CB-001K",
                "format": "UYVY",
                "is_enable": True,
            },
            {
                "id": "VIDEO6",
                "camera_id": "",
                "camera_name": "",
                "type": "MVG2CB-NONE",
                "format": "",
                "is_enable": False,
            },
            {
                "id": "VIDEO7",
                "camera_id": "",
                "camera_name": "",
                "type": "MVG2CB-NONE",
                "format": "UYVY",
                "is_enable": False,
            },
        ],
        "ip_cameras": [
            {
                "id": "NETWORK0",
                "camera_id": "dashboard_video",
                "camera_name": "仪表盘",
                "uri": "rtsp://***************/media/video1",
                "is_enable": True,
            },
            {
                "id": "NETWORK1",
                "camera_id": "tripartite_video",
                "camera_name": "三方相机",
                "uri": "rtsp://admin:builderX@************:554/h264/ch1/main/av_stream/",
                "is_enable": True,
            },
        ],
        "eth_can": [{"id": "CAN0", "bitrate": 500000, "ext_fd": "", "is_enable": True}],
    }

    data2 = {
        "host_name": "",
        "gmsl_cameras": [
            {
                "id": "VIDEO0",
                "type": "MVG2CB-001D",
                "camera_id": "main_video",
                "camera_name": "主视角",
                "format": "UYVY",
                "is_enable": True,
            },
            {
                "id": "VIDEO2",
                "type": "MVG2CB-001K",
                "camera_id": "left_video",
                "camera_name": "左视",
                "format": "UYVY",
                "is_enable": True,
            },
            {
                "id": "VIDEO1",
                "type": "MVG2CB-001K",
                "camera_id": "corner_video",
                "camera_name": "切角",
                "format": "UYVY",
                "is_enable": True,
            },
            {
                "id": "VIDEO3",
                "type": "MVG2CB-NONE",
                "format": "",
                "camera_id": "",
                "camera_name": "",
                "is_enable": False,
            },
            {
                "id": "VIDEO4",
                "type": "MVG2CB-001K",
                "camera_id": "back_video",
                "camera_name": "后视",
                "is_enable": True,
                "format": "UYVY",
            },
            {
                "id": "VIDEO5",
                "type": "MVG2CB-001K",
                "camera_id": "right_video",
                "camera_name": "右视",
                "is_enable": True,
                "format": "UYVY",
            },
            {
                "id": "VIDEO6",
                "type": "MVG2CB-NONE",
                "camera_id": "",
                "camera_name": "",
                "format": "",
                "is_enable": False,
            },
            {
                "id": "VIDEO7",
                "type": "MVG2CB-NONE",
                "camera_id": "",
                "camera_name": "",
                "format": "UYVY",
                "is_enable": False,
            },
        ],
        "ip_cameras": [
            {
                "id": "NETWORK0",
                "camera_id": "dashboard_video",
                "camera_name": "仪表盘",
                "uri": "rtsp://***************/media/video1",
                "is_enable": True,
            },
            {
                "id": "NETWORK1",
                "camera_id": "tripartite_video",
                "camera_name": "三方相机",
                "uri": "rtsp://admin:builderX@************:554/h264/ch1/main/av_stream/",
                "is_enable": True,
            },
        ],
        "eth_can": [{"id": "CAN0", "bitrate": 500000, "ext_fd": "", "is_enable": True}],
        "ros_nodes": [
            {
                "auto_start": 1,
                "node_name": "real_time_stream0",
                "package_name": "builderx_video",
                "node_type": "video_stream",
                "params": {
                    "width": 1920,
                    "detail": "混流01",
                    "device": "comp_video0",
                    "fps": 30,
                    "height": 1080,
                    "rtsp_uri": "rtsp://api.builderx.com/live/pro/xG6hkPL6UF/stream0",
                    "encoder": {
                        "bitrate": 4000000,
                        "control_rate": 0,
                        "fmt": "h265",
                        "iframeinterval": 60,
                        "idrinterval": 0,
                    },
                },
            },
            {
                "auto_start": 1,
                "node_name": "real_time_composite0",
                "package_name": "builderx_video",
                "node_type": "video_composite",
                "params": {
                    "camera_list": {
                        "camera_0": {
                            "device": "aif_ar_video",
                            "detail": "",
                            "left": 0,
                            "top": 0,
                            "width": 1920,
                            "height": 1080,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 0,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 1,
                            "border_width": 0,
                            "border_color": "#FF5F00",
                        },
                        "camera_1": {
                            "device": "tripartite_video",
                            "detail": "三方相机",
                            "left": 436,
                            "top": 24,
                            "width": 356,
                            "height": 200,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 0,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                        "camera_2": {
                            "device": "back_video",
                            "detail": "后视",
                            "left": 60,
                            "top": 60,
                            "width": 360,
                            "height": 202,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 2,
                            "flip": 2,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                        "camera_3": {
                            "device": "left_video",
                            "detail": "左视",
                            "left": 24,
                            "top": 344,
                            "width": 390,
                            "height": 220,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 1,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                        "camera_4": {
                            "device": "right_video",
                            "detail": "右视",
                            "left": 260,
                            "top": 344,
                            "width": 390,
                            "height": 220,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 1,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                        "camera_5": {
                            "device": "corner_video",
                            "detail": "切角",
                            "left": 30,
                            "top": 800,
                            "width": 410,
                            "height": 230,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 0,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                        "camera_6": {
                            "device": "dashboard_video",
                            "detail": "仪表盘",
                            "left": 568,
                            "top": 860,
                            "width": 390,
                            "height": 220,
                            "crop_left": 0,
                            "crop_top": 0,
                            "crop_width": 0,
                            "crop_height": 0,
                            "alpha": 1.0,
                            "rotate": 0,
                            "flip": 0,
                            "z_index": 0,
                            "primary": 0,
                            "border_width": 4,
                            "border_color": "#FF5F00",
                        },
                    },
                    "height": 1080,
                    "inter_mode": 5,
                    "name": "comp_video0",
                    "object_detection": {"class_name": ["Worker", "chunks"], "color": "#ffffff", "show": True},
                    "width": 1920,
                },
            },
        ],
    }

    run_data1 = WSModel.DeviceRunData(**data1)  # type: ignore
    run_data2 = WSModel.DeviceRunData(**data2)
    res = run_data1.compare(run_data2)
    assert all(res.values())

    data1["gmsl_cameras"][0]["is_enable"] = False
    run_data1 = WSModel.DeviceRunData(**data1)  # type: ignore
    run_data2 = WSModel.DeviceRunData(**data2)
    res = run_data1.compare(run_data2)
    assert all(res.values()) is False
