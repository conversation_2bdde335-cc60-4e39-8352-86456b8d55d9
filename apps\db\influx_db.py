from influxdb_client.client.write.point import Point
from influxdb_client.client.query_api_async import QueryApiAsync
from influxdb_client.client.write_api_async import WriteApiAsync
from influxdb_client.client.influxdb_client_async import InfluxDBClientAsync

from apps.config import get_settings
from apps.utils.patterns import singleton


__all__ = ["InfluxDB"]


@singleton
class InfluxDB:
    host = get_settings().influxdb_host
    port = get_settings().influxdb_port
    uri = f"http://{host}:{port}"
    org = get_settings().influxdb_org
    token = get_settings().influxdb_admin_token
    # influxdb_client = InfluxDBClientAsync(url=uri, token=token, org=org)

    def __init__(self, _=None) -> None:
        self.client = InfluxDBClientAsync(url=self.uri, token=self.token, org=self.org)
        self.query_api: QueryApiAsync = self.client.query_api()
        self.write_api: WriteApiAsync = self.client.write_api()

    async def query(self, q: str):
        """
        Read data from the InfluxDB database.

        Parameters:
            query (str): The query to retrieve data.

        Returns:
            data (list): The retrieved data.
        """
        return await self.query_api.query(q)  # type: ignore

    async def query_stream(self, q: str):
        """
        Read data from the InfluxDB database.

        Parameters:
            query (str): The query to retrieve data.

        Returns:
            data (list): The retrieved data.
        """
        return await self.query_api.query_stream(q)  # type: ignore

    async def write(self, bucket: str, record: list[Point]):
        """
        Write data to the InfluxDB database.

        Parameters:
            bucket (str): The bucket to write the data.
            record (dict): The data to write to the database.
        """
        return await self.write_api.write(bucket=bucket, record=record)  # type: ignore
