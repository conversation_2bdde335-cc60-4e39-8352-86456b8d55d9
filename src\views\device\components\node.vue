<template>
  <el-scrollbar :height="scrollHeight">
    <div v-if="vehicleDetail.node_list.length > 0" class="app-wrap">
      <div class="app-item" v-for="(nodeItem, index) in vehicleDetail.node_list" :key="index">
        <el-card shadow="hover" class="h-[150px] !rounded-xl !pt-1 ros-card">
          <div class="flex flex-row items-center">
            <el-tooltip effect="light" placement="top" :content="nodeItem.node_name">
              <div class="text-3xl font-semibold w-[206px] one-line-ellipsis">{{ nodeItem.node_name }}</div>
            </el-tooltip>
            <el-tag class="ml-2" round effect="light" :type="nodeItem.status === 1 ? 'success' : 'warning'">
              {{ nodeItem.status ? $t("vehicle.运行") : $t("vehicle.停止") }}
            </el-tag>
          </div>
          <div class="flex flex-row mt-2">
            <div class="w-[58px] ros-title">{{ $t("vehicle.类型") }}：</div>
            <div class="ml-0 text-[#666]">{{ nodeItem.node_type }}</div>
          </div>
          <div class="flex flex-row">
            <div class="w-[58px] ros-title">{{ $t("vehicle.名称") }}：</div>
            <div class="ml-0 text-[#666]">{{ nodeItem.package_name }}</div>
          </div>
          <div class="flex justify-end items-center mt-3 flex-wrap">
            <el-tooltip
              v-if="nodeItem.node_type === ROS_NODE_ENUM.stream"
              effect="light"
              placement="top"
              :content="$t('vehicle.复制自主画面链接')"
            >
              <Icon class="w-6 card-icon" :size="20" name="el-icon-DocumentCopy" @click="handleCreateLink()" />
            </el-tooltip>
            <el-tooltip
              v-if="nodeItem.node_type === ROS_NODE_ENUM.stream"
              effect="light"
              placement="top"
              :content="$t('stream.窗口设置')"
            >
              <Icon
                class="w-6 card-icon"
                id="openStream"
                :size="20"
                name="el-icon-Monitor"
                @click="handleStream(nodeItem)"
              />
            </el-tooltip>
            <el-tooltip effect="light" placement="top" :content="$t('vehicle.手动编辑')">
              <Icon class="w-6 card-icon" :size="18" name="el-icon-Edit" @click="handleEditNode(nodeItem)" />
            </el-tooltip>
            <el-tooltip effect="light" placement="top" :content="$t('vehicle.删除节点')">
              <Icon
                class="w-6 card-icon"
                :size="18"
                name="el-icon-Delete"
                @click="handleDeleteRos(nodeItem.node_name)"
              />
            </el-tooltip>
            <el-dropdown class="ml-1" trigger="click">
              <Icon class="w-6 card-icon" :size="22" name="local-icon-gengduoandroid" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :icon="VideoPlay" @click="handleControlRos('start', nodeItem)">
                    {{ $t("vehicle.启动节点") }}
                  </el-dropdown-item>
                  <el-dropdown-item :icon="VideoPause" @click="handleControlRos('stop', nodeItem)">
                    {{ $t("vehicle.停止节点") }}
                  </el-dropdown-item>
                  <el-dropdown-item :icon="RefreshRight" @click="handleControlRos('restart', nodeItem)">
                    {{ $t("vehicle.重启节点") }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-card>
      </div>
    </div>
    <div class="w-full" v-else>
      <el-empty :description="$t('vehicle.请导入或添加节点')" />
    </div>
  </el-scrollbar>
  <AddNode
    v-if="showAddNodeRef"
    ref="addNodeRef"
    :vehicleId="vehicleDetail.vehicle_id"
    @success="getNodeList"
    @close="closeAddNode"
  />
  <StreamPopup
    v-if="showStream"
    ref="streamRef"
    :vehicleId="vehicleDetail.vehicle_id"
    @success="editRosParams"
    @close="showStream = false"
  />
  <JsonEditor ref="uploadNodeRef" title="ROS节点导入" :jsonParams="pageData.jsonFile" @success="handleImportNodes" />
</template>

<script setup lang="ts">
import { ROS_NODE_ENUM } from "@/utils/constants";
import { RefreshRight, VideoPause, VideoPlay } from "@element-plus/icons-vue";
import feedback from "@/utils/feedback";
import { downJsonFile, timeFormat } from "@/utils/util";
import useClipboard from "vue-clipboard3";
import { useI18n } from "vue-i18n";
import useWebsocketStore from "@/stores/modules/websocket";

import {
  nodeList,
  editRos,
  delRosNode,
  vehiclesDetail,
  controlNode,
  deviceDelete,
  syncData,
  vehiclesList,
  importNode,
} from "@/api/device";

import AddNode from "./add-node.vue";
import StreamPopup from "../stream-popup.vue";

const { t } = useI18n();
const websocketStore = useWebsocketStore();
const emit = defineEmits(["success", "close"]);
const addNodeRef = shallowRef<InstanceType<typeof AddNode>>();
const showAddNodeRef = ref(false);
const streamRef = shallowRef<InstanceType<typeof StreamPopup>>();
const showStream = ref(false);
const uploadNodeRef = shallowRef();

const props = defineProps({
  vehicleId: {
    type: String,
    required: true,
  },
  vehicleOnline: {
    type: Boolean,
    required: true,
  },
});

const curNode = ref<any>({});
const vehicleDetail: any = reactive({
  vehicle_id: props.vehicleId,
  online: props.vehicleOnline,
  node_list: [],
  description: "",
  devices: {},
});
const pageData: any = reactive({
  jsonFile: [],
});

let timer: any = null;

onMounted(async () => {
  await getNodeList();

  setTimeout(() => {
    syncNodeStatus();
  }, 500);

  timer = setInterval(() => {
    syncNodeStatus();
  }, 5000);
});

onUnmounted(() => {
  if (timer) clearInterval(timer);
});

const scrollHeight = computed(() => {
  return document.documentElement.clientHeight - 178;
});

const getNodeList = async () => {
  const { lists: res } = await nodeList(vehicleDetail.vehicle_id);
  // 先按照package_name排序，再按照node_type排序
  res.sort((a: any, b: any) => {
    if (a.package_name < b.package_name) {
      return -1;
    } else if (a.package_name > b.package_name) {
      return 1;
    } else {
      if (a.node_type < b.node_type) {
        return -1;
      } else if (a.node_type > b.node_type) {
        return 1;
      } else {
        return 0;
      }
    }
  });
  vehicleDetail.node_list = res;
};

const handleEditNode = async (data: any) => {
  showAddNodeRef.value = true;
  await nextTick();
  addNodeRef.value?.open("edit");
  addNodeRef.value?.setFormData(data);
};

const handleStream = async (data: any) => {
  showStream.value = true;
  curNode.value = data;
  curNode.value.id = vehicleDetail.vehicle_id;
  await nextTick();
  streamRef.value?.open();
};

const handleCreateLink = () => {
  const host = window.location.host;
  const url = `http://${host}/aid?vehicleId=${vehicleDetail.vehicle_id}`;
  const { toClipboard } = useClipboard();
  toClipboard(url)
    .then(() => {
      feedback.msgSuccess(`成功复制自主画面链接到剪切板`);
    })
    .catch(() => {
      feedback.msgError("创建失败");
    });
};

const syncNodeStatus = () => {
  if (vehicleDetail.node_list.length && websocketStore.nodeStatus[vehicleDetail.vehicle_id]) {
    vehicleDetail.node_list.forEach((oldItem: any) => {
      websocketStore.nodeStatus[vehicleDetail.vehicle_id].forEach((newItem: any) => {
        if (oldItem.node_name === newItem.name) {
          oldItem.status = newItem.value;
        }
      });
    });
  }
};

const handleAddNode = async () => {
  showAddNodeRef.value = true;
  await nextTick();
  addNodeRef.value?.open();
};

const handleControlRos = async (type: string, item: any) => {
  const typeEnum: any = {
    start: "启动",
    stop: "停止",
    restart: "重启",
  };
  await feedback.confirm(`确定要${typeEnum[type]}${item.node_name}节点？`);
  const params = {
    vehicle_id: vehicleDetail.vehicle_id,
    data: {
      node_name: item.node_name,
      node_type: "ros",
      action: type,
    },
  };
  await controlNode(params);
  feedback.msgSuccess("操作成功");
};

const handleDownloadNode = async () => {
  const { lists: res } = await nodeList(vehicleDetail.vehicle_id);
  let fileName = `nodes_${timeFormat(Date.now(), "mm月dd日hh时MM分")}.json`;
  downJsonFile(res, fileName);
};

const handleDeleteRos = async (nodeName: String) => {
  await feedback.confirm(t("vehicle.确定要删除"));
  const params = {
    vehicle_id: vehicleDetail.vehicle_id,
    node_name: nodeName,
  };
  await delRosNode(params);
  await getNodeList();
};

const closeAddNode = () => {
  showAddNodeRef.value = false;
};

const editRosParams = async (obj: object) => {
  const params = {
    vehicle_id: vehicleDetail.vehicle_id,
    node_name: curNode.value.node_name,
    params: obj,
  };
  feedback.loading("");
  await editRos(params);
  feedback.msgSuccess("操作成功");
  getNodeList();
  feedback.closeLoading();
};

const handleImportNode = async (file: any) => {
  if (file && file.raw) {
    const reader: any = new FileReader();

    reader.onload = async () => {
      try {
        pageData.jsonFile = JSON.parse(reader.result);
        setTimeout(() => {
          uploadNodeRef.value?.open();
        }, 200);
      } catch (error) {
        console.error("Invalid JSON file");
      }
    };
    reader.readAsText(file.raw);
  }
};

const handleImportNodes = async () => {
  const params = {
    vehicle_id: vehicleDetail.vehicle_id,
    data: pageData.jsonFile,
  };
  feedback.loading("");
  await importNode(params);
  feedback.msgSuccess("操作成功");
  getNodeList();
  feedback.closeLoading();
};

defineExpose({ handleDownloadNode, handleImportNode, handleAddNode });
</script>
<style scoped lang="scss">
.app-wrap {
  display: grid;
  justify-content: space-around;
  gap: 16px;
  grid-template-columns: repeat(auto-fill, 300px);
}

.app-wrap::after {
  content: "";
  width: 300px;
}

.ros-card {
  background-color: var(--el-color-primary-light-9);
  border-color: rgb(242 240 236);
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom left, transparent, var(--el-color-primary-light-9)),
    url("@/assets/images/ros_logo.png");
  background-position: top right;
  background-size: 80px 80px;
}

.card-icon {
  cursor: pointer;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background-color: #ff8740;
  transition: all 0.3s linear;

  &:hover {
    background-color: var(--el-color-primary);
    transform: scale(1.1);
  }
}

.card-icon + .card-icon {
  margin-left: 4px;
}

:deep(.el-card__body) {
  padding: 8px 20px;
}
</style>
