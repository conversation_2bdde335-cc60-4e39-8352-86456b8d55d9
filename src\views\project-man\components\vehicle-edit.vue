<template>
  <div class="edit-popup">
    <popup ref="popupRef" :title="popupTitle" :async="true" width="450px" @confirm="handleSubmit" @close="handleClose">
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item label="车辆名称" prop="vehicle_name">
          <el-input v-model="formData.vehicle_name" placeholder="请输入车辆名称" clearable />
        </el-form-item>
        <el-form-item label="车辆类型" prop="vehicle_type">
          <el-select v-model="formData.vehicle_type" placeholder="请选择车辆类型" clearable>
            <el-option v-for="item in vehicleTypeList" :key="item.flag" :label="item.name" :value="Number(item.flag)" />
          </el-select>
        </el-form-item>
        <el-form-item label="车辆SN" prop="vehicle_sn">
          <el-input v-model="formData.vehicle_sn" placeholder="请输入车辆SN" clearable />
        </el-form-item>
        <el-form-item label="安装人员" prop="installer">
          <el-select
            v-model="formDataInstallerModel"
            placeholder="请选择安装人员"
            multiple
            clearable
            @change="handleInstallerChange"
          >
            <el-option v-for="item in adminList" :key="item.username" :label="item.nickname" :value="item.username" />
          </el-select>
        </el-form-item>
        <el-form-item label="作业指导" prop="guide_id">
          <el-select v-model="formData.guide_id" placeholder="请选择作业指导" clearable>
            <el-option v-for="item in guideList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="智能功能" prop="smart_feature">
          <el-select v-model="formData.smart_feature" placeholder="请选择智能功能" multiple clearable>
            <el-option v-for="item in smartFeatureList" :key="item.flag" :label="item.name" :value="Number(item.flag)" />
          </el-select>
        </el-form-item>
        <el-form-item label="平台ID" prop="oms_id">
          <el-input v-model="formData.oms_id" placeholder="请输入平台ID" clearable />
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";
import { guideList as guideListApi } from "@/api/guide-man";
import { projectAddVehicle, projectEditVehicle } from "@/api/project-man";
import { getUserList } from "../util";
import useMetadataStore from "@/stores/modules/metadata";

const props = defineProps({ id: String });
const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "编辑车辆" : "添加车辆";
});

const formData: any = reactive({
  vehicle_id: "",
  vehicle_name: "",
  vehicle_type: 1,
  vehicle_sn: "",
  installer: [],
  guide_id: "",
  oms_id: "",
  smart_feature: [],
});

const formRules = reactive({
  vehicle_name: [
    {
      required: true,
      message: "请输入车辆名称",
      trigger: ["blur"],
    },
  ],
});

const vehicleTypeList: any = ref([]);
const smartFeatureList: any = ref([]);
const adminList: any = ref([]);
const guideList: any = ref([]);
const formDataInstallerModel: any = ref([]);
const metadataStore = useMetadataStore();

const handleInstallerChange = (e: any) => {
  formData.installer = adminList.value.filter((item: any) => e.includes(item.username));
};

onMounted(async () => {
  await getVehicleTypeList();
  await getAdminList();
  await getGuideList();
  console.log(formData.installer);
  formDataInstallerModel.value = formData.installer.map((item: any) => item.username);
});

const getVehicleTypeList = async () => {
  vehicleTypeList.value = await metadataStore.fetchMetadata("VEHICLE_TYPE");
  smartFeatureList.value = await metadataStore.fetchMetadata("smart_feature");
};

const getAdminList = async () => {
  // 66de61b875f9d546876b21aa 角色ID
  adminList.value = await getUserList();
};

const getGuideList = async () => {
  const { lists } = await guideListApi({});
  guideList.value = lists;
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  mode.value == "edit"
    ? await projectEditVehicle({ id: props.id, ...formData })
    : await projectAddVehicle({ id: props.id, ...formData });
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (data: any) => {
  console.log(data);
  
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
