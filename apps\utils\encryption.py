import hashlib
import secrets
import uuid


def sha256_str(data: str) -> str:
    """根据字符串生成SHA256加密值"""
    sha256_hash = hashlib.sha256()
    sha256_hash.update(data.encode("utf-8"))
    return sha256_hash.hexdigest()


def md5_str(data: str) -> str:
    """根据字符串生成MD5值"""
    hl_md5 = hashlib.md5()
    hl_md5.update(data.encode("utf-8"))
    return hl_md5.hexdigest()


def make_token(len_: int = 16) -> str:
    """生成唯一Token"""
    return secrets.token_hex(len_)


def make_uuid() -> str:
    """生成唯一UUID"""
    return uuid.uuid4().hex
