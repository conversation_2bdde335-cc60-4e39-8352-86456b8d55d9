from typing import Optional

from .http_base import HttpCode, HttpResp


__all__ = ["AppException"]


class AppException(Exception):
    """应用异常基类"""

    def __init__(
        self,
        exc: HttpCode,
        *args,
        code: Optional[int] = None,
        msg: Optional[str] = None,
        echo_exc: bool = False,
        **kwargs,
    ):
        super().__init__()

        self.exc_code = exc

        _code = code if code is not None else exc.code
        _message = msg if msg is not None else exc.msg

        self._code = _code or HttpResp.FAILED.code
        self._message = _message or HttpResp.FAILED.msg

        self.echo_exc = echo_exc
        self.kwargs = kwargs or {}
        self.args = args

    @property
    def code(self) -> int:
        return self._code

    @property
    def msg(self) -> str:
        return self._message

    def __str__(self):
        return f"{self.code}: {self.msg}"
