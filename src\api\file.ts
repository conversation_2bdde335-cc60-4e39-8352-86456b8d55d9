import request from "@/utils/request";

const API_VERSION = 'v2';

export function fileCateAdd(params: Record<string, any>) {
  return request.post({ url: `/${API_VERSION}/material/cate_add`, params });
}

export function fileCateEdit(params: Record<string, any>) {
  return request.put({ url: `/${API_VERSION}/material/cate_update/${params.id}`, params });
}

// 文件分类删除
export function fileCateDelete(id: number) {
  return request.delete({ url: `/${API_VERSION}/material/cate_delete/` + id });
}

// 文件分类列表
export function fileCateLists(params: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/material/cate_list`, params });
}

// 视频图片列表
export function videoImageList(params: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/material/media_list`, params });
}

// 文件列表
export function fileList(params: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/data_manage/media/video`, params });
}

// 下载视频文件
export function fileDownload(fileName: string) {
  return request.get(
    { url: `/${API_VERSION}/material/download/${fileName}`, responseType: "blob" },
    { isReturnDefaultResponse: true }
  );
}

// 文件删除
export function fileDelete(params: Record<string, any>) {
  return request.delete({ url: `/${API_VERSION}/material/delete`, data: params.ids });
}

// 文件移动
export function fileMove(params: Record<string, any>) {
  return request.post({ url: "/common/album/albumMove", params });
}

// 文件重命名
export function fileRename(params: { id: number; name: string }) {
  return request.post({ url: "/common/album/albumRename", params });
}
