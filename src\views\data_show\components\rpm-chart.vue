<template>
  <div ref="chartRef" class="w-full h-full mx-0 my-auto"></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import useWebsocketStore from "@/stores/modules/websocket";

const webStore = useWebsocketStore();
const chartRef = ref();
const chart = ref();

onMounted(() => {
  setTimeout(() => {
    chart.value = echarts.init(chartRef.value);
    chart.value.setOption(options);
  }, 100);

  setInterval(() => {
    if (!chart.value) return;
    chart.value.setOption({
      series: [
        {
          data: [
            {
              value: webStore.vehicleInfo.engine_rpm.toFixed(1),
              name: "rpm",
            },
          ],
        },
      ],
    });
  }, 1000);
});

const options = {
  tooltip: {
    formatter: "{a} <br/>{c} {b}",
  },
  // title: {
  //   text: "发动机转速",
  //   bottom: 14,
  //   left: "center",
  //   textStyle: {
  //     fontSize: 24,
  //     color: "#f6f6f6",
  //   },
  // },
  // grid: {
  //   //布局
  //   show: true,
  //   left: "30px",
  //   right: "40px",
  //   bottom: "60px",
  //   top: "40px",
  // },
  series: [
    {
      name: "转速",
      type: "gauge",
      min: 0,
      max: 2100,
      splitNumber: 7,
      radius: "84%",
      axisLine: {
        // 坐标轴线
        lineStyle: {
          // 属性lineStyle控制线条样式
          color: [
            [0.09, "lime"],
            [0.82, "#1e90ff"],
            [1, "#ff4500"],
          ],
          width: 3,
          shadowColor: "#fff", //默认透明
          shadowBlur: 10,
        },
      },
      axisLabel: {
        // 坐标轴小标记
        fontWeight: "bolder",
        color: "#fff",
        shadowColor: "#fff", //默认透明
        shadowBlur: 10,
      },
      axisTick: {
        // 坐标轴小标记
        length: 15, // 属性length控制线长
        lineStyle: {
          // 属性lineStyle控制线条样式
          color: "auto",
          shadowColor: "#fff", //默认透明
          shadowBlur: 10,
        },
      },
      splitLine: {
        // 分隔线
        length: 25, // 属性length控制线长
        lineStyle: {
          // 属性lineStyle（详见lineStyle）控制线条样式
          width: 3,
          color: "#fff",
          shadowColor: "#fff", //默认透明
          shadowBlur: 10,
        },
      },
      pointer: {
        // 分隔线
        shadowColor: "#fff", //默认透明
        shadowBlur: 5,
      },
      title: {
        textStyle: {
          // 其余属性默认使用全局文本样式，详见TEXTSTYLE
          fontWeight: "bolder",
          fontSize: 20,
          fontStyle: "italic",
          color: "#fff",
          shadowColor: "#fff", //默认透明
          shadowBlur: 10,
        },
      },
      detail: {
        offsetCenter: [0, "60%"], // x, y，单位px
        textStyle: {
          fontWeight: "bolder",
          fontSize: 25,
          color: "#fff",
        },
      },
      data: [
        {
          value: 803,
          name: "rpm",
        },
      ],
    },
  ],
};

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
});
</script>
<style scoped lang="scss"></style>
