<template>
  <div class="edit-popup">
    <popup ref="popupRef" :title="popupTitle" :async="true" width="480px" @confirm="handleSubmit" @close="handleClose">
      <el-form ref="formRef" :model="formData" label-width="80px" :rules="formRules">
        <el-form-item label="所属类型" prop="pid">
          <el-select class="w-80" v-model="formData.pid" placeholder="请选择所属类型">
            <el-option label="控制端" :value="1" />
            <el-option label="被控端" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="设备名称" prop="name">
          <el-input class="w-80" v-model="formData.name" placeholder="请输入设备名称" clearable />
        </el-form-item>

        <el-form-item label="备注">
          <div class="w-80">
            <el-input
              v-model="formData.remarks"
              class="w-full"
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 4 }"
              maxlength="200"
              show-word-limit
              clearable
            />
          </div>
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>
<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import { deviceModelAdd, deviceModelDetail, deviceModelEdit } from "@/api/device";
import feedback from "@/utils/feedback";

const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "编辑设备" : "新增设备";
});

const formData = reactive({
  id: 0,
  pid: 1,
  name: "",
  remarks: "",
});

const formRules = {
  pid: [
    {
      required: true,
      message: "请选择所属类型",
      trigger: "change",
    },
  ],
  name: [
    {
      required: true,
      message: "请输入设备名称以及型号",
      trigger: "blur",
    },
  ],
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  mode.value == "edit" ? await deviceModelEdit(formData) : await deviceModelAdd(formData);
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = (data: Record<any, any>) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      //@ts-ignore
      formData[key] = data[key];
    }
  }
};

const getDetail = async (row: Record<string, any>) => {
  const data = await deviceModelDetail({
    id: row.id,
  });
  setFormData(data);
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
  getDetail,
});
</script>
