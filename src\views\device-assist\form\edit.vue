<template>
  <div class="edit-popup">
    <popup ref="popupRef" :title="popupTitle" :async="true" width="550px" @confirm="handleSubmit" @close="handleClose">
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item label="字段类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择" clearable @change="handleTypeChange">
            <el-option v-for="item in FORM_TYPE_LIST" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="isShowField" label="字段ID" prop="field">
          <el-input v-model="formData.field" placeholder="请输入字段ID" clearable />
        </el-form-item>
        <el-form-item v-if="isShowTitle" label="字段名称" prop="title">
          <el-input v-model="formData.title" placeholder="请输入字段名称" clearable />
        </el-form-item>

        <el-form-item label="是否显示" prop="display" @click.prevent="">
          <el-switch v-model="formData.display" />
        </el-form-item>
        <el-form-item v-if="isShowField" label="是否必填" prop="required">
          <el-switch v-model="formData.props.required" />
        </el-form-item>

        <el-form-item v-if="isShowInfo" label="提示信息" prop="info">
          <el-input v-model="formData.info" placeholder="请输入提示信息" clearable />
        </el-form-item>

        <el-form-item v-if="isShowDefaultValue" label="默认值" prop="defaultValue">
          <el-input
            v-if="formData.type === 'input' || formData.type === 'radio'"
            v-model="formData.props.defaultValue"
            placeholder="请输入默认值"
            clearable
          />

          <el-input-number
            v-if="formData.type === 'inputNumber'"
            v-model="formData.props.defaultValue"
            controls-position="right"
            placeholder="请输入默认值"
            clearable
          />

          <el-switch v-if="formData.type === 'switch'" v-model="formData.props.defaultValue" />
        </el-form-item>

        <el-form-item v-if="isShowMaxValue" label="最大值" prop="maxValue">
          <el-input-number v-model="formData.props.maxValue" controls-position="right" />
        </el-form-item>

        <el-form-item v-if="isShowMaxValue" label="最小值" prop="minValue">
          <el-input-number v-model="formData.props.minValue" controls-position="right" />
        </el-form-item>

        <el-form-item v-if="isShowOptionsValue" label="选项数据" prop="optionsValue">
          <el-table :data="formData.options" border>
            <el-table-column label="label" prop="label">
              <template #default="{ row }">
                <el-input v-model="row.label" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="value" label="选项值">
              <template #default="{ row }">
                <el-input v-model="row.value" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="disabled" label="是否禁用">
              <template #default="{ row }">
                <el-switch v-model="row.disabled" />
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button type="danger" link @click="handleDeleteOption(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-button link type="primary" @click="handleAddOption">添加选项</el-button>
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";
import { FORM_TYPE_LIST } from "@/utils/constants";

const emit = defineEmits(["edit", "add", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "编辑字段" : "新增字段";
});

const formData: any = reactive({
  display: true,
  props: {},
});

const isShowField = computed(() => {
  return !(formData.type === "title" || formData.type === "newline");
});

const isShowTitle = computed(() => {
  return formData.type !== "newline";
});

const isShowInfo = computed(() => {
  return formData.type == "input" || formData.type == "select";
});

const isShowDefaultValue = computed(() => {
  return (
    formData.type == "input" || formData.type == "inputNumber" || formData.type == "radio" || formData.type == "switch"
  );
});

const isShowMaxValue = computed(() => {
  return formData.type == "inputNumber";
});

const isShowOptionsValue = computed(() => {
  return formData.type == "radio" || formData.type == "select";
});

const formRules = reactive({
  title: [{ required: true, message: "请输入", trigger: ["blur"] }],
  type: [{ required: true, message: "请输入", trigger: ["blur"] }],
  field: [{ required: true, message: "请输入", trigger: ["blur"] }],
});

const handleAddOption = () => {
  if (formData.options == undefined) formData.options = [];
  formData.options.push({ label: "", value: "", disabled: false });
};

const handleDeleteOption = (row: any) => {
  const index = formData.options.indexOf(row);
  formData.options.splice(index, 1);
};

const handleTypeChange = async (val: string) => {
  if (val === "radio") {
    formData.options = [
      { label: "选项1", value: "1", disabled: false },
      { label: "选项2", value: "2", disabled: false },
    ];
  }
};

const handleSubmit = async () => {
  if (formData.props && formData.props.defaultValue) {
    formData.props.defaultValue = JSON.stringify({ value: formData.props.defaultValue });
  }

  await formRef.value?.validate();
  popupRef.value?.close();

  if (mode.value == "edit") {
    emit("edit", formData);
  } else {
    emit("add", formData);
  }
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (row: any) => {
  for (const key in row) {
    if (row[key] != null && row[key] != undefined) {
      formData[key] = row[key];
    }
  }

  if (row.props && row.props.defaultValue) {
    formData.props.defaultValue = JSON.parse(row.props.defaultValue).value;
  }

  await nextTick();
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
