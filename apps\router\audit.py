from typing import Annotated
from fastapi import APIRouter, Request, Query

from apps.common import permission_dp, unified_resp
import apps.services.audit as AService
import apps.models.audit as AModels
from apps.models.permissions import FuncId


router = APIRouter(prefix="/audit", tags=["审计日志"], dependencies=permission_dp(FuncId.AuditManage))


@router.get("/")
@unified_resp
async def list_all(_: Request, q: Annotated[AModels.Query, Query()]):
    """查询所有元数据"""
    return await AService.query(q)
