<script setup lang="ts">
import { onMounted, onUnmounted } from "vue";
import AMapLoader from "@amap/amap-jsapi-loader";
import device_icon from "@/assets/images/map_device.png";

let map: any = null;
let marker: any = null;
let customData: any = [];

onMounted(() => {
  //@ts-ignore
  window._AMapSecurityConfig = {
    securityJsCode: "163aa7cfca1e67b5d6eb627b2e014b57", // "安全密钥",
  };

  AMapLoader.load({
    key: "f0f7e58b7882db63bffab5fa22f8611c", // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: [
      "AMap.moveAnimation", // 动画插件
      "AMap.Scale", // 右下角缩略图插件 比例尺
      "AMap.MapType", // 地图类型切换插件，可用于切换卫星地图
      "AMap.Geolocation", // 定位控件，用来获取和展示用户主机所在的经纬度位置
      "AMap.Geocoder", // 逆地理编码,通过经纬度获取地址所在位置详细信息
    ], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  })
    .then((AMap) => {
      const path = [
        new AMap.LngLat(116.391935, 39.907517),
        new AMap.LngLat(116.390948, 39.92228),
        new AMap.LngLat(116.391677, 39.922988),
        new AMap.LngLat(116.402513, 39.923531),
        new AMap.LngLat(116.403307, 39.908011),
        new AMap.LngLat(116.391935, 39.907517),
      ];
      customData = [
        { position: path[0], duration: 100 },
        { position: path[1], duration: 4000 },
        { position: path[2], duration: 400 },
        { position: path[3], duration: 1500 },
        { position: path[4], duration: 2000 },
        { position: path[5], duration: 1000 },
      ];

      map = new AMap.Map("container", {
        viewMode: "3D", // 是否为3D地图模式
        zoom: 11, // 初始化地图级别
        center: [116.391935, 39.907517], // 初始化地图中心点位置
      });

      AMap.plugin("AMap.MoveAnimation", function () {
        marker = new AMap.Marker({
          position: new AMap.LngLat(116.391935, 39.907517),
          // angle: 90,
          map: map,
          icon: device_icon,
          offset: new AMap.Pixel(-100, -100),
          autoRotation: true,
          scale: 0.3,
        });
      });

      // 加载获取当前位置控件
      const geolocation = new AMap.Geolocation({
        position: "LB", // 定位按钮的停靠位置
        offset: [20, 30], // 定位按钮距离停靠位置的偏移量
      });
      map.addControl(geolocation);

      // 未行驶线条
      new AMap.Polyline({
        map: map,
        path: path,
        showDir: true,
        strokeColor: "#28F", //线颜色
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6, //线宽
        // strokeStyle: "solid"  //线样式
      });
      // 已行驶线条
      let passedPolyline = new AMap.Polyline({
        map: map,
        // path: path,
        strokeColor: "#AF5", //线颜色
        // strokeOpacity: 1,     //线透明度
        strokeWeight: 6, //线宽
        // strokeStyle: "solid"  //线样式
      });

      marker.on("moving", function (e: { passedPath: any }) {
        passedPolyline.setPath(e.passedPath);
      });

      map?.setFitView();
    })
    .catch((e) => {
      console.log(e);
    });
});

onUnmounted(() => {
  map?.destroy();
});

const play = () => {
  marker.moveAlong(customData);
};

setTimeout(() => {
  play();
}, 1000);

defineExpose({
  play,
});
</script>

<template>
  <div id="container" style="height: calc(100vh - 218px)"></div>
</template>

<style scoped>
#container {
  width: 100%;
}
</style>
