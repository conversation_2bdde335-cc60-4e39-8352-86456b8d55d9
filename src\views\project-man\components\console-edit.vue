<template>
  <div class="edit-popup">
    <popup ref="popupRef" :title="popupTitle" :async="true" width="450px" @confirm="handleSubmit" @close="handleClose">
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item label="操作台名称" prop="console_name">
          <el-input v-model="formData.console_name" placeholder="请输入操作台名称" clearable />
        </el-form-item>
        <el-form-item label="操作台类型" prop="console_type">
          <el-select v-model="formData.console_type" placeholder="请选择操作台类型" clearable>
            <el-option v-for="item in consoleTypeList" :key="item.flag" :label="item.name" :value="Number(item.flag)" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作台SN" prop="console_sn">
          <el-input v-model="formData.console_sn" placeholder="请输入操作台SN" clearable />
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";
import useMetadataStore from "@/stores/modules/metadata";
import { projectAddConsole, projectEditConsole } from "@/api/project-man";

const metadataStore = useMetadataStore();
const props = defineProps({ id: String });
const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "编辑操作台" : "添加操作台";
});

const formData: any = reactive({
  console_id: "",
  console_name: "",
  console_type: 1,
  console_sn: "",
});

const formRules = reactive({
  console_name: [
    {
      required: true,
      message: "请输入操作台",
      trigger: ["blur"],
    },
  ],
});

const consoleTypeList: any = ref([]);

onMounted(() => {
  getConsoleTypeList();
});

const getConsoleTypeList = async () => {
  consoleTypeList.value = await metadataStore.fetchMetadata("VEHICLE_TYPE");
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  mode.value == "edit"
    ? await projectEditConsole({ id: props.id, ...formData })
    : await projectAddConsole({ id: props.id, ...formData });
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (data: any) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>

<style scoped lang="scss"></style>
