<template>
  <el-card v-if="formData.from_platform === 'web'" class="!border-none" shadow="never">
    <el-form class="ls-form" :model="formData" inline>
      <el-form-item label="车辆ID">
        <el-input class="w-[220px]" placeholder="请输入" v-model="formData.vehicle_id" clearable />
      </el-form-item>

      <el-form-item label="记录时间">
        <el-date-picker
          style="width: 180px"
          v-if="activeTab === 'daily'"
          v-model="selectedDate"
          type="date"
          placeholder="选择日期"
          :clearable="false"
        />
        <el-date-picker
          style="width: 180px"
          v-if="activeTab === 'monthly'"
          v-model="selectedMonth"
          type="month"
          placeholder="选择月份"
          :clearable="false"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="resetPageFormat">查询</el-button>
        <el-button @click="resetParams">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
  <el-card class="!border-none mt-2 relative" shadow="never">
    <el-button
      v-if="formData.from_platform === 'web'"
      class="absolute z-[999] top-[26px] right-10"
      type="primary"
      @click="exportToPDF"
    >
      生成当{{ activeTab === "daily" ? "日" : "月" }}报告
    </el-button>
    <el-tabs class="" v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="按日统计" name="daily">
        <div
          class="stats-container daily-stats"
          :class="{ 'stats-container-height': formData.from_platform === 'web' }"
        >
          <el-row :gutter="20" class="summary-cards mb-4">
            <el-col :span="8">
              <el-card shadow="hover">
                <div class="card-header">装车总数</div>
                <div class="card-body">{{ dailyStats.totalLoads }} 车</div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <div class="card-header">平均装车时间</div>
                <div class="card-body">{{ dailyStats.avgLoadTime }} 分钟/车</div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <div class="card-header">连续工作时长</div>
                <div class="card-body">
                  {{ formatWorkTime(dailyStats.normal_work_time_minutes) }}
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="mb-4">
            <el-col :span="12">
              <el-card shadow="hover">
                <div
                  ref="workTimePieChart"
                  data-chart-ref="workTimePieChart"
                  style="width: 100%; height: 330px"
                ></div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="hover" style="height: 370px">
                <template #header>
                  <div class="card-header">
                    <span class="text-[#000] text-[18px] font-extrabold">工作报告</span>
                  </div>
                </template>
                <div class="report-content">
                  <div class="report-item">
                    <span class="report-label">正常工作时长</span>
                    <span class="report-value">{{
                      formatWorkTime(dailyStats.normal_work_time_minutes)
                    }}</span>
                  </div>
                  <div class="report-item">
                    <span class="report-label">故障检修时长</span>
                    <span class="report-value"
                      >{{ formatWorkTime(dailyStats.trouble_shooting_time_minutes) }}
                    </span>
                  </div>
                  <div class="report-item">
                    <span class="report-label">闲置时长</span>
                    <span class="report-value">{{
                      formatWorkTime(dailyStats.idle_time_minutes)
                    }}</span>
                  </div>
                  <div class="report-item">
                    <span class="report-label">装车数量</span>
                    <span class="report-value">{{ dailyStats.totalLoads }} 车</span>
                  </div>
                  <el-divider class="my-2" />
                  <div class="report-summary">
                    <p class="text-gray-600">
                      报告摘要：本日总有效工作时间为
                      {{ formatWorkTime(dailyStats.normal_work_time_minutes) }}，期间共完成
                      {{ dailyStats.totalLoads }} 次装车作业。平均装车耗时
                      {{ dailyStats.avgLoadTime }} 分钟。请关注闲置与故障时长，持续优化作业效率。
                    </p>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-card shadow="hover" class="my-4">
            <div
              ref="timelineChart"
              data-chart-ref="timelineChart"
              style="width: 100%; height: 200px"
            ></div>
          </el-card>
          <el-card shadow="hover">
            <div
              ref="dailyChart"
              data-chart-ref="dailyChart"
              style="width: 100%; height: 330px"
            ></div>
          </el-card>
          <el-card shadow="hover" class="my-4">
            <div class="text-[#000] text-[18px] font-extrabold text-center mb-3">
              每日班组装车统计
            </div>

            <el-table :data="dailyStats.team_info" size="large">
              <el-table-column label="班组名称" prop="group_name" align="center" />
              <el-table-column label="装车数" prop="count" align="center" />
              <el-table-column label="最后更新时间" prop="latest_timestamp" align="center">
                <template #default="{ row }">
                  {{ dayjs(row.latest_timestamp).local().format("YYYY-MM-DD HH:mm:ss") }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="按月统计" name="monthly">
        <div
          class="stats-container monthly-stats"
          :class="{ 'stats-container-height': formData.from_platform === 'web' }"
        >
          <el-row :gutter="20" class="summary-cards mb-4">
            <el-col :span="8">
              <el-card shadow="hover">
                <div class="card-header">装车总数</div>
                <div class="card-body">{{ monthlyStats.totalLoads }} 车</div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <div class="card-header">平均装车时间</div>
                <div class="card-body">{{ monthlyStats.avgLoadTime }} 分钟/车</div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="hover">
                <div class="card-header">每日平均装车数量</div>
                <div class="card-body">{{ monthlyStats.avgDailyLoads }} 车/天</div>
              </el-card>
            </el-col>
          </el-row>
          <el-card shadow="hover">
            <div
              ref="monthlyChart"
              data-chart-ref="monthlyChart"
              style="width: 100%; height: 330px"
            ></div>
          </el-card>
          <el-card shadow="hover" class="my-4">
            <div class="text-[#000] text-[18px] font-extrabold text-center mb-3">
              每月班组装车统计
            </div>

            <el-table :data="monthlyStats.team_info" size="large">
              <el-table-column label="班组名称" prop="group_name" align="center" />
              <el-table-column label="装车数" prop="count" align="center" />
              <el-table-column label="最后更新时间" prop="latest_timestamp" align="center">
                <template #default="{ row }">
                  {{ dayjs(row.latest_timestamp).local().format("YYYY-MM-DD HH:mm:ss") }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { useRoute } from "vue-router";
import * as echarts from "echarts";
import { getDailyStats, getMonthlyStats } from "@/api/prod-count";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import useUserStore from "@/stores/modules/user";
import cache from "@/utils/cache";

const userStore = useUserStore();

const formatWorkTime = (minutes) => {
  if (minutes === undefined || minutes === null) {
    return "N/A";
  }
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  return `${hours}时${mins}分`;
};

const activeTab = ref("daily");

const route = useRoute();

const formData = ref({
  vehicle_id: route.query.vehicle_id || "66f135aa8ea96a77201f53e9",
  from_platform: route.query.from_platform || "web",
});
const selectedDate = ref(new Date());
const selectedMonth = ref(new Date());

const dailyChart = ref(null);
const monthlyChart = ref(null);
const workTimePieChart = ref(null);
const timelineChart = ref(null);
let dailyChartInstance = null;
let monthlyChartInstance = null;
let workTimePieChartInstance = null;
let timelineChartInstance = null;

const exportToPDF = () => {
  const elementToCapture = document.querySelector(`.${activeTab.value}-stats`);
  if (!elementToCapture) return;

  const clone = elementToCapture.cloneNode(true);

  const title = document.createElement("div");
  const titleText =
    activeTab.value === "daily"
      ? `${selectedDate.value.toISOString().split("T")[0]} 装车统计`
      : `${selectedMonth.value.getFullYear()}-${String(selectedMonth.value.getMonth() + 1).padStart(
          2,
          "0"
        )} 装车统计`;
  title.innerText = titleText;
  title.style.textAlign = "center";
  title.style.fontSize = "28px";
  title.style.fontWeight = "bold";
  title.style.marginBottom = "40px";

  clone.insertBefore(title, clone.firstChild);

  clone.style.position = "absolute";
  clone.style.top = "0px";
  clone.style.left = "-9999px";
  clone.style.width = "1000px";
  clone.style.margin = "0 auto";
  clone.style.height = `${elementToCapture.scrollHeight}px`;
  clone.style.overflow = "visible";

  const chartRefs = {
    daily: ["dailyChart", "workTimePieChart", "timelineChart"],
    monthly: ["monthlyChart"],
  };

  const activeChartRefs = chartRefs[activeTab.value] || [];
  const chartInstances = {
    dailyChart: dailyChartInstance,
    workTimePieChart: workTimePieChartInstance,
    timelineChart: timelineChartInstance,
    monthlyChart: monthlyChartInstance,
  };

  activeChartRefs.forEach((refName) => {
    const chartInstance = chartInstances[refName];
    if (chartInstance) {
      const dataUrl = chartInstance.getDataURL({
        type: "png",
        pixelRatio: 2,
        backgroundColor: "#fff",
      });
      const img = new Image();
      img.src = dataUrl;
      img.style.width = "100%";
      if (chartInstance === timelineChartInstance) {
        img.style.height = "200px";
      } else {
        img.style.height = "330px";
      }

      const chartContainer = clone.querySelector(`[data-chart-ref="${refName}"]`);
      if (chartContainer) {
        chartContainer.innerHTML = "";
        chartContainer.appendChild(img);
      }
    }
  });

  const tableCells = clone.querySelectorAll(".el-table__body-wrapper tbody tr td .cell");
  tableCells.forEach((cell) => {
    cell.style.display = "flex";
    cell.style.alignItems = "center";
    cell.style.justifyContent = "center";
    cell.style.height = "48px";
  });

  document.body.appendChild(clone);

  html2canvas(clone, {
    useCORS: true,
    width: 1000,
    height: elementToCapture.scrollHeight + title.offsetHeight,
    useCORS: true,
  })
    .then((canvas) => {
      document.body.removeChild(clone);

      const contentWidth = canvas.width;
      const contentHeight = canvas.height;

      const A4_PAGE_WIDTH = 210;
      const A4_PAGE_HEIGHT = 297;
      const PAGE_MARGIN = 5;

      const pdf = new jsPDF("p", "mm", "a4");

      const pdfWidth = A4_PAGE_WIDTH - PAGE_MARGIN * 2;
      const pdfHeight = (pdfWidth * contentHeight) / contentWidth;
      const imgData = canvas.toDataURL("image/png", 1.0);

      let heightLeft = pdfHeight;
      let position = 0;

      pdf.addImage(imgData, "PNG", PAGE_MARGIN, PAGE_MARGIN, pdfWidth, pdfHeight);
      heightLeft -= A4_PAGE_HEIGHT - PAGE_MARGIN * 2;

      while (heightLeft > 0) {
        position -= A4_PAGE_HEIGHT - PAGE_MARGIN * 2;
        pdf.addPage();
        pdf.addImage(imgData, "PNG", PAGE_MARGIN, position, pdfWidth, pdfHeight);
        heightLeft -= A4_PAGE_HEIGHT - PAGE_MARGIN * 2;
      }

      pdf.save(`${activeTab.value}-report.pdf`);
    })
    .catch((err) => {
      document.body.removeChild(clone);
      console.error("PDF export failed:", err);
    });
};

const dailyStats = ref({
  totalLoads: 0,
  avgLoadTime: 0,
  chartData: {
    hours: [],
    counts: [],
  },
  team_info: [],
  timelineData: [],
});

const monthlyStats = ref({
  totalLoads: 0,
  avgLoadTime: 0,
  avgDailyLoads: 0,
  chartData: {
    days: [],
    counts: [],
  },
  team_info: [],
});

const fetchDailyData = async () => {
  try {
    const dateStr = selectedDate.value.toISOString().split("T")[0];
    const data = await getDailyStats({ date: dateStr, vehicle_id: formData.value.vehicle_id });

    console.log(data);

    const hours = Array.from({ length: 24 }, (_, i) => `${i + 1}:00`);

    dailyStats.value = {
      totalLoads: data.total_loads,
      avgLoadTime: data.avg_load_time,
      chartData: {
        hours: hours,
        counts: data.hourly_counts,
      },
      normal_work_time_minutes: data.normal_work_time_minutes,
      trouble_shooting_time_minutes: data.trouble_shooting_time_minutes,
      idle_time_minutes: data.idle_time_minutes,
      team_info: data.team_info,
      timelineData: data.timeline_data.map((item, index) => ({
        ...item,
        id: index,
      })),
    };
    updateDailyChart();
    updateWorkTimePieChart();
  } catch (error) {
    console.error("Failed to fetch daily stats:", error);
  }
};

const resetPageFormat = () => {
  if (activeTab.value === "daily") {
    fetchDailyData();
  } else {
    fetchMonthlyData();
  }
};

const resetParams = () => {
  formData.value.vehicle_id = "";
  selectedDate.value = new Date();
  selectedMonth.value = new Date();
  resetPageFormat();
};

const fetchMonthlyData = async () => {
  try {
    const year = selectedMonth.value.getFullYear();
    const month = selectedMonth.value.getMonth() + 1;
    const monthStr = `${year}-${String(month).padStart(2, "0")}`;
    const data = await getMonthlyStats({ month: monthStr, vehicle_id: formData.value.vehicle_id });

    const days = Array.from({ length: data.days_in_month }, (_, i) => i + 1);

    monthlyStats.value = {
      totalLoads: data.total_loads,
      avgLoadTime: data.avg_load_time,
      avgDailyLoads: data.avg_daily_loads,
      chartData: {
        days: days,
        counts: data.daily_counts,
      },
      team_info: data.team_info,
    };
    updateMonthlyChart();
  } catch (error) {
    console.error("Failed to fetch monthly stats:", error);
  }
};

const initDailyChart = () => {
  if (dailyChart.value) {
    dailyChartInstance = echarts.init(dailyChart.value);
    updateDailyChart();
  }
};

const initMonthlyChart = () => {
  if (monthlyChart.value) {
    monthlyChartInstance = echarts.init(monthlyChart.value);
    updateMonthlyChart();
  }
};

const initWorkTimePieChart = () => {
  if (workTimePieChart.value) {
    workTimePieChartInstance = echarts.init(workTimePieChart.value);
    updateWorkTimePieChart();
  }
};

const initTimelineChart = () => {
  if (timelineChart.value) {
    timelineChartInstance = echarts.init(timelineChart.value);
    updateTimelineChart();
  }
};

const getBaseChartOptions = (title, xAxisData, seriesData, xName, yName) => ({
  title: {
    text: title,
    left: "center",
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    data: xAxisData,
    name: xName,
    axisTick: {
      alignWithLabel: true,
    },
  },
  yAxis: {
    type: "value",
    name: yName,
  },
  series: [
    {
      data: seriesData,
      type: "bar",
      label: {
        show: true,
        position: "top",
        formatter: "{c}车",
      },
    },
  ],
});

const updateDailyChart = () => {
  if (!dailyChartInstance) return;
  dailyChartInstance.setOption(
    getBaseChartOptions(
      "每日装车数量趋势 (按小时)",
      dailyStats.value.chartData.hours,
      dailyStats.value.chartData.counts,
      "",
      "装车数量"
    )
  );
};

const updateTimelineChart = () => {
  if (!timelineChartInstance) return;

  const date = selectedDate.value.toISOString().split("T")[0];

  function parseTimeToTimestamp(t) {
    if (typeof t === "number") return t;
    if (typeof t !== "string") return NaN;

    if (/^\d{1,2}:\d{2}(:\d{2})?$/.test(t)) {
      return new Date(date + " " + t).getTime();
    }

    const ts = new Date(t).getTime();
    return isNaN(ts) ? NaN : ts;
  }

  const seriesData = dailyStats.value.timelineData
    .map((a) => {
      const startTs = parseTimeToTimestamp(a.start);
      const endTs = parseTimeToTimestamp(a.end);

      if (isNaN(startTs) || isNaN(endTs) || startTs >= endTs) {
        console.warn("Invalid time item skipped:", a);
        return null;
      }

      return [startTs, endTs, 0, a.color || null, a.label || a.name, a];
    })
    .filter(Boolean);

  const option = {
    title: {
      text: `今日工作状态详情`,
      left: "center",
    },
    tooltip: {
      trigger: "item",
      renderMode: "html",
      formatter: function (params) {
        const item = params.data[5] || {};
        const start = new Date(params.data[0]);
        const end = new Date(params.data[1]);

        function fmt(d) {
          return (
            d.getHours().toString().padStart(2, "0") +
            ":" +
            d.getMinutes().toString().padStart(2, "0")
          );
        }

        return [
          "<strong>" + (item.name || params.data[4]) + "</strong>",
          '<div style="margin-top:6px">' + fmt(start) + " — " + fmt(end) + "</div>",
        ].join("");
      },
    },
    grid: { left: 80, right: 40, top: 40, bottom: 50 },
    xAxis: {
      type: "time",
      min: new Date(date + " 00:00").getTime(),
      max: new Date(date + " 23:59:59").getTime(),
      axisLabel: {
        formatter: function (val) {
          const d = new Date(val);
          return d.getHours().toString().padStart(2, "0") + ":00";
        },
      },
      splitLine: { show: true },
    },
    yAxis: {
      type: "category",
      data: [""],
      inverse: true,
      axisTick: { show: false },
      axisLabel: { inside: false },
    },
    legend: {
      show: true,
      top: 10,
      left: "right",
      data: dailyStats.value.timelineData.map((a) => {
        return {
          name: a.name,
          itemStyle: {
            color: a.color,
          },
        };
      }),
    },
    series: dailyStats.value.timelineData.map((activity) => ({
      name: activity.name,
      type: "custom",
      renderItem: function (params, api) {
        const start = api.value(0);
        const end = api.value(1);
        const yIndex = api.value(2);

        const startCoord = api.coord([start, yIndex]);
        const endCoord = api.coord([end, yIndex]);

        const height = api.size([0, 1])[1] * 0.6;

        const x = startCoord[0];
        const y = startCoord[1] - height / 2;
        const width = Math.max(0, endCoord[0] - startCoord[0]);

        if (width <= 1) {
          return;
        }

        return {
          type: "rect",
          shape: {
            x: x,
            y: y,
            width: width,
            height: height,
            r: Math.min(6, height / 2),
          },
          style: api.style({
            fill: activity.color,
          }),
        };
      },
      encode: { x: [0, 1], y: 2 },
      data: seriesData.filter((d) => d[5].id === activity.id),
    })),
  };

  timelineChartInstance.setOption(option);
};

const updateWorkTimePieChart = () => {
  if (!workTimePieChartInstance) return;

  const pieData = [
    {
      value: dailyStats.value.normal_work_time_minutes || 0,
      name: "正常工作",
    },
    {
      value: dailyStats.value.trouble_shooting_time_minutes || 0,
      name: "故障检修",
    },
    { value: dailyStats.value.idle_time_minutes || 0, name: "等待装车" },
  ];

  workTimePieChartInstance.setOption({
    title: {
      text: "每日工作时间分配",
      left: "center",
    },
    tooltip: {
      trigger: "item",
      formatter: (params) => {
        return `${params.seriesName}<br/>${params.name}: ${formatWorkTime(params.value)} (${
          params.percent
        }%)`;
      },
    },
    legend: {
      orient: "vertical",
      left: "left",
      top: "10%",
    },
    series: [
      {
        name: "时间占比",
        type: "pie",
        radius: "50%",
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  });
};

const updateMonthlyChart = () => {
  if (!monthlyChartInstance) return;
  monthlyChartInstance.setOption(
    getBaseChartOptions(
      "每月装车数量趋势 (按天)",
      monthlyStats.value.chartData.days,
      monthlyStats.value.chartData.counts,
      "",
      "装车数量"
    )
  );
};

onMounted(() => {
  const token = "Bearer t-926a5bcade9631b0c3ae067805fcd7ac6e390cba9c0a";
  console.log("token", token);

  // 之后从安卓中获取token
  if (token) {
    userStore.token = token;
    cache.set("token", token);
  }

  if (formData.value.vehicle_id) {
    fetchDailyData().then(() => {
      initDailyChart();
      initWorkTimePieChart();
      initTimelineChart();
    });
  } else {
    initDailyChart();
    initWorkTimePieChart();
  }

  window.addEventListener("resize", () => {
    dailyChartInstance?.resize();
    monthlyChartInstance?.resize();
    workTimePieChartInstance?.resize();
  });
});

const handleTabClick = (tab) => {
  if (!formData.value.vehicle_id) {
    return;
  }

  nextTick(() => {
    if (tab.props.name === "daily") {
      if (!dailyChartInstance) {
        fetchDailyData().then(() => {
          initDailyChart();
          initWorkTimePieChart();
        });
      }
      if (!timelineChartInstance) {
        initTimelineChart();
      }
    } else if (tab.props.name === "monthly" && !monthlyChartInstance) {
      fetchMonthlyData().then(() => initMonthlyChart());
    }
  });
};
</script>

<style scoped>
.stats-container {
  padding: 1rem;
  overflow-y: auto;
  height: calc(100vh - 100px);
}

.stats-container-height {
  height: calc(100vh - 240px);
}

.summary-cards .el-card {
  text-align: center;
}
.card-header {
  color: #909399;
  margin-bottom: 10px;
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 10px;
}
.card-body {
  font-size: 24px;
  font-weight: bold;
}

.stats-container .card-header {
  font-weight: bold;
  font-size: 16px;
}

.stats-container .card-body {
  font-size: 24px;
  text-align: center;
  color: #333;
}
.report-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.report-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.report-label {
  font-weight: bold;
  color: #303133;
}

.report-value {
  color: #606266;
}

.report-summary {
  font-size: 13px;
  color: #909399;
  line-height: 1.6;
}
</style>
