import os
import csv
import json
import inspect
import logging
from collections import namedtuple
from datetime import datetime
from functools import wraps
from typing import Callable, Generic, TypeVar, Union

import arrow
from bson import ObjectId
from pydantic import BaseModel, Field
from fastapi import Request
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse

from apps.config import get_settings
from apps.utils import NameStyleConverter as NSC


__all__ = ["HttpCode", "HttpResp", "unified_resp"]


logger = logging.getLogger(__name__)

RT = TypeVar("RT")


HttpCodeV1 = namedtuple("HttpCodeV1", ["code", "msg"])


class HttpRespV1:
    """HTTP"""

    SUCCESS = HttpCodeV1(200, "success")
    FAILED = HttpCodeV1(300, "failed")

    PARAMS_VALID_ERROR = HttpCodeV1(310, "params valid error")
    TIME_FORMAT_ERROR = HttpCodeV1(310, "time format error")
    PARAMS_TYPE_ERROR = HttpCodeV1(311, "params type error")
    REQUEST_METHOD_ERROR = HttpCodeV1(312, "request method error")
    ASSERT_ARGUMENT_ERROR = HttpCodeV1(313, "assert argument error")
    TIME_RANGE_ERROR = HttpCodeV1(313, "time range error")
    INVALID_ID_ERROR = HttpCodeV1(310, "invalid id")

    TOKEN_EMPTY = HttpCodeV1(401, "token params empty")
    TOKEN_INVALID = HttpCodeV1(401, "token invalid")
    LOGIN_ACCOUNT_ERROR = HttpCodeV1(401, "user auth fail")
    LOGIN_DISABLE_ERROR = HttpCodeV1(401, "login disable")
    PASSWORD_INCORRECT = HttpCodeV1(403, "Password incorrect.")
    NO_PERMISSION = HttpCodeV1(403, "no permission")
    UNTRUSTED_DEVICE = HttpCodeV1(403, "Untrusted device")
    VEHICLE_LOCKED_BY_OTHER = HttpCodeV1(412, "vehicle locked by other")

    DEVICE_NOT_ONLINE = HttpCodeV1(404, "device not online")
    REQUEST_404_ERROR = HttpCodeV1(404, "request 404 error")
    USER_NOT_EXIST = HttpCodeV1(404, "user not exist")
    ROLE_NOT_EXIST = HttpCodeV1(404, "role not exist")
    MENU_NOT_EXIST = HttpCodeV1(404, "menu not exist")
    VEHICLE_NOT_FOUND = HttpCodeV1(404, "vehicle not found")
    DEVICE_NOT_FOUND = HttpCodeV1(404, "device not found")
    PROGRAM_NODE_NOT_FOUND = HttpCodeV1(404, "program node not found")
    OPC_NOT_FOUND = HttpCodeV1(404, "op console not found")
    METADATA_NOT_FOUND = HttpCodeV1(404, "metadata not found")
    VIDEO_NOT_FOUND = HttpCodeV1(404, "video not found")
    CAMERA_NOT_FOUND = HttpCodeV1(404, "camera device not found")
    TEMPLATE_NOT_FOUND = HttpCodeV1(404, "mix template not found")
    HLS_URL_NOT_FOUND = HttpCodeV1(404, "hls url not found")
    API_KEY_NOT_FOUND = HttpCodeV1(404, "hls url not found")

    DEVICE_ID_EXISTS = HttpCodeV1(412, "device id exists")
    METADATA_EXISTS = HttpCodeV1(412, "metadata exists")
    PROGRAM_NODE_EXISTS = HttpCodeV1(412, "program node exists")
    OPC_NAME_EXISTS = HttpCodeV1(412, "op console name exists")
    USER_REPEAT = HttpCodeV1(412, "username or email repeat")
    ROLE_NAME_REPEAT = HttpCodeV1(412, "role name repeat")
    MENU_NAME_REPEAT = HttpCodeV1(412, "menu name repeat")
    VEHICLE_NAME_REPEAT = HttpCodeV1(412, "vehicle name repeat")
    DEVICE_NAME_REPEAT = HttpCodeV1(412, "device name repeat")
    TEMPLATE_NAME_EXISTS = HttpCodeV1(412, "template name exists")

    API_KEY_CREATE_FAILED = HttpCodeV1(500, "api key create failed")
    UNKNOWN_CMD = HttpCodeV1(500, "unknown cmd")
    UNKNOWN_TYPE = HttpCodeV1(500, "unknown type")
    REDIS_ERROR = HttpCodeV1(500, "Redis error")
    SYSTEM_ERROR = HttpCodeV1(500, "system error")
    HLS_ERROR = HttpCodeV1(500, "hls server error")
    SYSTEM_TIMEOUT_ERROR = HttpCodeV1(504, "system timeout error")
    VEHICLE_HAS_DEVICE = HttpCodeV1(500, "vehicle has device")


def read_err_msg():
    """读取错误码对应消息文件"""
    path = os.path.join(get_settings().root_path, "resources/error_code.csv")
    result = {}

    with open(path, newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            code = int(row["code"])
            result[code] = {header: row[header] for header in row if header != "code"}

    return result


class HttpCodeV2:
    _msg = read_err_msg()

    def __init__(self, code: int, v1: HttpCodeV1 = HttpRespV1.FAILED) -> None:
        self.code = code
        self.cmd = self._msg[self.code]["cmd"]
        self.msg = self._msg[self.code]["en"]
        self.msg_all = self._msg[self.code]
        self.v1 = v1

    def content(self, version="v2", lang="en"):
        if version == "v1":
            return {
                "code": self.v1.code,
                "msg": self.v1.msg,
                "data": {},
            }

        try:
            msg = self.msg_all[lang]
        except KeyError:
            msg = self.msg

        return {
            "code": self.code,
            "msg": msg,
            "data": {},
        }


class HttpRespV2:
    # 1000-1999: General
    SUCCESS = HttpCodeV2(1000, HttpRespV1.SUCCESS)
    FAILED = HttpCodeV2(1001, HttpRespV1.FAILED)

    # 2000-2999: Parameter/Request errors
    PARAM_INVALID = HttpCodeV2(2001, HttpRespV1.PARAMS_VALID_ERROR)
    PARAM_TYPE_ERROR = HttpCodeV2(2002, HttpRespV1.PARAMS_TYPE_ERROR)
    REQUEST_METHOD_ERROR = HttpCodeV2(2003, HttpRespV1.REQUEST_METHOD_ERROR)
    ASSERT_ARGUMENT_ERROR = HttpCodeV2(2004, HttpRespV1.ASSERT_ARGUMENT_ERROR)
    TIME_FORMAT_ERROR = HttpCodeV2(2005, HttpRespV1.TIME_FORMAT_ERROR)
    TIME_RANGE_ERROR = HttpCodeV2(2006, HttpRespV1.TIME_RANGE_ERROR)
    INVALID_ID = HttpCodeV2(2007, HttpRespV1.INVALID_ID_ERROR)

    # 3000-3999: Authentication/Permission
    TOKEN_EMPTY = HttpCodeV2(3001, HttpRespV1.TOKEN_EMPTY)
    TOKEN_INVALID = HttpCodeV2(3002, HttpRespV1.TOKEN_INVALID)
    USER_AUTH_FAIL = HttpCodeV2(3003, HttpRespV1.LOGIN_ACCOUNT_ERROR)
    LOGIN_DISABLED = HttpCodeV2(3004, HttpRespV1.LOGIN_DISABLE_ERROR)
    PASSWORD_INCORRECT = HttpCodeV2(3005, HttpRespV1.PASSWORD_INCORRECT)
    NO_PERMISSION = HttpCodeV2(3006, HttpRespV1.NO_PERMISSION)
    UNTRUSTED_DEVICE = HttpCodeV2(3007, HttpRespV1.UNTRUSTED_DEVICE)
    API_KEY_CREATE_FAIL = HttpCodeV2(3008, HttpRespV1.API_KEY_CREATE_FAILED)

    # 4000-4999: Resource Not Found
    RESOURCE_NOT_FOUND = HttpCodeV2(4001, HttpRespV1.REQUEST_404_ERROR)
    DEVICE_OFFLINE = HttpCodeV2(4002, HttpRespV1.DEVICE_NOT_ONLINE)
    USER_NOT_EXIST = HttpCodeV2(4003, HttpRespV1.USER_NOT_EXIST)
    ROLE_NOT_EXIST = HttpCodeV2(4004, HttpRespV1.ROLE_NOT_EXIST)
    MENU_NOT_EXIST = HttpCodeV2(4005, HttpRespV1.MENU_NOT_EXIST)
    VEHICLE_NOT_FOUND = HttpCodeV2(4006, HttpRespV1.VEHICLE_NOT_FOUND)
    DEVICE_NOT_FOUND = HttpCodeV2(4007, HttpRespV1.DEVICE_NOT_FOUND)
    PROGRAM_NODE_NOT_FOUND = HttpCodeV2(4008, HttpRespV1.PROGRAM_NODE_NOT_FOUND)
    OP_CONSOLE_NOT_FOUND = HttpCodeV2(4009, HttpRespV1.OPC_NOT_FOUND)
    METADATA_NOT_FOUND = HttpCodeV2(4010, HttpRespV1.METADATA_NOT_FOUND)
    VIDEO_NOT_FOUND = HttpCodeV2(4011, HttpRespV1.VIDEO_NOT_FOUND)
    CAMERA_NOT_FOUND = HttpCodeV2(4012, HttpRespV1.CAMERA_NOT_FOUND)
    MIX_TEMPLATE_NOT_FOUND = HttpCodeV2(4013, HttpRespV1.TEMPLATE_NOT_FOUND)
    HLS_NOT_FOUND = HttpCodeV2(4014, HttpRespV1.HLS_URL_NOT_FOUND)
    HLS_SERVER_ERROR = HttpCodeV2(4015, HttpRespV1.HLS_ERROR)
    VEHICLE_LOCKED = HttpCodeV2(4016, HttpRespV1.VEHICLE_LOCKED_BY_OTHER)

    # 5000-5999: Resource Conflict
    DEVICE_ID_EXISTS = HttpCodeV2(5001, HttpRespV1.DEVICE_ID_EXISTS)
    METADATA_EXISTS = HttpCodeV2(5002, HttpRespV1.METADATA_EXISTS)
    PROGRAM_NODE_EXISTS = HttpCodeV2(5003, HttpRespV1.PROGRAM_NODE_EXISTS)
    OP_CONSOLE_NAME_EXISTS = HttpCodeV2(5004, HttpRespV1.OPC_NAME_EXISTS)
    USERNAME_OR_EMAIL_EXISTS = HttpCodeV2(5005, HttpRespV1.USER_REPEAT)
    ROLE_NAME_EXISTS = HttpCodeV2(5006, HttpRespV1.ROLE_NAME_REPEAT)
    MENU_NAME_EXISTS = HttpCodeV2(5007, HttpRespV1.MENU_NAME_REPEAT)
    VEHICLE_NAME_EXISTS = HttpCodeV2(5008, HttpRespV1.VEHICLE_NAME_REPEAT)
    DEVICE_NAME_EXISTS = HttpCodeV2(5009, HttpRespV1.DEVICE_NAME_REPEAT)
    TEMPLATE_NAME_EXISTS = HttpCodeV2(5010, HttpRespV1.TEMPLATE_NAME_EXISTS)
    VEHICLE_HAS_DEVICE = HttpCodeV2(5011, HttpRespV1.VEHICLE_HAS_DEVICE)

    # 6000-6999: System Errors
    UNKNOWN_ERROR = HttpCodeV2(6001, HttpRespV1.UNKNOWN_CMD)
    UNKNOWN_TYPE = HttpCodeV2(6002, HttpRespV1.UNKNOWN_TYPE)
    UNKNOWN_CMD = HttpCodeV2(6003, HttpRespV1.UNKNOWN_CMD)
    SYSTEM_ERROR = HttpCodeV2(6004, HttpRespV1.SYSTEM_ERROR)
    SYSTEM_TIMEOUT = HttpCodeV2(6005, HttpRespV1.SYSTEM_TIMEOUT_ERROR)
    HLS_SERVER_ERROR = HttpCodeV2(6006, HttpRespV1.HLS_ERROR)

    @staticmethod
    def check_msg():
        """检查错误码是否正确"""
        count = 0
        for name, obj in inspect.getmembers(HttpRespV2):
            if not isinstance(obj, HttpCodeV2):
                continue
            if name != obj.cmd:
                count += 1
                logger.warning(
                    f"Warning: HttpRespV2 instance name '{name}' does not match cmd attribute '{obj.cmd}' for code {obj.code}"
                )
        if count > 0:
            logger.error(f"Error: Total {count} errors found in HttpRespV2")
            raise RuntimeError(f"Error: Total {count} errors found in HttpRespV2")


HttpRespV2.check_msg()

HttpCode = HttpCodeV2
HttpResp = HttpRespV2


def unified_resp(func: Callable[..., RT]):
    """统一响应格式
    接口正常返回时,统一响应结果格式
    """

    def datetime_encoder(dt: datetime):
        """日期时间格式化"""
        return arrow.get(dt).isoformat()

    def objectid_encoder(id_: ObjectId):
        """ObjectId格式化"""
        return str(id_)

    def get_request(**kwargs) -> Request:
        """遍历获取 Request"""
        for v in kwargs.values():
            if not isinstance(v, Request):
                continue
            return v
        assert False

    def vriable_naming_rename(method: str, data: str) -> str:
        """变量命名风格转换
        method: 转换方法, snake_case, camelCase, CamelCase
        data: 要转换的数据
        """
        if method == "camelCase":
            return NSC.snake_to_camel(data)
        elif method == "CamelCase":
            return NSC.snake_to_camel2(data)
        return data

    @wraps(func)
    async def wrapper(*args, **kwargs):
        if inspect.iscoroutinefunction(func):
            resp = await func(*args, **kwargs)
        else:
            resp = func(*args, **kwargs)

        # 列表数据对象化, 保证返回数据对象化
        if isinstance(resp, BaseModel):
            resp = resp.model_dump()
        elif isinstance(resp, list):
            resp = {"lists": resp}

        resp_data = jsonable_encoder(
            resp,
            by_alias=False,
            custom_encoder={
                datetime: datetime_encoder,
                ObjectId: objectid_encoder,
            },
        )
        try:
            # 变量名风格转换
            request = get_request(**kwargs)
            assert request is not None
            method_ = request.headers.get("X-Variable-Naming-Style", "snake_case")
            assert method_ != "snake_case"
            resp_data_str = vriable_naming_rename(method_, json.dumps(resp_data))
            resp_data = json.loads(resp_data_str)
        except AssertionError:
            pass

        resp_content = HttpResp.SUCCESS.content(request.state.api_version, request.state.accept_language)
        resp_content["data"] = resp_data

        request.state.track_info = resp_content
        return JSONResponse(
            content=resp_content,
            media_type="application/json;charset=utf-8",
        )

    return wrapper


class RespModel(BaseModel, Generic[RT]):
    code: int = Field(default=1000)
    msg: str = Field(default="Success")
    data: Union[RT, dict] = Field(default={})

    def model_dump(self, **kwargs):
        print("model dump")
        data = super().model_dump(**kwargs)
        return data

    class Config:
        json_encoders = {
            datetime: lambda v: arrow.get(v).isoformat(),
            ObjectId: lambda v: str(v),
        }
