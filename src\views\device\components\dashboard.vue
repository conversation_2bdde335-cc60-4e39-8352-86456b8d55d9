<template>
  <div class="relative">
    <div class="absolute left-1 top-1 z-10 w-6 h-6 cursor-pointer text-primary" @click="fullscreen">
      <icon
        :class="screenfull.isFullscreen ? 'icon-full-hidden' : 'icon-full'"
        :size="20"
        :name="screenfull.isFullscreen ? 'local-icon-fullscreen-exit' : 'local-icon-fullscreen'"
      />
    </div>
    <iframe
      id="DashboardFrame"
      class="w-full h-[76vh]"
      :src="dashboardUrl"
      sandbox="allow-same-origin allow-scripts"
      referrerpolicy="no-referrer"
    ></iframe>
  </div>
</template>

<script lang="ts" setup>
import screenfull from "screenfull";

defineProps({
  dashboardUrl: {
    type: String,
    required: true
  }
});

const fullscreen = () => {
  const element = document.getElementById("DashboardFrame") as HTMLElement;
  if (screenfull.isEnabled) {
    if (!screenfull.isFullscreen) {
      screenfull.request(element);
    } else {
      screenfull.exit();
    }
  }
};
</script>

<style lang="scss" scoped>
.icon-full-hidden {
  display: none;
}
.icon-full {
  display: block;
}
</style>