{"人员账号": "人员账号", "人员名称": "人员名称", "人员角色": "人员角色", "查询": "查询", "重置": "重置", "账号": "账号", "名称": "名称", "角色": "角色", "邮箱": "邮箱", "关联车辆": "关联车辆", "状态": "状态", "密码": "密码", "确认密码": "确认密码", "请输入账号": "请输入账号", "请输入名称": "请输入名称", "请选择车辆": "请选择车辆", "创建时间": "创建时间", "最近登录时间": "最近登录时间", "编辑人员": "编辑人员", "新增角色": "新增角色", "备注": "备注", "排序": "排序", "正常": "正常", "停用": "停用", "权限": "权限", "权限设置": "权限设置", "展开折叠": "展开/折叠", "全选不全选": "全选/不全选", "父子联动": "父子联动", "新增菜单": "新增菜单", "下载当前菜单": "下载当前菜单", "上传菜单": "上传菜单", "图标": "图标", "权限标识": "权限标识", "更新时间": "更新时间", "菜单类型": "菜单类型", "目录": "目录", "菜单": "菜单", "按钮": "按钮", "父级菜单": "父级菜单", "请选择父级菜单": "请选择父级菜单", "菜单名称": "菜单名称", "请输入菜单名称": "请输入菜单名称", "菜单图标": "菜单图标", "无": "无", "搜索图标": "搜索图标", "路由路径": "路由路径", "请输入路由路径": "请输入路由路径", "访问的路由地址": "访问的路由地址，如：`admin`，如外网地址需内链访问则以`http(s)://`开头", "是否显示": "是否显示", "显示": "显示", "隐藏": "隐藏", "选择隐藏则路由将不会出现在侧边栏": "选择隐藏则路由将不会出现在侧边栏，但仍然可以访问", "菜单状态": "菜单状态", "选择停用则路由将不会出现在侧边栏": "选择停用则路由将不会出现在侧边栏，也不能被访问", "菜单排序": "菜单排序", "数值越大越排前": "数值越大越排前", "组件路径": "组件路径", "请输入组件路径": "请输入组件路径", "访问的组件路径": "访问的组件路径，如：`permission/admin/index`，默认在`views`目录下", "选中菜单": "选中菜单", "请输入选中菜单": "请输入选中菜单", "访问详情页面菜单高亮显示": "访问详情页面，编辑页面时，菜单高亮显示，如`/consumer/lists`", "权限字符": "权限字符", "请输入权限字符": "请输入权限字符", "将作为验权使用": "将作为server端API验权使用，如`system:admin:list`，请谨慎修改", "路由参数": "路由参数", "请输入路由参数": "请输入路由参数", "访问路由的默认传递参数": "访问路由的默认传递参数，如：`{id: 1, name: admin}`或`id=1&name=admin`", "是否缓存": "是否缓存", "缓存": "缓存", "不缓存": "不缓存", "选择缓存则会被缓存": "选择缓存则会被`keep-alive`缓存"}