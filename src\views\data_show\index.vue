<template>
  <div id="DataShow" class="relative">
    <el-popover
      :width="300"
      :disabled="isFrom !== 0"
      v-if="!screenfull.isFullscreen"
      popper-style="box-shadow: rgb(14 18 22 / 35%) 0px 10px 38px -10px, rgb(14 18 22 / 20%) 0px 10px 20px -15px; padding: 20px;"
    >
      <template #reference>
        <div class="absolute left-1 top-1 z-10 w-6 h-6 cursor-pointer text-primary" @click="fullscreen">
          <icon
            :class="screenfull.isFullscreen ? 'icon-full-hidden' : 'icon-full'"
            :size="16"
            :name="screenfull.isFullscreen ? 'local-icon-fullscreen-exit' : 'local-icon-fullscreen'"
          />
        </div>
      </template>
      <template #default>
        <div class="flex flex-row">
          <el-table :data="pageData.vehiclesList" border>
            <el-table-column align="center" :label="$t('vehicle.车辆名称')" prop="vehicle_name" min-width="100" />
            <el-table-column align="center" :label="$t('vehicle.操作')" prop="vehicle_name" min-width="100">
              <template #default="{ row }">
                <el-button type="primary" link @click="joinRoom(row)">获取车辆数据</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </el-popover>

    <CcScaleScreen :width="1920" :height="1080">
      <BorderBox1>
        <div class="head">
          <div class="gradient-text">拓疆者数据展示({{ pageData.vehicleName }})</div>
          <div class="time whitespace-nowrap absolute text-[#c2c2c2] text-[26px] bottom-0 right-5 text-left w-[248px]">
            {{ pageData.time }}
          </div>
        </div>
        <div class="data-wrap">
          <div class="row-wrap left-wrap">
            <ChartWrap title="发动机转速" class="left-top left-content">
              <!-- 转速 -->
              <RpmChart />
            </ChartWrap>
            <ChartWrap title="剩余油量" class="left-center left-content">
              <!-- 水球 -->
              <WaterPoloChart />
            </ChartWrap>
            <BorderBox12 class="left-bottom left-content">
              <!-- 电压油压 -->
              <PressureChart />
            </BorderBox12>
          </div>
          <div class="row-wrap center-wrap">
            <div class="center-top center-content">
              <!-- <Temperature /> -->
              <!-- <MapLine /> -->
              <video src="./image/wajivideo.mp4" autoplay loop muted :controls="false"></video>
            </div>
            <ChartWrap title="操控强度展示" class="center-bottom center-content">
              <ControlChart />
            </ChartWrap>
          </div>
          <div class="row-wrap right-wrap">
            <ChartWrap title="操控时长" class="right-top right-content">
              <CurrentTime />
            </ChartWrap>
            <ChartWrap title="温度总览" class="right-center right-content">
              <TemperatureChart />
            </ChartWrap>
            <BorderBox12 class="right-bottom right-content p-2">
              <HLS ref="hlsRef" :token="userStore.token" :url="hlsUrl" fit="fill"></HLS>
            </BorderBox12>
          </div>
        </div>
      </BorderBox1>
    </CcScaleScreen>
  </div>
</template>
<script lang="ts" setup name="dataShow">
import dayjs from "dayjs";
import screenfull from "screenfull";
import { BorderBox1, BorderBox12 } from "@newpanjing/datav-vue3";
import CcScaleScreen from "./util/scale-screen";
import HLS from "@/components/hls-player/index.vue";
import useUserStore from "@/stores/modules/user";
import useWebsocketStore from "@/stores/modules/websocket";
import { getVehiclesData } from "@/api/data-man";

import MapLine from "./components/map-line/index.vue";
import RpmChart from "./components/rpm-chart.vue";
import TemperatureChart from "./components/temperature-chart.vue";
import PressureChart from "./components/pressure-gauge-chart.vue";
import WaterPoloChart from "./components/water-polo-chart.vue";
import ControlChart from "./components/control-chart.vue";
import TestChart from "./components/test-chart.vue";
import CurrentTime from "./components/current-time-copy.vue";
import ChartWrap from "./components/item-wrap/index.vue";

const props = defineProps({
  vehicleId: {
    type: String,
    default: "",
  },
  isFrom: {
    type: Number,
    default: 0,
  },
});
const websocketStore = useWebsocketStore();
const userStore = useUserStore();
const hlsRef = shallowRef();

const pageData = reactive({
  vehicleId: props.vehicleId,
  time: "",
  vehiclesList: [],
  vehicleName: "",
});

const hlsUrl = computed(() => {
  const host = window.location.host;
  // const host = "*************:8000";
  return `${location.protocol}//${host}/api/v2/vehicles/${pageData.vehicleId}/service/live_stream/index.m3u8`;
});

const fullscreen = () => {
  const element = document.getElementById("DataShow") as HTMLElement;
  if (screenfull.isEnabled) {
    if (!screenfull.isFullscreen) {
      screenfull.request(element);
    } else {
      screenfull.exit();
    }
  }
};

const getVehiclesDataList = async () => {
  const { lists: res } = await getVehiclesData();
  pageData.vehiclesList = res;
};

const joinRoom = (row: any) => {
  leaveRoom();
  hlsRef.value.close();
  websocketStore.send({
    cmd: "room.join",
    data: { vehicle_id: row._id },
  });
  pageData.vehicleId = row._id;
  pageData.vehicleName = row.vehicle_name;
  hlsRef.value.play();
};

const leaveRoom = () => {
  websocketStore.send({
    cmd: "room.leave",
    data: { vehicle_id: pageData.vehicleId },
  });
};

setTimeout(() => {
  nextTick(() => {
    window.dispatchEvent(new Event("resize"));
    hlsRef.value.play();
  });
}, 500);

let timer: any;

onMounted(async () => {
  timer = setInterval(() => {
    pageData.time = dayjs().local().format("YYYY-MM-DD HH:mm:ss");
  }, 1000);
  await getVehiclesDataList();
  if (props.isFrom === 1) {
    pageData.vehiclesList.find((item: any) => item._id === props.vehicleId) && joinRoom(pageData.vehiclesList.find((item: any) => item._id === props.vehicleId));
  } else {
    joinRoom(pageData.vehiclesList[0]);
  }
});

onUnmounted(() => {
  leaveRoom();
  if (timer) clearInterval(timer);
});
</script>

<style lang="scss" scoped>
@font-face {
  font-family: "electronicFont";
  src: url("./css/font/DS-DIGIT.TTF");
}

#DataShow {
  font-size: 56px;
  height: 100%;
  max-height: 90vh;
  width: 100%;
  background-image: url("./image/page_bg.png");
  background-size: cover;
  background-position: center center;
  user-select: none;
}
.head {
  width: 1900px;
  height: 112px;
  margin: 0 auto;
  background-image: url("./image/head_bg.png");
  background-size: cover;
  background-position: center center;
  z-index: -1;
  text-align: center;
  padding-top: 20px;
  position: relative;
  .time {
    font-family: "electronicFont";
    letter-spacing: 2px;
  }
  .gradient-text {
    font-size: 50px;
    font-weight: bold;
    background: linear-gradient(to bottom, rgb(110, 121, 158), rgb(24, 56, 236));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: Arial, Helvetica, sans-serif;
  }
}
.data-wrap {
  height: 968px;
  display: flex;
  flex-direction: row;
  gap: 30px;
  margin: 0 30px;
  padding: 20px 0;
  .row-wrap {
    flex: 1;
  }
  .left-wrap {
    display: flex;
    flex-direction: column;
    gap: 10px;
    .left-content {
      background-color: #0b112391;
      height: 33%;
      width: 100%;
    }
  }
  .center-wrap {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 10px;
    .center-top {
      background-color: #0b112391;
      height: 60%;
    }
    .center-bottom {
      background-color: #0b112391;
      height: 40%;
    }
  }
  .right-wrap {
    display: flex;
    flex-direction: column;
    gap: 10px;
    .right-content {
      height: 33%;
    }
  }
}
.icon-full-hidden {
  display: none;
}
.icon-full {
  display: block;
}
</style>
