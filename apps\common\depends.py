"""
全局注入依赖
"""

import re
import json
from typing import Optional, Union, List, Any, Tuple
from functools import lru_cache

from bson import ObjectId
from bson.errors import InvalidId
from fastapi import Request, WebSocket, Depends
from fastapi.security import OAuth2PasswordBearer

import apps.models.cache as Cache
import apps.models.user as UserModel
import apps.services.role as RoleService
from apps.services.user import UserService
from apps.models.permissions import FuncId, RoleResourceFlag
from .app_exc import AppException
from .http_base import HttpResp

__all__ = [
    "Access",
    "CheckPermission",
    "ApiVersion",
    "Multilingual",
    "permission_dp",
]


class Access(OAuth2PasswordBearer):
    """
    自定义的 权限校验
    继承 OAuth2PasswordBearer 类，是为了让 FastAPI 自动生成的文档中的登录按钮生效
    """

    token_expier = 7200

    # 免登录接口
    not_auth_uri: Tuple = (
        re.compile(r"user:(login|auth)"),  # 用户认证
        re.compile(r"user:me:reset_password"),  # 用户重置密码
        # 视频数据和封面数据查看，下载，包含从车辆接口进入和从数据管理接口进入
        re.compile(r"data_manage:media:video:(cover|play|download):[a-z0-9]+\.(mp4|jpg)"),
        re.compile(r"vehicles:[0-9a-fA-F]{24}:service:video:(cover|play|download):[a-z0-9]+\.(mp4|jpg)"),
    )

    def __init__(self):
        super().__init__(tokenUrl="/api/v1/user/auth")
        self.auth_token = ""
        self.auth_type = ""

    @lru_cache(maxsize=128)
    def is_not_auth_url(self, url) -> bool:
        """是否是不需要登录的 uri"""
        for no_auth_url in self.not_auth_uri:
            if no_auth_url.match(url):
                return True
        return False

    async def get_user_info(self, user_id: str) -> UserModel.CacheInfo:
        """获取用户信息"""
        # 检查用户信息是否正确
        user_info = Cache.UserInfo(user_id)
        user_info_str = await user_info.get()
        if user_info_str is None:
            try:
                user_obj_ = await UserService().info(ObjectId(user_id))
                role_ids = [str(role_id) for role_id in user_obj_["role_ids"]]
                user_obj_["role_ids"] = role_ids
            except Exception:
                raise AppException(HttpResp.TOKEN_INVALID)
        else:
            user_obj_ = json.loads(user_info_str)
        user_obj_["_id"] = user_id
        user = UserModel.CacheInfo(**user_obj_)
        # 检查用户状态
        assert user.status is not UserModel.Status.LOCKED.value, "User is locked"
        assert user.status is not UserModel.Status.DELETED.value, "User is deleted"
        # 缓存用户信息
        await user_info.set(user.model_dump_json(exclude={"id", "_id"}))

        # 添加超级管理员 buff
        if UserModel.SUPER_ADMIN_ROLE_ID in user.role_ids:
            user.is_super_admin = True
        return user

    async def check_user_token(self, req: Union[Request, WebSocket]):
        """Redis 中检查 token 是否有效, 并缓存用户信息加入到 request.state"""
        assert self.auth_type == "Bearer"
        user_token = Cache.UserToken(self.auth_token)
        user_id = await user_token.get()
        if user_id is None:
            raise AppException(HttpResp.TOKEN_INVALID)

        user = await self.get_user_info(user_id)

        # token 过期时间小于 2 小时，重新设置过期时间
        await user_token.expire(self.token_expier)

        req.state.user_token = self.auth_token
        req.state.user = user

    async def check_api_token(self, req: Request):
        user_id = await UserService().find_api_key(self.auth_token)
        if user_id is None:
            raise AppException(HttpResp.TOKEN_INVALID)
        user = await self.get_user_info(user_id)

        req.state.user_token = self.auth_token
        req.state.user = user

    async def ws(self, req: WebSocket):
        """WebSocket 接口认证校验，需要手动调用"""
        token = req.query_params.get("authorization")
        try:
            self.auth_type, self.auth_token = token.split()  # type: ignore
        except Exception:
            raise AppException(HttpResp.TOKEN_EMPTY)
        await self.check_user_token(req)

    async def http(self, req: Request):
        """HTTP 接口认证校验，使用注入回调"""
        url_key = req.url.path[8:].replace("/", ":")
        if self.is_not_auth_url(url_key):
            return None

        token = req.headers.get("authorization")
        try:
            self.auth_type, self.auth_token = token.split()  # type: ignore
        except Exception:
            raise AppException(HttpResp.TOKEN_EMPTY)
        if self.auth_token.startswith("u-"):
            await self.check_user_token(req)
        elif self.auth_token.startswith("t-"):
            await self.check_api_token(req)
        else:
            raise AppException(HttpResp.TOKEN_INVALID)

    async def __call__(self, req: Request):
        """注入回调"""
        await self.http(req)


class CheckPermission:
    """检查权限"""

    def __init__(self, func_ids: List[FuncId], role_flag: Optional[RoleResourceFlag]):
        self.need_func_ids = set(func_ids)
        self.need_role_flag = role_flag

    def parse_resources_id(self, req: Request) -> Tuple[Optional[ObjectId], Optional[ObjectId]]:
        """获取车辆或者操作台ID"""
        vehicle_id, opc_id = None, None
        url_keys = req.url.path[8:].strip("/").split("/")
        if len(url_keys) <= 1:
            return vehicle_id, opc_id
        try:
            if url_keys[0] == "vehicles":
                vehicle_id = ObjectId(url_keys[1])
            if url_keys[0] == "op_consoles":
                opc_id = ObjectId(url_keys[1])
        except InvalidId:
            raise AppException(HttpResp.INVALID_ID)
        return vehicle_id, opc_id

    async def __call__(self, req: Request) -> None:
        user = req.state.user
        if user.is_super_admin:
            return

        vehicle_id, opc_id = self.parse_resources_id(req)
        if await RoleService.is_have_permissions(
            user.role_ids,
            list(self.need_func_ids),
            self.need_role_flag,
            opc_id,
            vehicle_id,
        ):
            return None

        raise AppException(HttpResp.NO_PERMISSION)


def permission_dp(func_id: FuncId, flags: Optional[RoleResourceFlag] = None) -> Any:
    """生成权限依赖"""
    return [Depends(CheckPermission([func_id], flags))]


class ApiVersion:
    """设置API版本"""

    def __init__(self, version: str):
        self.version = version

    def __call__(self, req: Request):
        req.state.api_version = self.version

    @staticmethod
    def get(req: Request):
        if req.url.path.startswith("/api/v1/"):
            return "v1"
        return "v2"


class Multilingual:
    """设置语言参数"""

    support_lang = ("zh-CN", "zh-TW", "en", "ja")

    def __init__(self):
        pass

    def __call__(self, req: Request):
        req.state.accept_language = Multilingual.get(req)

    @staticmethod
    def get(req: Request):
        accept_lang = req.headers.get("accept-language", "en")
        try:
            Multilingual.support_lang.index(accept_lang)
        except Exception:
            return "en"
        return accept_lang


# class UnifiedRespMiddleware(BaseHTTPMiddleware):
#     """统一响应格式
#     接口正常返回时,统一响应结果格式
#     """

#     def __init__(self, app):
#         super().__init__(app)

#     async def dispatch(self, request, call_next):
#         print("UnifiedRespMiddleware")
#         response = await call_next(request)
#         print("type:", type(response))
#         print(response)
#         return response

#     def datetime_encoder(self, dt: datetime):
#         """日期时间格式化"""
#         return arrow.get(dt).isoformat()

#     def objectid_encoder(self, id_: ObjectId):
#         """ObjectId格式化"""
#         return str(id_)

#     def vriable_naming_rename(self, method: str, data: str) -> str:
#         """变量命名风格转换
#         method: 转换方法, snake_case, camelCase, CamelCase
#         data: 要转换的数据
#         """
#         if method == "camelCase":
#             return NSC.snake_to_camel(data)
#         elif method == "CamelCase":
#             return NSC.snake_to_camel2(data)
#         return data

#     # @wraps(func)
#     # async def wrapper(*args, **kwargs):
#     #     if inspect.iscoroutinefunction(func):
#     #         resp = await func(*args, **kwargs)
#     #     else:
#     #         resp = func(*args, **kwargs)

#     #     # 列表数据对象化, 保证返回数据对象化
#     #     if isinstance(resp, list):
#     #         resp = {"lists": resp}

#     #     if isinstance(resp, BaseModel):
#     #         resp = resp.model_dump()

#     #     resp_data = jsonable_encoder(
#     #         resp,
#     #         by_alias=False,
#     #         custom_encoder={
#     #             datetime: datetime_encoder,
#     #             ObjectId: objectid_encoder,
#     #         },
#     #     )

#     #     try:
#     #         # 变量名风格转换
#     #         request = get_request(**kwargs)
#     #         assert request is not None
#     #         method_ = request.headers.get("X-Variable-Naming-Style", "snake_case")
#     #         assert method_ != "snake_case"
#     #         resp_data_str = vriable_naming_rename(method_, json.dumps(resp_data))
#     #         resp_data = json.loads(resp_data_str)
#     #     except AssertionError:
#     #         pass

#     #     resp_content = HttpResp.SUCCESS.content(request.state.api_version, request.state.accept_language)
#     #     resp_content["data"] = resp_data

#     #     request.state.track_info = resp_content
#     #     return JSONResponse(
#     #         content=resp_content,
#     #         media_type="application/json;charset=utf-8",
#     #     )
