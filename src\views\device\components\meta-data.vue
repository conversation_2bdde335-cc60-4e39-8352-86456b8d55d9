<template>
  <div class="meta-wrap">
    <el-table :data="pageData.metaData">
      <el-table-column label="名称" prop="key"></el-table-column>
      <el-table-column label="值" prop="value"></el-table-column>
      <!-- <el-table-column label="类型" prop="type"></el-table-column> -->
      <el-table-column label="描述" prop="desc"></el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleShowDialog(row, 'edit')">编辑</el-button>
          <el-button type="primary" link @click="handleDeleteData(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <el-dialog class="save-layout" v-model="pageData.dialogFormVisible" :title="pageData.dialogFormTitle" width="400px">
    <div class="flex items-center">
      <div class="w-20">名称</div>
      <el-input :disabled="pageData.dialogType === 'edit'" v-model.trim="pageData.curRow.key" />
    </div>
    <div class="flex items-center mt-2">
      <div class="w-20">值</div>
      <el-input v-model.trim="pageData.curRow.value" />
    </div>
    <div class="flex items-center mt-2">
      <div class="w-20">描述</div>
      <el-input :disabled="pageData.dialogType === 'edit'" v-model.trim="pageData.curRow.desc" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="mr-1" @click="pageData.dialogFormVisible = false">{{ $t("stream.取消") }}</el-button>
        <el-button type="primary" @click="handleSave">{{ $t("stream.确定") }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { metaDataList, metaDataEdit, metaDataAdd, metaDataDelete, metaDataDetail } from "@/api/device";
import feedback from "@/utils/feedback";

const props = defineProps({
  vehicleId: {
    type: String,
    require: true,
    default: "",
  },
});

const pageData: any = reactive({
  metaData: [],
  dialogFormVisible: false,
  dialogType: "add",
  curRow: {
    key: "",
    value: "",
    type: "",
    desc: "",
  },
});

const getMetaDataList = async () => {
  const { lists } = await metaDataList(props.vehicleId);
  pageData.metaData = lists;
};

const getMetaDataDetail = async (row: any) => {
  await metaDataDetail({ key: row.key, vehicleId: props.vehicleId });
};

const handleShowDialog = async (row: any, type: string) => {
  pageData.dialogType = type;
  if (type == "add") {
    Object.assign(pageData.curRow, {
      key: "",
      value: "",
      type: "",
      desc: "",
    });
    pageData.dialogFormTitle = "添加元数据";
  } else if (type == "edit") {
    pageData.dialogFormTitle = "编辑元数据";
  }
  Object.assign(pageData.curRow, row);
  pageData.dialogFormVisible = true;
};

const handleDeleteData = async (row: any) => {
  await feedback.confirm("确定要删除？");
  await metaDataDelete({ key: row.key, vehicleId: props.vehicleId });
  feedback.msgSuccess("删除成功");
  getMetaDataList();
};

const handleSave = async () => {
  pageData.curRow.vehicleId = props.vehicleId;
  if (pageData.dialogType === "edit") {
    await metaDataEdit(pageData.curRow);
  } else {
    await metaDataAdd(pageData.curRow);
  }
  pageData.dialogFormVisible = false;
  getMetaDataList();
};

onMounted(() => {
  getMetaDataList();
});

defineExpose({
  handleShowDialog,
});
</script>
<style lang="scss" scoped></style>
