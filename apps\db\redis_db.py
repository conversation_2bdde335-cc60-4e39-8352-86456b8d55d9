from typing import Union, Set, Optional, Awaitable, Dict

from redis import asyncio as aioredis

from apps.utils import singleton
from apps.config import get_settings


__all__ = ["RedisDB"]


@singleton
class RedisUtilClass:
    """Redis操作工具类"""

    def __init__(self, _=None) -> None:
        _c = get_settings()
        self.prefix: str = _c.rdb_prefix  # key通用前缀
        self.rdb: aioredis.Redis = aioredis.Redis(
            host=_c.redis_host,
            port=_c.redis_port,
            db=_c.redis_db,
            password=_c.redis_password,
            encoding="utf-8",
            decode_responses=True,
        )

    async def info(self, section: Optional[str] = None) -> dict:
        """Redis服务信息"""
        return await self.rdb.info(section)

    async def dbsize(self) -> dict:
        """当前数据库key数量"""
        return await self.rdb.dbsize()

    def gen_key(self, key: str) -> str:
        """获取key"""
        return f"{self.prefix}{key}"

    async def set(self, key: str, value: Union[str, int, float], timeout: Union[int, None] = None):
        """设置键值对"""
        await self.rdb.set(self.gen_key(key), value=value, ex=timeout)

    async def setnx(self, key: str, value: Union[str, int, float]) -> int:
        """设置键值对, 如果key不存在"""
        return await self.rdb.setnx(self.gen_key(key), value=value)

    async def incr(self, key: str, amount: int = 1) -> int:
        """自增"""
        return await self.rdb.incr(self.gen_key(key), amount)

    async def get(self, key: str) -> Union[str, None]:
        """获取key的值"""
        return await self.rdb.get(self.gen_key(key))

    async def exist(self, key: str) -> bool:
        """判断多项key是否存在
        返回存在的key数量
        """
        if await self.rdb.exists(self.gen_key(key)) == 1:
            return True
        return False

    async def sadd(self, key: str, *values: Union[str, int, float]):
        """将数据放入set缓存
        返回添加的数量
        """
        res = self.rdb.sadd(self.gen_key(key), *values)
        assert isinstance(res, Awaitable)
        return await res

    async def sget(self, key: str) -> Set:
        """根据 key 获取 set 中的所有值"""
        res = self.rdb.smembers(self.gen_key(key))
        assert isinstance(res, Awaitable)
        return await res

    async def srem(self, key: str, *values: Union[str, int, float]) -> int:
        """根据 key 删除有序集合中的值"""
        res = self.rdb.srem(self.gen_key(key), *values)
        assert isinstance(res, Awaitable)
        return await res

    async def zadd(self, key: str, mapping: dict) -> int:
        """将数据放入有序集合
        返回添加的数量
        """
        return await self.rdb.zadd(self.gen_key(key), mapping)

    async def zget(self, key: str, begin_index=0, end_index=-1) -> Set:
        """根据 key 获取有序集合中的所有值"""
        return await self.rdb.zrange(self.gen_key(key), begin_index, end_index)

    async def zkeys(self, key: str, start: int, end: int) -> Set:
        """根据 key 获取有序集合中的所有值"""
        return await self.rdb.zrange(self.gen_key(key), start, end)

    async def zrem(self, key: str, *values: Union[str, int, float]) -> int:
        """根据 key 删除有序集合中的值"""
        return await self.rdb.zrem(self.gen_key(key), *values)

    async def zkindex(self, key: str, value: Union[str, int, float]) -> int:
        """根据 key 获取有序集合中值的索引"""
        return await self.rdb.zrank(self.gen_key(key), value)

    async def hmset(self, key: str, mapping: dict) -> str:
        """设置key, 通过字典的方式设置多个field, value对
        返回添加的数量
        """
        res = self.rdb.hmset(self.gen_key(key), mapping=mapping)
        assert isinstance(res, Awaitable)
        return await res

    async def hset(self, key: str, field: str, value: Union[str, int, float]) -> str:
        """
        向hash表中放入数据,如果不存在将创建
        返回添加的数量
        """
        res = self.hmset(key, mapping={field: value})
        assert isinstance(res, Awaitable)
        return await res

    async def hgetall(self, key: str) -> dict:
        """获取key中所有的field和value"""
        res = self.rdb.hgetall(self.gen_key(key))
        assert isinstance(res, Awaitable)
        return await res

    async def hget(self, key: str, field: str) -> Optional[str]:
        """获取key中field域的值"""
        res = self.rdb.hget(self.gen_key(key), field)
        assert isinstance(res, Awaitable)
        return await res

    async def hexists(self, key: str, field: str) -> bool:
        """判断key中有没有field域名"""
        res = self.rdb.hexists(self.gen_key(key), field)
        assert isinstance(res, Awaitable)
        return await res

    async def hdel(self, key: str, *fields: str) -> int:
        """删除hash表中的值
        返回删除的数量
        """
        res = self.rdb.hdel(self.gen_key(key), *fields)
        assert isinstance(res, Awaitable)
        return await res

    async def ttl(self, key: str) -> int:
        """根据key获取过期时间"""
        return await self.rdb.ttl(self.gen_key(key))

    async def expire(self, key: str, time: int):
        """指定缓存失效时间"""
        await self.rdb.expire(self.gen_key(key), time)

    async def delete(self, *keys: str):
        """删除一个或多个键"""
        return await self.rdb.delete(*(self.gen_key(key) for key in keys))

    async def xadd(self, task_queue, message_data) -> str:
        return await self.rdb.xadd(self.gen_key(task_queue), message_data)

    async def lpush(self, key, value) -> int:
        res = self.rdb.lpush(self.gen_key(key), value)
        assert isinstance(res, Awaitable)
        return await res

    async def close(self):
        """关闭连接"""
        await self.rdb.aclose()


RedisDB = RedisUtilClass()


class BaseCache:
    """缓存基类"""

    def __init__(self, key_name: str):
        self.key_name = key_name

    async def delete(self):
        return await RedisDB.delete(self.key_name)


class StringCache(BaseCache):
    """字符串类型的数据"""

    def __init__(self, key_name: str):
        self.key_name = key_name

    async def get(self) -> str | None:
        return await RedisDB.get(self.key_name)

    async def set(self, value: str | int | float, ex: int | None = None) -> None:
        return await RedisDB.set(self.key_name, value, ex)

    async def expire(self, ex: int):
        return await RedisDB.expire(self.key_name, ex)

    async def ttl(self) -> int:
        return await RedisDB.ttl(self.key_name)

    async def exist(self) -> bool:
        return await RedisDB.exist(self.key_name)

    async def setnx(self, value: str | int | float) -> int:
        return await RedisDB.setnx(self.key_name, value)

    async def incr(self) -> int:
        return await RedisDB.incr(self.key_name)


class HashCache(BaseCache):
    """哈希类型的数据"""

    def __init__(self, key_name: str):
        self.key_name = key_name

    async def hsetall(self, data: Dict):
        return await RedisDB.hmset(self.key_name, data)

    async def hgetall(self) -> Dict:
        return await RedisDB.hgetall(self.key_name)

    async def ttl(self) -> int:
        return await RedisDB.ttl(self.key_name)

    async def expire(self, time: int):
        return await RedisDB.expire(self.key_name, time)

    async def exist(self) -> bool:
        return await RedisDB.exist(self.key_name)

    async def hdel(self, key: str):
        return await RedisDB.hdel(self.key_name, key)

    async def hset(self, key: str, value: str | int | float):
        return await RedisDB.hset(self.key_name, key, value)

    async def hget(self, key: str):
        return await RedisDB.hget(self.key_name, key)


class HashFieldCache:
    """哈希字段"""

    def __init__(self, p_key: str, c_key: str):
        self.p_key = p_key
        self.c_key = c_key

    async def hget(self) -> str | None:
        return await RedisDB.hget(self.p_key, self.c_key)

    async def set(self, value: str | int | float):
        return await RedisDB.hset(self.p_key, self.c_key, value)

    async def delete(self):
        return await RedisDB.hdel(self.p_key, self.c_key)


class SetCache(BaseCache):
    """集合类型的数据"""

    def __init__(self, key_name: str):
        self.key_name = key_name

    async def sadd(self, value: str | int | float):
        return await RedisDB.sadd(self.key_name, value)

    async def remove(self, value: str | int | float):
        return await RedisDB.srem(self.key_name, value)

    async def sget(self) -> set[str]:
        return await RedisDB.sget(self.key_name)


class SortSetCache(BaseCache):
    """集合类型的数据"""

    def __init__(self, key_name: str):
        self.key_name = key_name

    async def zadd(self, value: str | int | float, score: int):
        return await RedisDB.zadd(self.key_name, {value: score})

    async def zrm(self, value: str | int | float):
        return await RedisDB.zrem(self.key_name, value)

    async def zget(self) -> set[str]:
        return await RedisDB.zget(self.key_name)

    async def zindex(self, value: str | int | float) -> int | None:
        return await RedisDB.zkindex(self.key_name, value)


class ListCache(BaseCache):
    """列表类型的数据"""

    def __init__(self, key_name: str):
        self.key_name = key_name

    async def lpush(self, value: str | int | float):
        return await RedisDB.lpush(self.key_name, value)

    async def delete(self):
        return await RedisDB.delete(self.key_name)
