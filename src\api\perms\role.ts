import request from "@/utils/request";

const API_VERSION = 'v2';

// 角色列表
export function roleLists(params: any) {
  return request.get({ url: `/${API_VERSION}/user/role`, params });
}
// 角色列表
export function roleAll(params?: any) {
  return request.get({ url: `/${API_VERSION}/user/role/all`, params });
}
// 角色列表
export function roleDetail(rid: string) {
  return request.get({ url: `/${API_VERSION}/user/role/${rid}` });
}
// 添加角色
export function roleAdd(params: any) {
  return request.post({ url: `/${API_VERSION}/user/role`, params });
}
// 编辑角色
export function roleEdit(params: any) {
  const { id, ...data } = params;
  return request.put({ url: `/${API_VERSION}/user/role/${id}`, data });
}
// 删除角色
export function roleDelete(id: string) {
  return request.delete({ url: `/${API_VERSION}/user/role/${id}` });
}

// 编辑角色
export function setMenuPermissions(params: any) {
  const { id, ...data } = params;
  return request.put({ url: `/${API_VERSION}/user/role/${id}/menu`, data });
}

// 获取api权限列表
export function apiPermissionsLists() {
  return request.get({ url: `/${API_VERSION}/user/role/permissions` });
}

// 设置api权限
export function setApiPermissions(params: any) {
  const { id, ...data } = params;
  return request.put({ url: `/${API_VERSION}/user/role/` + id + `/permissions`, data });
}

// 设置资源权限
export function setResourcePermissions(params: any) {
  const { id, ...data } = params;
  return request.put({ url: `/${API_VERSION}/user/role/` + id + `/resource`, data });
}
