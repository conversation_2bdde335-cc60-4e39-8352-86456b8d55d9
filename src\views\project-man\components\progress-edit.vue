<template>
  <popup
    custom-class="no-footer"
    ref="popupRef"
    title="当前情况更新"
    :async="false"
    width="450px"
    @confirm="handleSubmit"
    @close="handleClose"
  >
    <el-radio-group class="radio-group" v-model="selectValue">
      <el-radio v-for="item in progressList" :key="item.progress_id" :value="item.progress_id">
        {{ item.content }}
      </el-radio>
    </el-radio-group>
  </popup>
</template>

<script lang="ts" setup>
import Popup from "@/components/popup/index.vue";
import useMetadataStore from "@/stores/modules/metadata";

const metadataStore = useMetadataStore();
const emit = defineEmits(["success", "close"]);
const popupRef = shallowRef<InstanceType<typeof Popup>>();

const selectValue = ref("");
const progressList: any = ref([]);

const open = () => {
  popupRef.value?.open();
};

const handleClose = () => {
  emit("close");
};

const handleSubmit = () => {
  emit(
    "success",
    progressList.value.find((item: any) => item.progress_id === Number(selectValue.value))
  );
  handleClose();
};

onMounted(async () => {
  progressList.value = await metadataStore.fetchMetadata("project_progress");
});

onUnmounted(() => {});

defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.radio-group {
  @apply flex flex-col items-center justify-center;
  height: 150px;
  overflow-y: auto;
  flex-wrap: nowrap;
  .el-radio {
    margin-bottom: 10px;
    margin-right: 0px;
  }
  .el-radio:last-child {
    margin-bottom: 0;
  }
}
</style>
