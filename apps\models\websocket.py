from enum import Enum
from typing import List, Union, Optional, Annotated, Dict, Any

from pydantic import BaseModel

from .ros import Node, NodeCrontrol, UpdateParams
from .vehicle import GMSLCamera, IPCamera, CAN
from .data_man import RecordVideoTask, RecordMqttTask


class DeviceDataSyncCode(Enum):
    synced = 0
    nosync = 1


class CommonCmd(Enum):
    ping = "ping"
    pong = "pong"


class DeviceRunData(BaseModel):
    ros_nodes: Optional[List[Node]] = []
    gmsl_cameras: Optional[List[GMSLCamera]] = []
    ip_cameras: Optional[List[IPCamera]] = []
    eth_can: Optional[List[CAN]] = []

    def compare(self, data2: "DeviceRunData"):
        """
        自定义比较函数，用于比较两个 DeviceRunData 对象是否逻辑相等，
        忽略列表中元素的顺序，并进行深度比较。
        """
        data1 = self

        result = {
            "ros_nodes": True,
            "gmsl_cameras": True,
            "ip_cameras": True,
            "eth_can": True,
        }

        # Compare ros_nodes (no special is_enable logic)
        ros_nodes_1 = sorted(data1.ros_nodes or [], key=lambda x: x.node_name)
        ros_nodes_2 = sorted(data2.ros_nodes or [], key=lambda x: x.node_name)
        result["ros_nodes"] = ros_nodes_1 == ros_nodes_2

        # Compare gmsl_cameras (with special is_enable logic)
        gmsl_cam_1 = sorted(data1.gmsl_cameras or [], key=lambda x: x.id)
        gmsl_cam_2 = sorted(data2.gmsl_cameras or [], key=lambda x: x.id)
        if len(gmsl_cam_1) != len(gmsl_cam_2):
            result["gmsl_cameras"] = False
        else:
            result["gmsl_cameras"] = all(
                (cam1.is_enable is False and cam2.is_enable is False) or (cam1 == cam2)
                for cam1, cam2 in zip(gmsl_cam_1, gmsl_cam_2)
            )

        # Compare ip_cameras (with special is_enable logic)
        ip_cam_1 = sorted(data1.ip_cameras or [], key=lambda x: x.id)
        ip_cam_2 = sorted(data2.ip_cameras or [], key=lambda x: x.id)
        if len(ip_cam_1) != len(ip_cam_2):
            result["ip_cameras"] = False
        else:
            result["ip_cameras"] = all(
                (ip_cam1.is_enable is False and ip_cam2.is_enable is False) or (ip_cam1 == ip_cam2)
                for ip_cam1, ip_cam2 in zip(ip_cam_1, ip_cam_2)
            )

        # Compare eth_can (with special is_enable logic)
        eth_can_1 = sorted(data1.eth_can or [], key=lambda x: x.id)
        eth_can_2 = sorted(data2.eth_can or [], key=lambda x: x.id)
        if len(eth_can_1) != len(eth_can_2):
            result["eth_can"] = False
        else:
            result["eth_can"] = all(
                (can1.is_enable is False and can2.is_enable is False) or (can1 == can2)
                for can1, can2 in zip(eth_can_1, eth_can_2)
            )

        return result


class VehicleCmd(Enum):
    sync_check = "sync.check"
    sync_data = "sync.data"
    create_node = "ros.create_node"
    control_node = "ros.control_node"
    update_node_param = "ros.update_node_param"
    set_logtrack_node = "ros.set_logtrack_node"
    set_ipc = "ros.set_ipc"
    set_gmsl = "sys.set_gmsl"
    set_can = "sys.set_can"


class MsgModel(BaseModel):
    id: str = ""
    room_id: Optional[str] = None
    user: Optional[str] = None
    cmd: str
    data: Any


class PingPongModel(MsgModel):
    cmd: Annotated[str, CommonCmd]


class DataMangeMsg(MsgModel):
    cmd: Union[Annotated[str, CommonCmd], str]
    data: Union[
        List[RecordMqttTask],  # mqtt录制任务
        List[RecordVideoTask],  # 视频录制任务
        Dict,
    ] = {}


class VehicleMsg(MsgModel):
    cmd: Union[Annotated[str, VehicleCmd], Annotated[str, CommonCmd]]
    data: Union[
        Node,  # ros节点
        NodeCrontrol,  # 节点控制
        UpdateParams,  # ros节点更新参数
        DeviceRunData,  # 设备同步数据
        Dict,  #
    ] = {}


class OPCCmd(Enum):
    status_info = "status.opc.info"
    update_params = "service.opc.update_params"


class OPCMsg(MsgModel):
    cmd: Union[Annotated[str, OPCCmd], Annotated[str, CommonCmd]]
    data: Dict = {}


class UserMsg(MsgModel):
    data: Dict = {}
