<template>
  <el-row>
    <el-input v-model="mseUrl" placeholder="请输入" size="default" clearable></el-input>
    <el-button type="primary" size="default" @click="play()">播放</el-button>
  </el-row>
  <div id="myDiv">
    <video id="mse-video" autoplay muted playsinline controls style="max-width: 100%; max-height: 100%"></video>
  </div>
</template>
<script lang="ts" setup>
const mseUrl = ref("");

const mseQueue: any = [];
let mseSourceBuffer: any;
let mseStreamingStarted = false;

function readPacket(packet) {
  if (!mseStreamingStarted) {
    mseSourceBuffer.appendBuffer(packet);
    mseStreamingStarted = true;
    return;
  }
  mseQueue.push(packet);
  if (!mseSourceBuffer.updating) {
    pushPacket();
  }
}

function pushPacket() {
  let packet;
  const videoEl = document.querySelector("#mse-video") as any;

  if (!mseSourceBuffer.updating) {
    if (mseQueue.length > 0) {
      packet = mseQueue.shift();
      mseSourceBuffer.appendBuffer(packet);
    } else {
      mseStreamingStarted = false;
    }
  }
  if (videoEl.buffered.length > 0) {
    if (typeof document.hidden !== "undefined" && document.hidden) {
      // no sound, browser paused video without sound in background
      videoEl.currentTime = videoEl.buffered.end(videoEl.buffered.length - 1) - 0.5;
    }
  }
}

let ws: any = null;
function startPlay(url) {
  const mse = new MediaSource();
  const videoEl = document.querySelector("#mse-video") as any;
  videoEl.src = window.URL.createObjectURL(mse);
  mse.addEventListener(
    "sourceopen",
    function () {
      if (ws) ws.close();
      ws = new WebSocket(url);
      ws.binaryType = "arraybuffer";
      ws.onopen = function (event) {
        console.log("Connect to ws");
      };
      ws.onmessage = function (event) {
        const data = new Uint8Array(event.data);
        if (data[0] === 9) {
          let mimeCodec;
          const decodedArr = data.slice(1);
          if (window.TextDecoder) {
            mimeCodec = new TextDecoder("utf-8").decode(decodedArr);
          } else {
            mimeCodec = Utf8ArrayToStr(decodedArr);
          }
          mseSourceBuffer = mse.addSourceBuffer('video/mp4; codecs="' + mimeCodec + '"');
          mseSourceBuffer.mode = "segments";
          mseSourceBuffer.addEventListener("updateend", pushPacket);
        } else {
          readPacket(event.data);
        }
      };
    },
    false
  );
}

const play = () => {
  startPlay(mseUrl.value);
};

onMounted(() => {
  const videoEl = document.querySelector("#mse-video") as any;

  videoEl.addEventListener("pause", () => {
    if (videoEl.currentTime > videoEl.buffered.end(videoEl.buffered.length - 1)) {
      videoEl.currentTime = videoEl.buffered.end(videoEl.buffered.length - 1) - 0.1;
      videoEl.play();
    }
  });
});

onUnmounted(() => {
  ws.close();
});
</script>
<style lang="scss" scoped></style>
