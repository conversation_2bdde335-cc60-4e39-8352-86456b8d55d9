<template>
  <div class="reset">
    <div class="reset-wrap">
      <div class="reset-title">重置密码</div>
      <el-form ref="formRef" :model="formData" label-width="80px" :rules="formRules">
        <el-form-item label="新密码" prop="password">
          <el-input v-model="formData.password" placeholder="请输入新密码" clearable type="password" />
        </el-form-item>
        <el-form-item label="确认密码" prop="password_again">
          <el-input v-model="formData.password_again" placeholder="请输入再次输入" clearable type="password" />
        </el-form-item>
      </el-form>
      <div class="w-full">
        <el-button class="w-[200px] mx-auto block mt-7" type="primary" size="default" @click="submitForm"
          >确认重置</el-button
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { type FormInstance } from "element-plus";
import feedback from "@/utils/feedback";
import { adminReset } from "@/api/perms/admin";
import { useRoute } from "vue-router";

const route = useRoute();
const router = useRouter();

const formData = reactive({
  token: "",
  password: "",
  password_again: "",
});

const formRef = ref<FormInstance>();

const validatePasswordAgain = (rule: any, value: string, callback: any) => {
  if (value !== formData.password) {
    callback(new Error("两次输入的密码不一致"));
  } else {
    callback();
  }
};

const formRules = reactive({
  password: [
    {
      required: true,
      message: "请输入新密码",
      trigger: ["blur"],
    },
    {
      min: 8,
      message: "密码长度不能少于 8 位",
      trigger: ["blur", "change"],
    },
  ],
  password_again: [
    {
      required: true,
      message: "请输入再次输入",
      trigger: ["blur"],
    },
    { validator: validatePasswordAgain, trigger: ["blur", "change"] },
  ],
});

const submitForm = async () => {
  await formRef.value?.validate();
  await adminReset(formData);
  feedback.msgSuccess("密码重置成功");
  router.push("/login");
};

onMounted(() => {
  console.log(route.query);
  const { token } = route.query;
  if (token) formData.token = token as string;
});
</script>

<style lang="scss" scoped>
.reset {
  width: 100vw;
  height: 100vh;
  background-color: #f3f3f4;
  padding-top: 100px;
  &-wrap {
    padding: 32px;
    width: 460px;
    margin: auto;
    border-radius: 8px;
    background-color: #ffffff;
  }
  &-title {
    text-align: center;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 24px;
  }
}
</style>
