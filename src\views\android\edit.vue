<template>
  <div class="edit-popup">
    <popup
      ref="popupRef"
      :confirmButtonText="$t('vehicle.确定')"
      :cancelButtonText="$t('vehicle.取消')"
      :title="popupTitle"
      :async="true"
      width="550px"
      @confirm="handleSubmit"
      @close="handleClose"
    >
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item :label="$t('vehicle.操作台名称')" prop="name">
          <el-input v-model="formData.name" :placeholder="$t('vehicle.请输入')" clearable />
        </el-form-item>
        <el-form-item :label="$t('vehicle.可控车辆')" prop="vehicle_type">
          <el-select v-model="formData.vehicle_type" :placeholder="$t('vehicle.请选择')" multiple clearable>
            <el-option
              v-for="(item, key, index) in pageData.vehicleTypeList"
              :key="key"
              :label="item"
              :value="Number(key)"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import { consoleAdd, consoleUpdate, consolesDetail } from "@/api/android";
import { vehiclesList } from "@/api/device";
import feedback from "@/utils/feedback";
import { useI18n } from "vue-i18n";
import useMetadataStore from "@/stores/modules/metadata";
const metadataStore = useMetadataStore();

const { t, locale } = useI18n();
const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? t("vehicle.编辑操作台") : t("vehicle.添加操作台");
});

const formData: any = reactive({
  id: "",
  name: "",
  vehicle_type: "",
});

const vehicle_list: any = ref([]);

const formRules = reactive({
  name: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
  vehicle_type: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
});

const pageData: any = reactive({
  vehicleTypeList: {},
});

onMounted(() => {
  getVehicleTypeList();
});

const getVehicleTypeList = async () => {
  pageData.vehicleTypeList = await metadataStore.fetchMetadata("VEHICLE_TYPE", "kv");
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  mode.value == "edit" ? await consoleUpdate(formData) : await consoleAdd(formData);
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
  getLists();
};

const getLists = async () => {
  const { lists } = await vehiclesList({ page_no: 1, page_size: 99 });
  vehicle_list.value = lists;
};

const setFormData = async (row: any) => {
  // 获取设备信息
  // const res = await consolesDetail(row.id);
  const data = { id: row.id, ...row };
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
