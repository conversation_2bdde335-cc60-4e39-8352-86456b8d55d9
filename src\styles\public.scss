@use './variables.scss' as *;

@if $buildMode and $buildMode != 'lite' {
  @font-face {
    font-family: "Meiryo"; /* 自定义字体名称 */
    src: url("/src/assets/font/meiryo.ttc"); /* 字体路径 */
    font-weight: normal; /* 字体粗细 */
    font-style: normal; /* 字体样式 */
  }
  @font-face {
    font-family: 'OPPOSans';
    src: url('/src/assets/font/OPPOSans-R.ttf');
    font-weight: normal;
    font-style: normal;
  }
}

body {
  @apply text-base text-tx-primary overflow-hidden min-w-[375px];
}

[lang]:lang(ja) {
  font-family: "Meiryo", "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Segoe UI", "Roboto", "Oxygen", "Ubuntu",
    "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

[lang]:lang(zh-CN) {
  font-family: "OPPOSans", "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Segoe UI", "Roboto", "Oxygen", "Ubuntu",
    "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

.form-tips {
  @apply text-tx-secondary text-xs leading-6 mt-1;
}

.clearfix:after {
  content: "";
  display: block;
  clear: both;
  visibility: hidden;
}

/* NProgress */
#nprogress .bar {
  background-color: var(--el-color-primary) !important;
}

/* 单行超长省略号 */
.one-line-ellipsis {
  overflow: hidden;

  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 两行超长省略号 */
.two-line-ellipsis {
  display: -webkit-box;
  overflow: hidden;
  word-break: break-all;
  -webkit-box-orient: vertical;

  text-overflow: ellipsis;

  -webkit-line-clamp: 2;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
}

:hover::-webkit-scrollbar-thumb {
  background: #dde1e7;
}

:hover::-webkit-scrollbar-track {
  background: transparent;
}
