<template>
  <div v-if="isShow" class="overlay"></div>
  <div v-if="isShow" class="popup">
    <StreamConsole :vehicleId="props.vehicleId" :token="userStore.token" @close="handleClose"></StreamConsole>
  </div>
</template>
<script lang="ts" setup>
import StreamConsole from "./stream-console.vue";
import useUserStore from "@/stores/modules/user";
const userStore = useUserStore();

const props = defineProps({
  vehicleId: {
    type: String,
    require: true,
  },
});

const emit = defineEmits(["success", "close"]);

const isShow = ref(false);
const open = () => {
  isShow.value = true;
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
});
</script>

<style scoped lang="scss">
.popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
  z-index: 3001;
  width: 1080px;
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 3001;
}
</style>
