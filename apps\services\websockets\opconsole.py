"""
操作台 WebSocket 服务
"""

import anyio
from bson import ObjectId
from fastapi import WebSocket

import apps.models.cache as Cache
import apps.models.websocket as WSModel
import apps.services.opconsole as OPCService
from apps.services.common import get_header_info
from .base import WebSockClient


class OPCSockClient(WebSockClient):
    """操作台WebSocket客户端链接"""

    def __init__(self, wsock: WebSocket, device_id: str, opc_id: ObjectId):
        super().__init__(wsock)
        self.device_id = device_id
        self.opc_id = str(opc_id)
        self.dev_cache = Cache.OPCDevice(device_id)
        self.opc_cache = Cache.OPC(opc_id)

    async def init(self):
        await self.wsock.accept()
        await self.opc_cache.online_status.set(1)
        # await self.send_opc_info()

    async def clean(self):
        # 清除所有房间消息订阅
        self.rooms.clear()
        # 解锁所有锁定的车辆
        await self.opc_cache.online_status.delete()
        locked_vehicles = await self.opc_cache.locked_vehicles.sget()
        for v_id in locked_vehicles:
            await Cache.Vehicle(v_id).locked_op_console.delete()
        await self.opc_cache.locked_vehicles.delete()

    async def send_opc_info(self):
        """发送操作台的一些信息"""
        msg = WSModel.OPCMsg(
            cmd=WSModel.OPCCmd.status_info.value,
            data={"_id": self.opc_id},
        )
        await self.send_msg(msg.model_dump_json())

    async def process_msg(self):
        """处理客户端发送的消息"""
        async with anyio.create_task_group() as tg:
            tg.start_soon(self.sub_room_msg, self.opc_id)
            async for msg in self.recv_socket_msg():
                if msg.room_id:
                    await self.send_room_msg(msg)
                    continue
                if msg.cmd == "ping":
                    await self.send_pong_msg(msg)
                elif msg.cmd.startswith("room."):
                    await self.room_switch(msg, tg)
            tg.cancel_scope.cancel()

    async def process_room_msg(self, msg: WSModel.MsgModel):
        """处理房间消息"""
        if msg.user == self.opc_id:
            return
        await self.send_msg(msg.model_dump_json())

    async def send_room_msg(self, msg: WSModel.MsgModel):
        """发送消息到房间"""
        msg.user = self.opc_id
        # TODO: 这里需要判断是否有权限发送消息到目标房间
        assert msg.room_id
        await self.broadcast_room_msg(msg.room_id, msg)


async def process_opc(device_id: str, wsock: WebSocket):
    """处理操作台设备连接"""
    device_info = get_header_info(wsock)
    opc_info = await OPCService.get_opc_id_for_device(device_id, device_info)
    opc_id = opc_info["opc_id"]

    wsock.state.client_type = "opc"
    wsock.state.client_id = device_id
    wc = OPCSockClient(wsock, device_id, opc_id)
    try:
        await wc.init()
        await wc.process_msg()
    except Exception as e:  # pylint: disable=broad-except
        print(f"process op_console error:{e}")
    finally:
        await wc.clean()
    del wc
