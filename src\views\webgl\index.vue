<template>
  <div ref="threeRef" class="h-[100vh] w-[100vw] bg-white"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as THREE from "three";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";

const threeRef = ref<HTMLDivElement | null>(null);

let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let controls: OrbitControls;
let animationFrameId: number;
let mixer: THREE.AnimationMixer | null = null; // 动画混合器
let clock: THREE.Clock; // 用于动画更新

// 挖掘机骨骼节点引用
let upperBody: THREE.Object3D | null = null; // 上部回转体
let boom: THREE.Object3D | null = null; // 大臂
let arm: THREE.Object3D | null = null; // 小臂
let bucket: THREE.Object3D | null = null; // 铲斗
let leftTrack: THREE.Object3D | null = null; // 左履带
let rightTrack: THREE.Object3D | null = null; // 右履带
let leftJoystick: THREE.Object3D | null = null; // 左操纵杆
let rightJoystick: THREE.Object3D | null = null; // 右操纵杆

// 控制状态
const isUpperBodyRotatingLeft = ref(false); // 上部左转
const isUpperBodyRotatingRight = ref(false); // 上部右转
const isBoomRaising = ref(false); // 大臂上升
const isBoomLowering = ref(false); // 大臂下降
const isArmExtending = ref(false); // 小臂伸出
const isArmRetracting = ref(false); // 小臂收回
const isBucketDigging = ref(false); // 铲斗挖掘
const isBucketDumping = ref(false); // 铲斗倾倒
const isLeftTrackForward = ref(false); // 左履带前进
const isLeftTrackBackward = ref(false); // 左履带后退
const isRightTrackForward = ref(false); // 右履带前进
const isRightTrackBackward = ref(false); // 右履带后退

// 旋转速度和角度限制
let rotationSpeed = 0.02; // 旋转速度
let boomAngleLimit = { min: -0.5, max: 0.8 }; // 大臂角度限制
let armAngleLimit = { min: -1.0, max: 0.2 }; // 小臂角度限制
let bucketAngleLimit = { min: -0.8, max: 0.5 }; // 铲斗角度限制

onMounted(() => {
  if (!threeRef.value) return;

  console.log("threeRef", threeRef);

  const container = threeRef.value;

  // 1. Scene
  scene = new THREE.Scene();
  clock = new THREE.Clock(); // 初始化时钟
  scene.background = new THREE.Color(0xffffff); // 设置背景颜色为白色

  // 2. Camera
  camera = new THREE.PerspectiveCamera(
    75, // fov
    container.clientWidth / container.clientHeight, // aspect
    0.1, // near
    1000 // far
  );
  camera.position.set(5, 5, 5); // 调整相机位置以便更好地观察模型
  camera.lookAt(scene.position);

  // 3. Renderer
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(container.clientWidth, container.clientHeight);
  renderer.setPixelRatio(window.devicePixelRatio);
  container.appendChild(renderer.domElement);

  // 4. Lighting
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 柔和的白光
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 1); // 平行光
  directionalLight.position.set(5, 10, 7.5);
  scene.add(directionalLight);

  // 5. Controls
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true; // 启用阻尼效果，使控制更平滑
  controls.dampingFactor = 0.05;
  controls.screenSpacePanning = false; // true:右键平移 false:右键旋转
  controls.minDistance = 2; // 相机最小缩放距离
  controls.maxDistance = 50; // 相机最大缩放距离
  // controls.maxPolarAngle = Math.PI / 2; // 限制相机垂直旋转角度

  // 6. Load Model
  const loader = new GLTFLoader();
  // 注意：这里的路径是相对于 `public` 文件夹的，或者需要构建工具正确处理。
  // 如果 `car.glb` 在 `src/views/webgl/model/car.glb` 并且你使用 Vite,
  // 你可以这样获取 URL:
  const modelURL = new URL("./model/excavator.glb", import.meta.url).href;
  // 如果模型在 public/model/car.glb, 则路径为 '/model/car.glb'
  console.log(modelURL);

  loader.load(
    modelURL, // 使用 Vite 的方式处理 src 目录下的资源
    (gltf) => {
      const model = gltf.scene;
      console.log('Loaded model:', model);

      // --- 骨骼和动画检测与使用 ---
      let hasSkinnedMesh = false;
      model.traverse((object) => {
        if (object instanceof THREE.SkinnedMesh) {
          hasSkinnedMesh = true;
          console.log('Found SkinnedMesh:', object);
          
          // 打印所有骨骼名称，帮助调试
          if (object.skeleton && object.skeleton.bones) {
            console.log('Available bones in this SkinnedMesh:');
            object.skeleton.bones.forEach(bone => {
              console.log(`- Bone Name: ${bone.name}`);
            });
          }
        }
        
        // 根据骨骼层级结构查找并引用挖掘机的各个部件
        if (object.name === 'up_body_03') {
          upperBody = object;
          console.log('找到上部回转体:', object.name);
        } else if (object.name === 'digger_01_015') {
          boom = object;
          console.log('找到大臂:', object.name);
        } else if (object.name === 'digger_02_018') {
          arm = object;
          console.log('找到小臂:', object.name);
        } else if (object.name === 'digger_07_021' || object.name === 'digger_08_022') {
          bucket = object;
          console.log('找到铲斗:', object.name);
        } else if (object.name.includes('chilun_L') || object.name.includes('F_L_') || object.name.includes('B_L_')) {
          // 左侧履带相关部件
          if (!leftTrack && (object.name === 'chilun_L0_057' || object.name === 'chilun_L1_056')) {
            leftTrack = object;
            console.log('找到左履带:', object.name);
          }
        } else if (object.name.includes('chilun_R') || object.name.includes('F_R_') || object.name.includes('B_R_')) {
          // 右侧履带相关部件
          if (!rightTrack && (object.name === 'chilun_R0_059' || object.name === 'chilun_R1_058')) {
            rightTrack = object;
            console.log('找到右履带:', object.name);
          }
        } else if (object.name === 'rocker00_l_013' || object.name === 'rocker01_l_014') {
          // 左操纵杆
          leftJoystick = object;
          console.log('找到左操纵杆:', object.name);
        } else if (object.name === 'rocker00_r_011' || object.name === 'rocker01_r_012') {
          // 右操纵杆
          rightJoystick = object;
          console.log('找到右操纵杆:', object.name);
        }
      });

      if (hasSkinnedMesh) {
        console.log('模型包含骨骼 (SkinnedMesh).');
      } else {
        console.log('模型不包含 SkinnedMesh，可能没有骨骼动画或骨骼未正确导出/识别。');
      }
      
      // 检查是否找到了所有需要的部件
      console.log('挖掘机部件引用状态:');
      console.log('- 上部回转体:', upperBody ? '已找到' : '未找到');
      console.log('- 大臂:', boom ? '已找到' : '未找到');
      console.log('- 小臂:', arm ? '已找到' : '未找到');
      console.log('- 铲斗:', bucket ? '已找到' : '未找到');
      console.log('- 左履带:', leftTrack ? '已找到' : '未找到');
      console.log('- 右履带:', rightTrack ? '已找到' : '未找到');
      console.log('- 左操纵杆:', leftJoystick ? '已找到' : '未找到');
      console.log('- 右操纵杆:', rightJoystick ? '已找到' : '未找到');
      
      // 如果未找到关键部件，输出警告
      if (!upperBody || !boom || !arm || !bucket || !leftTrack || !rightTrack) {
        console.warn('部分挖掘机关键部件未找到，请检查模型结构或命名。');
      }

      scene.add(model);

      const box = new THREE.Box3().setFromObject(model);
      const center = box.getCenter(new THREE.Vector3());
      const size = box.getSize(new THREE.Vector3());
      const maxDim = Math.max(size.x, size.y, size.z);
      const fov = camera.fov * (Math.PI / 180);
      let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
      cameraZ *= 1.5; 

      camera.position.set(center.x, center.y + size.y / 2, center.z + cameraZ);
      controls.target.copy(center);
      controls.update();
    },
    undefined, // onProgress callback (optional)
    (error) => {
      console.error("An error happened during model loading:", error);
    }
  );

  // 7. Animation Loop
  const animate = () => {
    animationFrameId = requestAnimationFrame(animate);
    controls.update(); // 只有在 enableDamping = true 时才需要

    const delta = clock.getDelta(); // 获取自上一帧以来的时间差
    if (mixer) {
      mixer.update(delta); // 更新动画混合器
    }

    // --- 控制挖掘机移动 ---
    // 1. 上部回转控制
    if (upperBody) {
      if (isUpperBodyRotatingLeft.value) {
        upperBody.rotation.y += rotationSpeed; // 左转
      } else if (isUpperBodyRotatingRight.value) {
        upperBody.rotation.y -= rotationSpeed; // 右转
      }
    }

    // 2. 大臂控制
    if (boom) {
      if (isBoomRaising.value) {
        // 大臂上升 (绕X轴旋转)
        boom.rotation.x = Math.max(boom.rotation.x - rotationSpeed, boomAngleLimit.min);
      } else if (isBoomLowering.value) {
        // 大臂下降
        boom.rotation.x = Math.min(boom.rotation.x + rotationSpeed, boomAngleLimit.max);
      }
    }

    // 3. 小臂控制
    if (arm) {
      if (isArmExtending.value) {
        // 小臂伸出
        arm.rotation.x = Math.max(arm.rotation.x - rotationSpeed, armAngleLimit.min);
      } else if (isArmRetracting.value) {
        // 小臂收回
        arm.rotation.x = Math.min(arm.rotation.x + rotationSpeed, armAngleLimit.max);
      }
    }

    // 4. 铲斗控制
    if (bucket) {
      if (isBucketDigging.value) {
        // 铲斗挖掘
        bucket.rotation.x = Math.max(bucket.rotation.x - rotationSpeed, bucketAngleLimit.min);
      } else if (isBucketDumping.value) {
        // 铲斗倾倒
        bucket.rotation.x = Math.min(bucket.rotation.x + rotationSpeed, bucketAngleLimit.max);
      }
    }

    // 5. 履带控制
    // 左履带
    if (leftTrack) {
      if (isLeftTrackForward.value) {
        // 左履带前进
        leftTrack.rotation.z -= rotationSpeed;
      } else if (isLeftTrackBackward.value) {
        // 左履带后退
        leftTrack.rotation.z += rotationSpeed;
      }
    }

    // 右履带
    if (rightTrack) {
      if (isRightTrackForward.value) {
        // 右履带前进
        rightTrack.rotation.z -= rotationSpeed;
      } else if (isRightTrackBackward.value) {
        // 右履带后退
        rightTrack.rotation.z += rotationSpeed;
      }
    }

    // 6. 操纵杆动画 - 根据当前操作状态同步操纵杆位置
    if (leftJoystick) {
      // 左操纵杆控制大臂和回转
      if (isBoomRaising.value) {
        leftJoystick.rotation.x = -0.3; // 向前推
      } else if (isBoomLowering.value) {
        leftJoystick.rotation.x = 0.3; // 向后拉
      } else {
        leftJoystick.rotation.x = 0; // 回中
      }

      if (isUpperBodyRotatingLeft.value) {
        leftJoystick.rotation.z = 0.3; // 向左推
      } else if (isUpperBodyRotatingRight.value) {
        leftJoystick.rotation.z = -0.3; // 向右推
      } else {
        leftJoystick.rotation.z = 0; // 回中
      }
    }

    if (rightJoystick) {
      // 右操纵杆控制小臂和铲斗
      if (isArmExtending.value) {
        rightJoystick.rotation.x = -0.3; // 向前推
      } else if (isArmRetracting.value) {
        rightJoystick.rotation.x = 0.3; // 向后拉
      } else {
        rightJoystick.rotation.x = 0; // 回中
      }

      if (isBucketDigging.value) {
        rightJoystick.rotation.z = 0.3; // 向左推
      } else if (isBucketDumping.value) {
        rightJoystick.rotation.z = -0.3; // 向右推
      } else {
        rightJoystick.rotation.z = 0; // 回中
      }
    }
    // --- 控制挖掘机移动结束 ---
    renderer.render(scene, camera);
  };
  animate();

  // 8. Resize Handler
  const handleResize = () => {
    if (container) {
      camera.aspect = container.clientWidth / container.clientHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(container.clientWidth, container.clientHeight);
    }
  };
  window.addEventListener("resize", handleResize);

  // 添加键盘事件监听器
  // 键盘控制说明：
  // 左操纵杆 (WASD):
  // - W: 大臂上升
  // - S: 大臂下降
  // - A: 上部左转
  // - D: 上部右转
  // 右操纵杆 (方向键):
  // - ↑: 小臂伸出
  // - ↓: 小臂收回
  // - ←: 铲斗挖掘
  // - →: 铲斗倾倒
  // 履带控制 (QEZC):
  // - Q: 左履带前进
  // - E: 右履带前进
  // - Z: 左履带后退
  // - C: 右履带后退
  const handleKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      // 左操纵杆控制 - 大臂和上部回转
      case 'w':
      case 'W':
        isBoomRaising.value = true;
        console.log('W键按下：大臂上升');
        break;
      case 's':
      case 'S':
        isBoomLowering.value = true;
        console.log('S键按下：大臂下降');
        break;
      case 'a':
      case 'A':
        isUpperBodyRotatingLeft.value = true;
        console.log('A键按下：上部左转');
        break;
      case 'd':
      case 'D':
        isUpperBodyRotatingRight.value = true;
        console.log('D键按下：上部右转');
        break;

      // 右操纵杆控制 - 小臂和铲斗
      case 'ArrowUp':
        isArmExtending.value = true;
        console.log('↑键按下：小臂伸出');
        break;
      case 'ArrowDown':
        isArmRetracting.value = true;
        console.log('↓键按下：小臂收回');
        break;
      case 'ArrowLeft':
        isBucketDigging.value = true;
        console.log('←键按下：铲斗挖掘');
        break;
      case 'ArrowRight':
        isBucketDumping.value = true;
        console.log('→键按下：铲斗倾倒');
        break;

      // 履带控制
      case 'q':
      case 'Q':
        isLeftTrackForward.value = true;
        console.log('Q键按下：左履带前进');
        break;
      case 'e':
      case 'E':
        isRightTrackForward.value = true;
        console.log('E键按下：右履带前进');
        break;
      case 'z':
      case 'Z':
        isLeftTrackBackward.value = true;
        console.log('Z键按下：左履带后退');
        break;
      case 'c':
      case 'C':
        isRightTrackBackward.value = true;
        console.log('C键按下：右履带后退');
        break;
    }
  };

  const handleKeyUp = (event: KeyboardEvent) => {
    switch (event.key) {
      // 左操纵杆控制 - 大臂和上部回转
      case 'w':
      case 'W':
        isBoomRaising.value = false;
        console.log('W键释放：大臂停止上升');
        break;
      case 's':
      case 'S':
        isBoomLowering.value = false;
        console.log('S键释放：大臂停止下降');
        break;
      case 'a':
      case 'A':
        isUpperBodyRotatingLeft.value = false;
        console.log('A键释放：上部停止左转');
        break;
      case 'd':
      case 'D':
        isUpperBodyRotatingRight.value = false;
        console.log('D键释放：上部停止右转');
        break;

      // 右操纵杆控制 - 小臂和铲斗
      case 'ArrowUp':
        isArmExtending.value = false;
        console.log('↑键释放：小臂停止伸出');
        break;
      case 'ArrowDown':
        isArmRetracting.value = false;
        console.log('↓键释放：小臂停止收回');
        break;
      case 'ArrowLeft':
        isBucketDigging.value = false;
        console.log('←键释放：铲斗停止挖掘');
        break;
      case 'ArrowRight':
        isBucketDumping.value = false;
        console.log('→键释放：铲斗停止倾倒');
        break;

      // 履带控制
      case 'q':
      case 'Q':
        isLeftTrackForward.value = false;
        console.log('Q键释放：左履带停止前进');
        break;
      case 'e':
      case 'E':
        isRightTrackForward.value = false;
        console.log('E键释放：右履带停止前进');
        break;
      case 'z':
      case 'Z':
        isLeftTrackBackward.value = false;
        console.log('Z键释放：左履带停止后退');
        break;
      case 'c':
      case 'C':
        isRightTrackBackward.value = false;
        console.log('C键释放：右履带停止后退');
        break;
    }
  };
  
  // 在页面上显示控制提示
  console.log('挖掘机控制说明：');
  console.log('左操纵杆 (WASD):');
  console.log('- W: 大臂上升');
  console.log('- S: 大臂下降');
  console.log('- A: 上部左转');
  console.log('- D: 上部右转');
  console.log('右操纵杆 (方向键):');
  console.log('- ↑: 小臂伸出');
  console.log('- ↓: 小臂收回');
  console.log('- ←: 铲斗挖掘');
  console.log('- →: 铲斗倾倒');
  console.log('履带控制 (QEZC):');
  console.log('- Q: 左履带前进');
  console.log('- E: 右履带前进');
  console.log('- Z: 左履带后退');
  console.log('- C: 右履带后退');


  window.addEventListener('keydown', handleKeyDown);
  window.addEventListener('keyup', handleKeyUp);

  // Cleanup on unmount
  onUnmounted(() => {
    window.removeEventListener("resize", handleResize);
    window.removeEventListener('keydown', handleKeyDown); // 移除键盘事件监听器
    window.removeEventListener('keyup', handleKeyUp);   // 移除键盘事件监听器
    cancelAnimationFrame(animationFrameId);
    renderer.dispose();
    if (mixer) {
      // 停止所有正在播放的动画并清除混合器
      mixer.stopAllAction();
      // @ts-ignore three.js types might not have _actions cleared this way but it's a common practice
      mixer._actions.forEach(action => action.stop());
      // @ts-ignore
      mixer._bindings = [];
      // @ts-ignore
      mixer._cache = {};
      // @ts-ignore
      mixer = null; 
    }
    // 销毁场景中的对象
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        if (object.geometry) {
          object.geometry.dispose();
        }
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach((material) => material.dispose());
          } else {
            object.material.dispose();
          }
        }
      }
    });
    if (container && renderer.domElement) {
      container.removeChild(renderer.domElement);
    }
  });
});
</script>
