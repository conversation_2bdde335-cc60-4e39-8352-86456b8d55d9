<template>
  <div class="edit-popup">
    <popup
      ref="popupRef"
      :title="popupTitle"
      :async="true"
      width="550px"
      @confirm="handleSubmit"
      @close="handleClose"
    >
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item label="指导名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="指导描述" prop="desc">
          <el-input v-model="formData.desc" placeholder="请输入" clearable />
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";
import { guideAdd, guideEdit } from "@/api/guide-man";

const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");

const popupTitle = computed(() => ` ${mode.value === "edit" ? "编辑" : "添加"}作业指导 `);

const formData: any = reactive({
  id: "",
  name: "",
  desc: "",
});


const formRules = reactive({
  name: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
});

const pageData: any = reactive({
});

onMounted(() => {
});



const handleSubmit = async () => {
  await formRef.value?.validate();
  mode.value == "edit" ? await guideEdit(formData) : await guideAdd(formData);
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};


const setFormData = async (row: any) => {
  const data = { id: row.id, ...row };
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
