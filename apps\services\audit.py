from apps.db.mongo_db import MongoDB
import apps.models.audit as AModels


COLL = MongoDB.get_collection("audit")


async def query(q: AModels.Query) -> AModels.RecordListOut:
    """查询操作记录"""
    limit = q.page_size
    skip_no = (q.page_no - 1) * q.page_size
    filter_d = q.model_dump(
        exclude_unset=True,
        exclude_none=True,
        exclude={"page_no", "page_size", "time_order"},
    )
    if q.start_time and q.end_time:
        filter_d["created_time"] = {"$gte": q.start_time, "$lte": q.end_time}
    count = await COLL.count_documents(filter_d)
    res_data = AModels.RecordListOut(count=count, lists=[])
    if count == 0:
        return res_data

    docs = COLL.aggregate(
        [
            {"$match": filter_d},
            {"$sort": {"created_time": q.time_order}},
            {"$skip": skip_no},
            {"$limit": limit},
            {
                "$lookup": {
                    "from": "users",
                    "localField": "user_id",
                    "foreignField": "_id",
                    "as": "U",
                },
            },
            {"$unwind": "$U"},
            {"$addFields": {"username": "$U.username", "nickname": "$U.nickname"}},
            {"$project": {"U": 0}},
        ]
    )

    async for doc in docs:
        res_data.lists.append(AModels.RecordOut(**doc))
    return res_data
