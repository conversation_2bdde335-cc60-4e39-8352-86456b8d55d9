<template>
  <div class="guide-wrap">
    <el-card class="flex-1 !border-none" style="height: calc(100vh - 84px)" shadow="never">
      <el-steps :active="pageData.step" align-center>
        <el-step title="网络账户配置" description="网络参数和账户配置">
          <template #description> 填写基本信息 </template>
        </el-step>
        <el-step title="服务器配置" description="IP配置" />
        <el-step title="操作台配置" description="IP配置" />
        <el-step title="网络摄像头配置" description="IP地址，码率" />
      </el-steps>
      <!-- 第1步 -->
      <div
        v-show="pageData.step === 0"
        class="form-wrap flex flex-row mx-24 pt-12"
        style="height: calc(100vh - 91px - 32px - 160px)"
      >
        <div class="form-content mr-4 flex-1">
          <el-form :model="form" label-width="120px">
            <el-form-item label="网络账户">
              <el-input v-model="form.name" />
            </el-form-item>
            <el-form-item label="网络类型选择">
              <el-select v-model="form.region" placeholder="请选择网络类型">
                <el-option label="局域网" value="shanghai" />
                <el-option label="5G" value="beijing" />
              </el-select>
            </el-form-item>
            <el-form-item label="项目日期">
              <el-col :span="11">
                <el-date-picker v-model="form.date1" type="date" placeholder="选择日期" style="width: 100%" />
              </el-col>
              <el-col :span="2" class="text-center">
                <span class="text-gray-500">-</span>
              </el-col>
              <el-col :span="11">
                <el-time-picker v-model="form.date2" placeholder="选择日期" style="width: 100%" />
              </el-col>
            </el-form-item>
            <el-form-item label="远程上电">
              <el-switch v-model="form.delivery" />
            </el-form-item>
            <el-form-item label="部署功能">
              <el-checkbox-group v-model="form.type">
                <el-checkbox label="辅助画面" name="type" />
                <el-checkbox label="行人识别" name="type" />
                <el-checkbox label="斗齿检测" name="type" />
                <el-checkbox label="BDI检测" name="type" />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="是否远程部署">
              <el-radio-group v-model="form.resource">
                <el-radio label="是" />
                <el-radio label="否" />
              </el-radio-group>
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="form.desc" type="textarea" />
            </el-form-item>
          </el-form>
        </div>
        <div class="form-description pl-4 max-w-[30%] overflow-y-auto" style="border-left: 1px solid #ebeef5">
          <el-descriptions title="提示内容" :column="1">
            <el-descriptions-item label="IP"
              >用于在互联网上传输数据的协议。IP 地址是分配给每个设备的唯一标识符，用于识别设备并路由数据包。
              <el-image src="/src/assets/images/excavator_background11.jpg" style="width: 100%; height: 100%" />
            </el-descriptions-item>
            <el-descriptions-item label="示例">
              <el-tag size="small">***********</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="IP组成">
              在计算机网络中，IP 是 Internet Protocol 的缩写，它是用于在互联网上传输数据的协议。IP
              地址是分配给每个设备的唯一标识符，用于识别设备并路由数据包。 IP 地址由四个十进制数字组成，每个数字范围为 0
              到 255，例如 ***********。 IP 地址通常以点分十进制格式表示，例如 *********** 表示设备在网络上的 IP 地址是
              192.168.1 网段中的第 1 个设备。
            </el-descriptions-item>

            <el-descriptions-item label="类别"
              >公用 IP 地址和私有 IP 地址。公用 IP 地址是分配给互联网上的设备的地址，而私有 IP
              地址是分配给局域网中的设备的地址。
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <!-- 第2步 -->
      <div
        v-show="pageData.step === 1"
        class="form-wrap flex flex-row mx-24 pt-12"
        style="height: calc(100vh - 91px - 32px - 160px)"
      >
        <div class="form-content mr-4 flex-1">
          <el-form :model="form" label-width="120px">
            <el-form-item label="网络账户">
              <el-input v-model="form.name" />
            </el-form-item>
            <el-form-item label="网络类型选择">
              <el-select v-model="form.region" placeholder="请选择网络类型">
                <el-option label="局域网" value="shanghai" />
                <el-option label="5G" value="beijing" />
              </el-select>
            </el-form-item>

            <el-form-item label="是否远程部署">
              <el-radio-group v-model="form.resource">
                <el-radio label="是" />
                <el-radio label="否" />
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
        <div class="form-description pl-4 max-w-[30%]" style="border-left: 1px solid #ebeef5">
          <el-descriptions title="提示内容" :column="1">
            <el-descriptions-item label="IP"
              >用于在互联网上传输数据的协议。IP
              地址是分配给每个设备的唯一标识符，用于识别设备并路由数据包。</el-descriptions-item
            >
            <el-descriptions-item label="IP组成">
              在计算机网络中，IP 是 Internet Protocol 的缩写，它是用于在互联网上传输数据的协议。IP
              地址是分配给每个设备的唯一标识符，用于识别设备并路由数据包。 IP 地址由四个十进制数字组成，每个数字范围为 0
              到 255，例如 ***********。 IP 地址通常以点分十进制格式表示，例如 *********** 表示设备在网络上的 IP 地址是
              192.168.1 网段中的第 1 个设备。</el-descriptions-item
            >
          </el-descriptions>
        </div>
      </div>
      <!-- 第3步 -->
      <div
        v-show="pageData.step === 2"
        class="form-wrap flex flex-row mx-24 pt-12"
        style="height: calc(100vh - 91px - 32px - 160px)"
      >
        <div class="form-content mr-4 flex-1">
          <el-form :model="form" label-width="120px">
            <el-form-item label="远程上电">
              <el-switch v-model="form.delivery" />
            </el-form-item>
            <el-form-item label="部署功能">
              <el-checkbox-group v-model="form.type">
                <el-checkbox label="辅助画面" name="type" />
                <el-checkbox label="行人识别" name="type" />
                <el-checkbox label="斗齿检测" name="type" />
                <el-checkbox label="BDI检测" name="type" />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="是否远程部署">
              <el-radio-group v-model="form.resource">
                <el-radio label="是" />
                <el-radio label="否" />
              </el-radio-group>
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="form.desc" type="textarea" />
            </el-form-item>
          </el-form>
        </div>
        <div class="form-description pl-4 max-w-[30%]" style="border-left: 1px solid #ebeef5">
          <el-descriptions title="提示内容" :column="1">
            <el-descriptions-item label="IP"
              >用于在互联网上传输数据的协议。IP
              地址是分配给每个设备的唯一标识符，用于识别设备并路由数据包。</el-descriptions-item
            >

            <el-descriptions-item label="类别"
              >公用 IP 地址和私有 IP 地址。公用 IP 地址是分配给互联网上的设备的地址，而私有 IP
              地址是分配给局域网中的设备的地址。
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <!-- 第4步 -->
      <div
        v-show="pageData.step === 3"
        class="form-wrap flex flex-row mx-24 pt-12"
        style="height: calc(100vh - 91px - 32px - 160px)"
      >
        <div class="form-content mr-4 flex-1">
          <el-form :model="form" label-width="120px">
            <el-form-item label="远程上电">
              <el-switch v-model="form.delivery" />
            </el-form-item>
            <el-form-item label="部署功能">
              <el-checkbox-group v-model="form.type">
                <el-checkbox label="辅助画面" name="type" />
                <el-checkbox label="行人识别" name="type" />
                <el-checkbox label="斗齿检测" name="type" />
                <el-checkbox label="BDI检测" name="type" />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="是否远程部署">
              <el-radio-group v-model="form.resource">
                <el-radio label="是" />
                <el-radio label="否" />
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
        <div class="form-description pl-4 max-w-[30%]" style="border-left: 1px solid #ebeef5">
          <el-descriptions title="提示内容" :column="1">
            <el-descriptions-item label="示例">
              <el-tag size="small">***********</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="类别"
              >公用 IP 地址和私有 IP 地址。公用 IP 地址是分配给互联网上的设备的地址，而私有 IP
              地址是分配给局域网中的设备的地址。
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <!-- 完成 -->
      <div
        v-show="pageData.step === 4"
        class="flex justify-center mx-24 pt-12"
        style="height: calc(100vh - 91px - 32px - 160px)"
      >
        <el-result icon="success" title="成功" sub-title="成功提交成功提交成功提交">
          <template #extra>
            <el-button type="primary" @click="pageData.step = 0">返回</el-button>
          </template>
        </el-result>
      </div>
      <div class="footer-wrap flex justify-end mx-24 mt-3" v-show="pageData.step < 4">
        <el-button type="primary" @click="handleJump('back')"> 上一步 </el-button>
        <el-button type="primary" @click="handleJump('next')">
          {{ pageData.step === 3 ? "提交" : "下一步" }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup name="guide">
const pageData = reactive({
  step: 0,
});

const form = reactive({
  name: "",
  region: "",
  date1: "",
  date2: "",
  delivery: false,
  type: [],
  resource: "",
  desc: "",
});

const handleJump = (type: string) => {
  if (type === "back" && pageData.step > 0) {
    pageData.step--;
  } else if (type === "next" && pageData.step < 4) {
    pageData.step++;
  }
};
</script>

<style lang="scss" scoped>
:deep(.is-process) {
  color: var(--el-text-color-regular);
}
</style>
