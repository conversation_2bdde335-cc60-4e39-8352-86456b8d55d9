"""
    内部接口，模块直接调用，不需要鉴权
"""

from fastapi import APIRouter, Request, HTTPException

from apps.db import RedisDB
from apps.db.mongo_db import MongoUtil

import apps.services.internal as InternalService

router = APIRouter(tags=["内部接口"])


@router.get("/check_db")
async def check_db(_: Request):
    """check db"""
    try:
        await RedisDB.info()
        await MongoUtil.get_info()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    return {"msg": "ok"}


@router.post("/init_data")
async def init_db_data(req: Request):
    """初始化业务运行必要数据"""
    srv = InternalService.InitDataBase()
    if await srv.init_data():
        return {"msg": "Initialization successful"}
    return {"msg": "It's already initialized"}


# @router.websocket("/data_manager/ws")
# async def vehicle_device_endpoint(wsock: WebSocket):
#     """处理数据收集模块的 WebScoket 连接"""
#     return await WService.process_data_manage(wsock)
