import request from "@/utils/request";

const API_VERSION = 'v2';

// 人员列表
export function adminLists(params: any) {
  return request.get({ url: `/${API_VERSION}/user/`, params });
}

// 人员添加
export function adminAdd(params: any) {
  return request.post({ url: `/${API_VERSION}/user/`, params });
}

// 人员详情
export function adminDetail(params: any) {
  return request.get({ url: `/${API_VERSION}/user/detail`, params });
}

// 人员编辑
export function adminEdit(params: any) {
  return request.put({ url: `/${API_VERSION}/user/`, params });
}

// 人员删除
export function adminDelete(params: any) {
  return request.delete({ url: `/${API_VERSION}/user/`, params });
}

// 人员状态改变
export function adminStatus(params: any) {
  return request.put({ url: `/${API_VERSION}/user/status`, params });
}

// 人员密码重置链接
export function adminResetRequest(params: any) {
  return request.post({ url: `/${API_VERSION}/user/reset_password`, params });
}

// 人员密码重置
export function adminReset(params: any) {
  return request.post({ url: `/${API_VERSION}/user/me/reset_password`, params });
}
