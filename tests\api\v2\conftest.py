import pytest

from starlette.testclient import Test<PERSON>lient


def user_login(client: TestClient, username, password) -> str:
    """登录"""
    res = client.post("/user/login", json={"username": username, "password": password})
    assert res.status_code == 200
    assert res.json()["code"] == 1000
    return res.json()["data"]["token"]


@pytest.fixture(scope="session")
def client(app_run):
    with TestClient(app_run) as ci:
        ci.base_url = "http://test/api/v2"
        yield ci


@pytest.fixture(scope="session")
def staff_header(client):
    token = user_login(client, "staff", "Builder@X")
    yield {"Authorization": f"Bearer {token}"}


@pytest.fixture(scope="session")
def admin_header(client):
    token = user_login(client, "admin", "BuilderX@2024")
    yield {"Authorization": f"Bearer {token}"}
