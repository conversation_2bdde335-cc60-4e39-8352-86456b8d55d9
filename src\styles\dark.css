:root.dark {
    color-scheme: dark;
    --table-header-bg-color: var(--el-bg-color);
    --el-bg-color-page: #0a0a0a;
    --el-bg-color: #1d2124;
    --el-bg-color-overlay: #1d1e1f;
    --el-text-color-primary: #e5eaf3;
    --el-text-color-regular: #cfd3dc;
    --el-text-color-secondary: #a3a6ad;
    --el-text-color-placeholder: #8d9095;
    --el-text-color-disabled: #6c6e72;
    --el-border-color-darker: #636466;
    --el-border-color-dark: #58585b;
    --el-border-color: #4c4d4f;
    --el-border-color-light: #414243;
    --el-border-color-lighter: #363637;
    --el-border-color-extra-light: #2b2b2c;
    --el-fill-color-darker: #424243;
    --el-fill-color-dark: #39393a;
    --el-fill-color: #303030;
    --el-fill-color-light: #262727;
    --el-fill-color-lighter: #1d1d1d;
    --el-fill-color-extra-light: #191919;
    --el-fill-color-blank: var(--el-bg-color);
    --el-mask-color: rgba(0, 0, 0, 0.8);
    --el-mask-color-extra-light: rgba(0, 0, 0, 0.3);
    --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.36), 0px 8px 20px rgba(0, 0, 0, 0.72);
    --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.72);
    --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.72);
    --el-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.72), 0px 12px 32px #000000,
        0px 8px 16px -8px #000000 !important;
    /* wangeditor主题 */
    --w-e-textarea-bg-color: var(--el-bg-color);
    --w-e-textarea-color: var(--el-text-color-primary);
    --w-e-textarea-border-color: var(--el-border-color);
    --w-e-textarea-slight-border-color: var(--el-border-color-light);
    --w-e-textarea-slight-color: var(--el-border-color);
    --w-e-textarea-slight-bg-color: var(--el-bg-color-page);
    /* --w-e-textarea-selected-border-color: #b4d5ff;
  --w-e-textarea-handler-bg-color: #4290f7; */
    --w-e-toolbar-color: var(--el-text-color-primary);
    --w-e-toolbar-bg-color: var(--el-bg-color);
    --w-e-toolbar-active-color: var(--el-text-color-primary);
    --w-e-toolbar-active-bg-color: var(--el-bg-color);
    --w-e-toolbar-disabled-color: var(--el-text-color-disabled);
    --w-e-toolbar-border-color: var(--el-border-color);
    --w-e-modal-button-bg-color: var(--el-bg-color);
    --w-e-modal-button-border-color: var(--el-border-color);
}
