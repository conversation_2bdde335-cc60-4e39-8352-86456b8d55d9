<template>
  <div class="edit-popup">
    <popup
      ref="popupRef"
      :title="popupTitle"
      :confirmButtonText="$t('vehicle.确定')"
      :cancelButtonText="$t('vehicle.取消')"
      :async="true"
      width="550px"
      @confirm="handleSubmit"
      @close="handleClose"
    >
      <el-form ref="formRef" :model="formData" label-width="110px" :rules="formRules">
        <el-form-item :label="$t('vehicle.车辆名称')" prop="vehicle_name">
          <el-input v-model="formData.vehicle_name" :placeholder="$t('vehicle.请输入')" clearable />
        </el-form-item>
        <el-form-item :label="$t('vehicle.车辆类型')" prop="vehicle_type">
          <el-select v-model="formData.vehicle_type" :placeholder="$t('vehicle.请选择')" clearable>
            <el-option
              v-for="(item, key, index) in pageData.vehicleTypeList"
              :key="key"
              :label="item"
              :value="Number(key)"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('vehicle.备注')" prop="description">
          <el-input v-model="formData.description" :placeholder="$t('vehicle.请输入')" clearable />
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>
<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import { vehicleAdd, vehicleUpdate, vehiclesDetail } from "@/api/device";
import feedback from "@/utils/feedback";
import useMetadataStore from "@/stores/modules/metadata";
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n();
const metadataStore = useMetadataStore();

const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? t("vehicle.编辑车辆") : t("vehicle.添加车辆");
});

const formData: any = reactive({
  id_: "",
  vehicle_name: "",
  vehicle_type: "",
  description: "",
  android: {},
  handleConfig: {},
});

const formRules = reactive({
  vehicle_name: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
  vehicle_type: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
});

const pageData: any = reactive({
  vehicleTypeList: {},
});

onMounted(() => {
  getVehicleTypeList();
});

const getVehicleTypeList = async () => {
  pageData.vehicleTypeList = await metadataStore.fetchMetadata("VEHICLE_TYPE", "kv");
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  if (mode.value == "edit") {
    await vehicleUpdate(formData);
  } else {
    await vehicleAdd(formData);
  }
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (row: any) => {
  const res = await vehiclesDetail(row.id);
  const data = { id_: res.id, ...res };
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
