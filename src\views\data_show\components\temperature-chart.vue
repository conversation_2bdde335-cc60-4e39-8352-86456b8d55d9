<template>
  <div ref="chartRef" class="w-full h-full mx-0 my-auto"></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import useWebsocketStore from "@/stores/modules/websocket";

const webStore = useWebsocketStore();
const chartRef = ref();
const chart = ref();

onMounted(() => {
  setTimeout(() => {
    chart.value = echarts.init(chartRef.value);
    chart.value.setOption(options);
  }, 100);

  setInterval(() => {
    const { water_temperature, hydraulic_oil_temperature, ambient_temperature } = webStore.vehicleInfo;

    const computedProportion = () => {
      chartData[0].value = water_temperature;
      chartData[1].value = hydraulic_oil_temperature;
      chartData[2].value = ambient_temperature.toFixed(1);
      let res = [];
      if (water_temperature > 0) {
        res.push((water_temperature / (chartData[0].max - chartData[0].min)) * 100);
      } else {
        res.push(0);
      }
      if (hydraulic_oil_temperature > 0) {
        res.push((hydraulic_oil_temperature / (chartData[1].max - chartData[1].min)) * 100);
      } else {
        res.push(0);
      }

      if (ambient_temperature >= 0) {
        res.push(
          ((ambient_temperature + Math.abs(chartData[2].min)) / (chartData[2].max + Math.abs(chartData[2].min))) * 100
        );
      } else if (ambient_temperature < 0) {
        res.push((Math.abs(ambient_temperature) / (chartData[2].max + Math.abs(chartData[2].min))) * 100);
      }
      return res;
    };
    if (chart.value) {
      chart.value.setOption({
        series: [{ data: computedProportion() }],
      });
    }
  }, 1000);
});

let chartData = [
  { key: "冷却液温度", color: "#10b981", value: 65, min: 30, max: 120 },
  { key: "液压油温", color: "#0ea5e9", value: 45, min: 20, max: 70 },
  { key: "环境温度", color: "#f59e0b", value: 0, min: -50, max: 50 },
];

const options = {
  grid: {
    left: "6%",
    top: "12%",
    right: "6%",
    bottom: "6%",
    containLabel: true,
  },
  xAxis: [
    {
      show: false,
    },
  ],
  yAxis: [
    {
      axisTick: "none",
      axisLine: "none",
      offset: "27",
      axisLabel: {
        textStyle: {
          color: "#ffffff",
          fontSize: "16",
        },
      },
      data: ["", "", ""],
    },
    {
      axisTick: "none",
      axisLine: "none",
      axisLabel: {
        textStyle: {
          color: "#ffffff",
          fontSize: "16",
        },
      },
      data: ["冷却液温度", "液压油温", "环境温度"],
    },
    {
      name: "单位：温度(°C)",
      nameGap: "50",
      nameTextStyle: {
        color: "#ffffff",
        fontSize: "16",
      },
      axisLine: {
        lineStyle: {
          color: "rgba(0,0,0,0)",
        },
      },
      data: [],
    },
  ],
  series: [
    {
      name: "条",
      type: "bar",
      yAxisIndex: 0,
      data: [50, 50, 50],
      label: {
        normal: {
          show: true,
          position: "right",
          formatter: function (param: any) {
            return chartData[param.dataIndex].value + "°C";
          },
          textStyle: {
            color: "#ffffff",
            fontSize: "16",
          },
        },
      },
      barWidth: 14,
      itemStyle: {
        normal: {
          barBorderRadius: 5,
          color: function (params: any) {
            return chartData[params.dataIndex % chartData.length].color;
          },
        },
      },
      z: 2,
    },
    {
      name: "框",
      type: "bar",
      yAxisIndex: 1,
      barGap: "-100%",
      data: [99.5, 99.5, 99.5],
      barWidth: 20,
      itemStyle: {
        normal: {
          color: "#060711",
          barBorderRadius: 5,
        },
      },
      z: 1,
    },
    {
      name: "外框",
      type: "bar",
      yAxisIndex: 2,
      barGap: "-100%",
      data: [100, 100, 100],
      barWidth: 24,
      itemStyle: {
        normal: {
          color: function (params: any) {
            return chartData[params.dataIndex % chartData.length].color;
          },
          barBorderRadius: 5,
        },
      },
      z: 0,
    },
    {
      name: "外圆",
      type: "scatter",
      hoverAnimation: false,
      data: [0, 0, 0],
      yAxisIndex: 2,
      symbolSize: 30,
      itemStyle: {
        normal: {
          color: function (params: any) {
            return chartData[params.dataIndex % chartData.length].color;
          },
          opacity: 1,
        },
      },
      z: 2,
    },
  ],
};

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
});
</script>
<style scoped lang="scss"></style>
