<template>
  <!-- <div class="material" v-loading="pager.loading"> -->
  <div class="material">
    <div class="material__left">
      <div class="flex-1 min-h-0">
        <div class="group-title flex items-center justify-center">{{ $t("common.分组信息") }}</div>
        <el-scrollbar>
          <div class="material-left__content pt-4 p-b-4">
            <el-tree
              ref="treeRef"
              node-key="id"
              :data="cateLists"
              empty-text=""
              :highlight-current="true"
              :expand-on-click-node="false"
              :current-node-key="cateId"
              @node-click="handleCatSelect"
            >
              <template v-slot="{ data }">
                <div class="flex flex-1 items-center min-w-0 pr-4">
                  <img class="w-[20px] h-[16px] mr-3" src="@/assets/images/icon_folder.png" />
                  <span class="flex-1 truncate mr-2">
                    <overflow-tooltip :content="data.name" />
                  </span>
                  <el-dropdown v-if="false" :hide-on-click="false">
                    <span class="muted m-r-10">···</span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <popover-input
                          @confirm="handleEditCate($event, data.id)"
                          size="default"
                          :value="data.name"
                          width="400px"
                          :limit="20"
                          show-limit
                          teleported
                        >
                          <div>
                            <el-dropdown-item> {{ $t("vehicle.命名分组") }} </el-dropdown-item>
                          </div>
                        </popover-input>
                        <div @click="handleDeleteCate(data.id)">
                          <el-dropdown-item>{{ $t("vehicle.删除分组") }}</el-dropdown-item>
                        </div>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-tree>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <div class="material__center flex flex-col">
      <div class="operate-btn flex justify-end">
        <el-date-picker
          class="grow-0"
          style="width: 360px"
          v-model="dateValue"
          type="datetimerange"
          :start-placeholder="$t('common.开始时间')"
          :end-placeholder="$t('common.结束时间')"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          @change="getFileListWithTime"
        />
        <div class="flex items-center ml-2">
          <el-tooltip :content="$t('common.列表视图')" placement="top">
            <div
              class="list-icon"
              :class="{
                select: listShowType == 'table',
              }"
              @click="listShowType = 'table'"
            >
              <icon name="local-icon-list-2" :size="18" />
            </div>
          </el-tooltip>
          <el-tooltip :content="$t('common.平铺视图')" placement="top">
            <div
              class="list-icon"
              :class="{
                select: listShowType == 'normal',
              }"
              @click="listShowType = 'normal'"
            >
              <icon name="el-icon-Menu" :size="18" />
            </div>
          </el-tooltip>
          <!-- <el-tooltip :content="$t('common.视频配置')" placement="top">
            <div class="list-icon config-icon" @click="">
              <icon name="local-icon-shezhi_mian" :size="18" />
            </div>
          </el-tooltip> -->
        </div>
      </div>

      <div class="material-center__content flex flex-col flex-1 mb-1 min-h-0">
        <el-scrollbar v-if="pager.lists.length" v-show="listShowType == 'normal'">
          <ul class="file-list flex flex-wrap mt-4">
            <li class="file-item-wrap" v-for="item in pager.lists" :key="item.id" :style="{ width: fileSize }">
              <!-- <del-wrap @close="batchFileDelete([item.id])"> -->
              <file-item
                :url="posterUrl(item.id)"
                :name="item.stream_name"
                :id="item.id"
                :file-size="fileSize"
                type="video"
                @click="selectFile(item)"
                @play="handlePreview"
              >
                <div class="item-selected" v-if="isSelect(item.id)">
                  <icon :size="24" name="el-icon-Check" color="#fff" />
                </div>
              </file-item>
              <!-- </del-wrap> -->

              <el-tooltip class="mt-1" :content="item.stream_name">
                <span class="w-[118px] two-line-ellipsis">{{ item.stream_name }}</span>
              </el-tooltip>
              <div class="operation-btns flex items-center">
                <el-button type="primary" link @click="handlePreview(item)"> {{ $t("common.查看") }} </el-button>
                <el-button type="primary" style="margin-left: 0" link @click="handleDownload(item)">
                  {{ $t("common.下载") }}
                </el-button>
              </div>
            </li>
          </ul>
        </el-scrollbar>

        <el-table
          ref="tableRef"
          class="mt-4"
          v-show="listShowType == 'table'"
          :data="pager.lists"
          width="100%"
          height="100%"
          size="large"
          @row-click="selectFile"
        >
          <el-table-column width="55">
            <template #default="{ row }">
              <el-checkbox :modelValue="isSelect(row.id)" @change.stop="selectFile(row)" />
            </template>
          </el-table-column>
          <el-table-column :label="$t('common.缩略图')" width="100">
            <template #default="{ row }">
              <file-item
                :url="posterUrl(row.id)"
                :name="row.stream_name"
                :id="row.id"
                file-size="50px"
                type="image"
                @play="handlePreview"
              ></file-item>
            </template>
          </el-table-column>
          <el-table-column :label="$t('vehicle.名称')" min-width="100" show-overflow-tooltip>
            <template #default="{ row }">
              <el-link @click.stop="handlePreview(row)" :underline="false">
                {{ row.stream_name }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="segment_start_time" :label="$t('common.创建时间')" min-width="100">
            <template #default="{ row }">
              {{ row.segment_start_time ? dayjs(row.segment_start_time).local().format("YYYY-MM-DD HH:mm:ss") : "--" }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('common.操作')" width="150" fixed="right">
            <template #default="{ row }">
              <div class="inline-block">
                <el-button type="primary" link @click.stop="handlePreview(row)">{{ $t("common.查看") }} </el-button>
              </div>
              <div class="inline-block">
                <el-button type="primary" link @click.stop="handleDownload(row)">{{ $t("common.下载") }} </el-button>
                <!-- <el-button type="primary" link @click.stop="batchFileDelete([row.id])">{{$t('common.删除')}}  </el-button> -->
              </div>
            </template>
          </el-table-column>
        </el-table>

        <div
          class="flex flex-1 justify-center items-center"
          v-if="!pager.loading && !pager.lists.length && listShowType == 'normal'"
        >
          暂无数据
        </div>
      </div>
      <div class="material-center__footer flex justify-between items-center mt-2">
        <div class="flex">
          <span class="mr-3">
            <el-checkbox
              :disabled="!pager.lists.length"
              v-model="isCheckAll"
              @change="selectAll"
              :indeterminate="isIndeterminate"
            >
              {{ $t("common.当页全选") }}
            </el-checkbox>
          </span>
          <!-- <el-button :disabled="!select.length" @click="batchFileDelete()"> 删除 </el-button> -->
          <el-button :disabled="!select.length" type="primary" size="default" @click="handleBatchDownload">
            {{ $t("common.批量下载") }}
          </el-button>
        </div>
        <pagination v-model="pager" @change="getFileList" layout="total, prev, pager, next, jumper, sizes" />
      </div>
    </div>
    <preview
      v-if="showPreview"
      v-model="showPreview"
      :url="previewUrl"
      :title="previewName"
      :type="type"
      :isShowHandle="true"
      :startTime="startTime"
      :endTime="endTime"
      :vehicleId="vehicleId"
    />
    <el-dialog v-model="pageData.popupShow" title="下载进度" width="500px">
      <el-table :data="pageData.downloadData" style="width: 100%" empty-text="当前没有下载任务">
        <el-table-column prop="stream_name" label="文件名称" width="180" />
        <el-table-column prop="progress" label="进度">
          <template #default="{ row }">
            <el-progress :percentage="row.progress" :stroke-width="15" striped striped-flow />
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog v-model="pageData.popupShowConfig" title="视频配置" width="500px"></el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { useCate, useFile } from "./hook";
import FileItem from "./file.vue";
import Preview from "./preview.vue";
import feedback from "@/utils/feedback";

import FileSaver from "file-saver";
import axios from "axios";
import cache from "@/utils/cache";
import JSZip from "jszip";
import dayjs from "dayjs";

import { useI18n } from "vue-i18n";
const { t } = useI18n();

const pageData: any = reactive({
  popupShow: false,
  popupShowConfig: false,
  videoConfig: {},
  downloadData: [],
  posterUrl: import.meta.env.VITE_FILE_SERVER,
});

const props = defineProps({
  fileSize: {
    type: String,
    default: "100px",
  },
  limit: {
    type: Number,
    default: 1,
  },
  type: {
    type: String,
    default: "image",
  },
  pageSize: {
    type: Number,
    default: 15,
  },
});
const emit = defineEmits(["change"]);
const { limit } = toRefs(props);
const previewUrl = ref("");
const previewName = ref("");
const startTime = ref("");
const endTime = ref("");
const vehicleId = ref("");
const showPreview = ref(false);
const dateValue = ref("");
const isScanning = ref(false);

const { treeRef, cateId, cateLists, handleAddCate, handleEditCate, handleDeleteCate, getCateLists, handleCatSelect } =
  useCate();

const {
  tableRef,
  listShowType,
  pager,
  fileParams,
  select,
  isCheckAll,
  isIndeterminate,
  getFileList,
  refresh,
  batchFileDelete,
  selectFile,
  isSelect,
  clearSelect,
  cancelSelete,
  selectAll,
} = useFile(cateId, dateValue, limit, props.pageSize);

const posterUrl = computed(() => (id: any) => {
  if (import.meta.env.PROD) pageData.posterUrl = window.document.location.origin;
  return `${pageData.posterUrl}/api/v2/data_manage/media/video/cover/${id}.jpg`;
});

const getData = async () => {
  await getCateLists();
  await getFileList();
};

const getFileListWithTime = () => {
  treeRef.value?.setCurrentKey("");
  getFileList();
};

const handlePreview = (row: any) => {
  const { id, stream_name, segment_start_time, segment_end_time, vehicle_id } = row;
  if (import.meta.env.PROD) pageData.posterUrl = window.document.location.origin;
  previewUrl.value = `${pageData.posterUrl}/api/v2/data_manage/media/video/play/${id}.mp4`;
  previewName.value = dayjs(segment_start_time).local().format("YYYY-MM-DD HH:mm:ss") + " " + stream_name;
  startTime.value = segment_start_time;
  if (!segment_end_time) {
    endTime.value = dayjs.utc(segment_start_time).add(5, "minute").format("YYYY-MM-DDTHH:mm:ss[+00:00]");
  } else {
    endTime.value = segment_end_time;
  }
  vehicleId.value = vehicle_id;
  showPreview.value = true;
};

const handleBatchDownload = async () => {
  const zip = new JSZip();
  const authorization = cache.get("token");
  pageData.popupShow = true;

  const downloadPromises = select.value.map((item: any, index: number) => {
    item.progress = 0;
    pageData.downloadData.push(item);
    const fileName = `${item.stream_name}_${item.segment_start_time}.mp4`;
    return axios
      .get(`/api/v2/data_manage/media/video/download/${item.id}.mp4`, {
        responseType: "blob",
        headers: { authorization },
        onDownloadProgress(e: any) {
          if (e.lengthComputable) {
            item.progress = Math.round((e.loaded * 100) / e.total);
          }
        },
      } as any)
      .then((res: any) => {
        zip.file(`${fileName}`, res.data);
      })
      .catch((error: any) => {
        console.error(`Failed to download ${fileName}`, error);
      })
      .finally(() => {
        const index = pageData.downloadData.findIndex((i: any) => i.id === item.id);
        pageData.downloadData.splice(index, 1);
      });
  });

  await Promise.all(downloadPromises);

  zip
    .generateAsync({ type: "blob" })
    .then((content) => {
      const fileName = `builderx_videos_${dayjs().format("MM-DD_HH:mm:ss")}.zip`;
      FileSaver.saveAs(content, fileName);
    })
    .finally(() => {
      pageData.popupShow = false;
    });
};

const handleDownload = async (item: any) => {
  pageData.popupShow = true;

  const flag = pageData.downloadData.some((i: any) => i.id === item.id);
  if (flag) return feedback.msgWarning("当前文件已存在下载列表");

  item.progress = 0;
  pageData.downloadData.push(item);

  const fileName = `${item.stream_name}.mp4`;
  const authorization = cache.get("token");

  axios
    .get(`/api/v2/data_manage/media/video/download/${item.id}.mp4`, {
      responseType: "blob",
      headers: { authorization },
      onDownloadProgress(e: any) {
        if (e.lengthComputable) {
          item.progress = Math.round((e.loaded * 100) / e.total);
        }
      },
    } as any)
    .then((res: any) => {
      FileSaver.saveAs(res.data, fileName);
    })
    .catch((error: any) => {})
    .finally(() => {
      const index = pageData.downloadData.findIndex((i: any) => i.id === item.id);
      pageData.downloadData.splice(index, 1);
    });
};



watch(cateId, () => {
  dateValue.value = "";
  getFileList();
});

watch(
  select,
  (val: any[]) => {
    emit("change", val);
    if (val.length == pager.lists.length && val.length !== 0) {
      isIndeterminate.value = false;
      isCheckAll.value = true;
      return;
    }
    if (val.length > 0) {
      isIndeterminate.value = true;
    } else {
      isCheckAll.value = false;
      isIndeterminate.value = false;
    }
  },
  {
    deep: true,
  }
);

onMounted(() => {
  listShowType.value = "table";
  getData();
});

defineExpose({
  clearSelect,
});
</script>

<style scoped lang="scss">
:deep(.el-dialog__body) {
  padding: 0px !important;
}
.group-title {
  height: 40px;
  font-size: 15px;
  border-bottom: 1px solid var(--el-color-primary-light-9);
  border-color: var(--el-border-color);
}
.config-icon {
}

.material {
  @apply h-full min-h-0 flex flex-1;

  &__left {
    @apply border-r border-br flex flex-col w-[200px];

    :deep(.el-tree-node__content) {
      height: 36px;
    }
  }

  &__center {
    flex: 1;
    min-width: 0;
    min-height: 0;
    padding: 16px 0 0 16px;

    .list-icon {
      border-radius: 3px;
      display: flex;
      padding: 5px;
      cursor: pointer;

      &.select {
        @apply text-primary bg-primary-light-8;
      }
    }

    .config-icon:hover {
      @apply text-primary bg-primary-light-8;
    }

    .file-list {
      .file-item-wrap {
        margin-right: 16px;
        line-height: 1.3;
        cursor: pointer;

        .item-selected {
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 4px;
          background-color: rgba(0, 0, 0, 0.5);
          box-sizing: border-box;
        }

        .operation-btns {
          height: 28px;
          visibility: hidden;
        }

        &:hover .operation-btns {
          visibility: visible;
        }
      }
    }
  }

  &__right {
    @apply border-l border-br flex flex-col;
    width: 130px;

    .select-lists {
      padding: 10px;

      .select-item {
        width: 100px;
        height: 100px;
      }
    }
  }
}
</style>
