<template>
  <div id="dynamic-text1" v-if="showType == 2">
    {{ showTime }} <br /><span class="text-9xl">{{ showDate }}</span>
  </div>
  <div id="myDiv">
    <p id="dynamic-text" v-if="showType == 1">{{ time }}</p>
    <p id="dynamic-text" v-if="showType == 0">{{ timestamp }}</p>
    <div class="h-16 text-base">
      <el-button type="primary" size="default" @click="handleZero">从零开始</el-button>
      <el-button type="primary" size="default" @click="handleReset">时间戳</el-button>
      <el-button type="primary" size="default" @click="handleDate">显示时间</el-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
import dayjs from "dayjs";

const time = ref(0);
const showType = ref(1);
const showDate = ref("");
const showTime = ref("");
const timestamp = ref(0);

let timer: any;
timer = setInterval(() => {
  time.value += 1;
}, 10);

const showData = () => {
  showTime.value = dayjs().format("HH:mm:ss:SSS");
  showDate.value = dayjs().format("YYYY-MM-DD");
  timestamp.value = Date.now();
  requestAnimationFrame(showData);
};

const handleZero = () => {
  showType.value = 1;
  time.value = 0;
  resetFont();
};

const handleReset = () => {
  showType.value = 0;
  resetFont();
};

const handleDate = () => {
  showType.value = 2;
  resetFont();
};

const resetFont = () => {
  setTimeout(() => {
    adjustFontSize(textElement, maxFontSize);
  }, 100);
};

// 设置最大字体大小
const maxFontSize = 380; // 根据需要设置最大字体大小
let textElement: any;

const adjustFontSize = (textElement: any, maxFontSize: any) => {
  const maxWidth = textElement.offsetWidth; // 获取元素的宽度
  let textElement1: any = document.getElementById("dynamic-text");
  const textLength = textElement1.textContent.length; // 获取文本长度
  const minFontSize = 12; // 最小字体大小
  const ratio = maxWidth / textLength; // 宽度与字符长度的比例

  // 计算字体大小
  const fontSize = Math.min(maxFontSize, Math.max(minFontSize, ratio));

  // 设置字体大小
  textElement.style.fontSize = `${fontSize}px`;
};

const handleResize = () => adjustFontSize(textElement, maxFontSize);
window.addEventListener("resize", handleResize);

onMounted(async () => {
  textElement = document.getElementById("myDiv");
  adjustFontSize(textElement, maxFontSize);
  window.addEventListener("resize", handleResize);
  showData();
});

onUnmounted(() => {
  clearInterval(timer);
  timer = 0;
  window.removeEventListener("resize", handleResize);
});
</script>
<style lang="scss" scoped>
#dynamic-text1 {
  font-size: 100px;
  background-color: #fff;
  text-align: center;
}
#myDiv {
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
  font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande", "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
  font-size: clamp(12px, 2vw, 24px);
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 显示省略号 */
}
</style>
