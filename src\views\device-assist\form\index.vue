<template>
  <div class="admin">
    <el-card class="!border-none" shadow="never">
      <el-form class="mb-[-16px]" :model="formData" inline>
        <el-form-item label="表单名称">
          <el-input v-model="formData.id" class="w-[180px]" clearable @keyup.enter="resetPage" />
        </el-form-item>
        <el-form-item>
          <!-- resetPage -->
          <el-button type="primary" @click="">查询</el-button>
          <el-button @click="resetParams">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card v-loading="pager.loading" class="mt-4 !border-none" shadow="never">
      <div class="mt-4">
        <el-table :data="pager.lists" size="large" :height="calcTableHeight()">
          <el-table-column label="表单名称" prop="form_name" min-width="80" />
          <el-table-column label="备注" prop="form_remark" min-width="60" />
          <!-- <el-table-column label="设备IP" prop="ip_address" min-width="100">
            <template #default="{ row }">
              <span class="text_hidden">{{ row.ip_address[0] }}</span>
            </template>
          </el-table-column> -->
          <el-table-column label="创建时间" prop="create_time" min-width="60" />
          <el-table-column align="center" label="操作" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" @click="handleDetail(row)"> 详情 </el-button>
              <el-button type="primary" @click="handleEdit(row)"> 编辑 </el-button>
              <el-button type="danger" @click="handleDelete(row.id)"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>

<script lang="ts" setup name="admin">
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import EditPopup from "./confirm.vue";
const router = useRouter();
const editRef = shallowRef<InstanceType<typeof EditPopup>>();
// 表单数据
const formData = reactive({
  id: "",
});
const showEdit = ref(false);

const tmpFun = async () => {
  return {
    count: 1,
    lists: [
      {
        id: 1,
        form_name: "安卓表单",
        form_remark: "这是一个测试表单",
        create_time: "2021-09-01 12:00:00",
      },
    ],
  };
};

const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: tmpFun,
  params: formData,
});

const calcTableHeight = () => {
  return window.innerHeight - 91 - 16 * 5 - 74 - 20 * 2 - 32 * 1;
};

const handleEdit = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open();
  editRef.value?.setFormData(data);
};

const handleDelete = async (id: number) => {
  await feedback.confirm("确定要删除？");
  // await Delete({ id });
  feedback.msgSuccess("删除成功");
  getLists();
};

const handleDetail = async (data: any) => {
  router.push({ path: "/dev/form-detail" });
};

onMounted(() => {
  getLists();
});
</script>
<style lang="scss" scoped>
.text_hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
</style>
