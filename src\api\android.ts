import request from "@/utils/request";

const API_VERSION = 'v2';

// 操作台列表
export function consolesList(params?: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/op_consoles/`, params });
}

// 操作台详情
export function consolesDetail(id: string) {
  return request.get({ url: `/${API_VERSION}/op_consoles/${id}` });
}

// 删除操作台
export function consoleDelete(id: string) {
  return request.delete({ url: `/${API_VERSION}/op_consoles/${id}` });
}

// 增加操作台
export function consoleAdd(params?: Record<string, any>) {
  return request.post({ url: `/${API_VERSION}/op_consoles/`, params });
}

// 编辑操作台
export function consoleUpdate(params: Record<string, any>) {
  const { id, ...data } = params;
  return request.put({ url: `/${API_VERSION}/op_consoles/${id}`, data });
}

// 未连接操作台设备列表
export function unRegisterDeviceList(params: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/devices/op_consoles/un_register_list`, params });
}

// 更新注册设备
export function unRegisterDeviceUpdate(params: Record<string, any>) {
  const { id, ...data } = params;
  return request.put({ url: `/${API_VERSION}/op_consoles/${id}/device_bind`, data });
}
