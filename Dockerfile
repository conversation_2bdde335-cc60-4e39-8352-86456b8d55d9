FROM hub.apps.builderx.com/python:3.12.9-bookworm AS builder

COPY requirements.txt /tmp/

RUN pip install -r /tmp/requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ && pip cache purge

FROM hub.apps.builderx.com/python:3.12.9-slim-bookworm

ARG USERNAME=builderx
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# 创建用户组和用户
RUN groupadd --gid $USER_GID $USERNAME \
    && useradd --uid $USER_UID --gid $USER_GID -m $USERNAME

COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

WORKDIR /app

COPY . .

USER builderx

EXPOSE 8000

# HEALTHCHECK --interval=30s --timeout=10s --retries=3 CMD wget -q -O - http://localhost:8000/internal/check_db || exit 1

CMD ["uvicorn", "main:app", "--workers", "4", "--host", "0.0.0.0", "--port", "8000", "--proxy-headers"]