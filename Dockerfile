FROM hub.apps.builderx.com/node:20-alpine AS build-stage

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

LABEL author="Chen"

RUN echo "-------------------- web环境配置 --------------------"

WORKDIR /app
COPY . /
RUN npm config set registry https://registry.npmmirror.com/
RUN npm install
RUN npm run build-lite

RUN echo "🎉 打 🎉 包 🎉 成 🎉 功 🎉"

# nginx镜像
FROM hub.apps.builderx.com/nginx:1.29.1-alpine3.22-slim AS production-stage

RUN rm /etc/nginx/conf.d/default.conf
COPY /nginx/default.conf /etc/nginx/conf.d/
COPY --from=build-stage /dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

RUN echo "🎉 部 🎉 署 🎉 成 🎉 功 🎉"
