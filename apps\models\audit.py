from datetime import datetime
from typing import Optional

import arrow
from pydantic import BaseModel, Field

from apps.models.common import ObjectIdStr, PagingModel, SortFlag


class Record(BaseModel):
    """操作信息"""

    path: str
    method: str
    user_id: ObjectIdStr
    body: str = ""
    params: str = ""
    code: int
    msg: str = ""
    created_time: datetime = Field(default_factory=lambda: arrow.utcnow().datetime)
    ip: str = ""
    client: str = ""


class RecordOut(Record):
    """操作信息输出"""

    id: ObjectIdStr = Field(..., alias="_id")
    username: str
    nickname: str


class RecordListOut(BaseModel):
    count: int
    lists: list[RecordOut]


class Query(PagingModel):
    """查询条件"""

    user_id: Optional[ObjectIdStr] = None
    path: Optional[str] = None
    method: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    time_order: SortFlag = SortFlag.DESC
