"""
操作台增删修改查测试
"""

import json

import faker
from bson import ObjectId
from starlette.testclient import TestClient


def build_opc_data(name: str = "") -> dict:
    name = name or f"{faker.Faker('zh_CN').name()}的操作台"
    return {"name": name, "type": [1], "organization": ["builderx"], "tags": ["builderx"]}


def assert_response_success(res):
    res_json = res.json()
    assert res_json["code"] == 1000
    return res_json


def find_opc_by_name(opc_list, opc_name):
    for item in opc_list:
        if item["name"] == opc_name:
            return True
    return False


def test_opc_curd(client: TestClient, admin_header, mdb):
    """增删改查操作台"""
    name = faker.Faker("zh_CN").name()
    opc_name = f"{name}的操作台"
    data = {"name": opc_name, "type": [1], "organization": ["builderx"], "tags": ["builderx"]}

    # add
    res = client.post("/op_consoles", json=data, headers=admin_header)
    res_json = res.json()
    assert res_json["code"] == 1000
    opc_id = str(res_json["data"]["id"])

    # query opc
    res = client.get("/op_consoles", headers=admin_header)
    assert res.status_code == 200
    res_json = res.json()
    assert res_json["code"] == 1000
    opc_list = res_json["data"]["lists"]
    is_find = False
    for item in opc_list:
        if item["name"] != opc_name:
            continue
        is_find = True
    assert is_find

    # update
    opc_name = f"更新后的{opc_name}"
    data = {"name": opc_name, "type": [1, 1.1]}
    res = client.put(f"/op_consoles/{opc_id}", content=json.dumps(data), headers=admin_header)
    res_data = res.json()
    assert res_data["code"] == 1000
    opc_data = mdb["op_consoles"].find_one({"name": opc_name})
    assert opc_data is not None

    # delete
    res = client.delete(f"/op_consoles/{opc_id}", headers=admin_header)
    res_data = res.json()
    assert res_data["code"] == 1000
    assert mdb["op_consoles"].find_one({"name": opc_name}) is None
    assert mdb["op_consoles"].find_one({"_id": ObjectId(opc_id)}) is None
