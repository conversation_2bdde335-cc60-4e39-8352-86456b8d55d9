import config from "@/config";
import request from "@/utils/request";

const API_VERSION = 'v2';

// 登录
export function login(params: Record<string, any>) {
  return request.post({ url: `/${API_VERSION}/user/login`, params: { ...params, terminal: config.terminal } });
}

// 退出登录
export function logout() {
  return request.get({ url: `/${API_VERSION}/user/logout` });
}

// 用户信息
export function getUserInfo() {
  return request.get({ url: `/${API_VERSION}/user/info` });
}

// 菜单路由
export function getMenu() {
  return request.get({ url: `/${API_VERSION}/user/menu/route`, headers: { "X-Variable-Naming-Style": "camelCase" } });
}

// 编辑人员信息
export function setUserInfo(params: Record<string, any>) {
  return request.put({ url: `/${API_VERSION}/user/me`, params }, { isSnakeCase: true });
}

// 编辑人员信息
export function setUserPassword(params: Record<string, any>) {
  return request.put({ url: `/${API_VERSION}/user/me/password`, params }, { isSnakeCase: true });
}

// 请求超级接口
export function superApi() {
  return request.post({ url: "/init_data" }, { urlPrefix: "/super_interface" });
}

// 令牌列表
export function getApiKeyList() {
  return request.get({ url: `/${API_VERSION}/user/api_keys` });
}

// 新增令牌
export function addApiKey(params: Record<string, any>) {
  return request.post({ url: `/${API_VERSION}/user/api_keys`, params });
}

// 删除令牌
export function delApiKey(params: Record<string, any>) {
  return request.delete({ url: `/${API_VERSION}/user/api_keys`, params });
}
