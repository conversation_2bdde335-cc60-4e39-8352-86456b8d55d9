import { defineStore } from "pinia";
import { metaDataGet } from "@/api/setting/dict";

export interface MetadataState {
  cache: Record<string, any>;
}

const useMetadataStore = defineStore({
  id: "metadata",
  state: (): MetadataState => ({
    cache: {},
  }),
  getters: {
    getMetadata: (state) => (key: string) => {
      return state.cache[key];
    },
  },
  actions: {
    // 获取元数据，如果缓存中存在则直接返回，否则请求接口
    async fetchMetadata(key: string, type: string = "orig") {
      if (type === "kv") {
        if (this.cache[`${key}_kv`]) {
          return this.cache[`${key}_kv`];
        }
      } else {
        if (this.cache[key]) {
          return this.cache[key];
        }
      }

      try {
        // 请求接口获取数据
        const res = await metaDataGet(key);

        if (res.type === "array" && res.value.length > 0 && typeof res.value[0] === "object") {
          let kv: any = {};
          res.value.forEach((item: any) => {
            if (item.flag && item.name) {
              kv[item.flag] = item.name;
            }
          });
          this.cache[`${key}_kv`] = kv;
        }

        this.cache[key] = res.value;

        if (type === "kv") {
          return this.cache[`${key}_kv`];
        }
        return res.value;
      } catch (error) {
        console.error(`获取元数据失败: ${key}`, error);
        return null;
      }
    },

    // 清除指定key的缓存
    clearCache(key?: string) {
      if (key) {
        delete this.cache[key];
      } else {
        this.cache = {};
      }
    },
  },
});

export default useMetadataStore;
