"""
数据管理相关接口测试
"""

import faker
from starlette.testclient import TestClient


def assert_response_success(res):
    """断言响应成功"""
    res_json = res.json()
    assert res_json["code"] == 1000
    return res_json


def test_data_manage_vehicle_list(client: TestClient, admin_header):
    """测试获取数据管理中的车辆列表"""
    res = client.get("/data_manage/vehicles", headers=admin_header)
    assert_response_success(res)
    # 只验证接口能正常返回，不验证具体内容


def test_data_manage_vehicle_data(client: TestClient, admin_header):
    """测试获取车辆数据"""
    # 首先获取车辆列表
    res = client.get("/vehicles", headers=admin_header, params={"page": 1, "page_size": 10})
    res_json = assert_response_success(res)
    vehicle_list = res_json["data"]["lists"]

    # 如果有车辆，测试获取其数据
    if vehicle_list:
        vehicle_id = vehicle_list[0]["id"]

        # 测试获取车辆信令数据
        res = client.get(f"/data_manage/vehicles/{vehicle_id}/signals", headers=admin_header)
        # 这个接口可能需要车辆有数据，所以可能会失败，我们只检查状态码
        assert res.status_code in [200, 404]

        # 测试获取车辆视频数据
        res = client.get(f"/data_manage/vehicles/{vehicle_id}/videos", headers=admin_header)
        # 这个接口可能需要车辆有数据，所以可能会失败，我们只检查状态码
        assert res.status_code in [200, 404]


def test_data_manage_media_video(client: TestClient, admin_header):
    """测试视频媒体接口"""
    # 测试视频查询
    params = {"page": 1, "page_size": 10, "start_time": "2023-01-01 00:00:00", "end_time": "2023-12-31 23:59:59"}
    res = client.get("/data_manage/media/video", params=params, headers=admin_header)
    # 这个接口可能没有视频数据，所以我们只检查状态码
    assert res.status_code == 200

    if res.status_code == 200:
        res_json = assert_response_success(res)
        if "lists" in res_json["data"] and res_json["data"]["lists"]:
            video_id = res_json["data"]["lists"][0]["id"]

            # 测试视频播放
            res = client.get(f"/data_manage/media/video/play/{video_id}.mp4", headers=admin_header)
            # 这个接口可能需要视频文件存在，所以可能会失败，我们只检查状态码
            assert res.status_code in [200, 404, 400]

            # 测试视频下载
            res = client.get(f"/data_manage/media/video/download/{video_id}.mp4", headers=admin_header)
            # 这个接口可能需要视频文件存在，所以可能会失败，我们只检查状态码
            assert res.status_code in [200, 404, 400]

            # 测试视频封面
            res = client.get(f"/data_manage/media/video/cover/{video_id}.jpg", headers=admin_header)
            # 这个接口可能需要封面文件存在，所以可能会失败，我们只检查状态码
            assert res.status_code in [200, 404, 400]


def test_data_manage_search(client: TestClient, admin_header):
    """测试数据搜索功能"""
    # 测试搜索车辆数据
    fake = faker.Faker("zh_CN")
    search_params = {
        "keyword": fake.word(),
        "start_time": "2023-01-01 00:00:00",
        "end_time": "2023-12-31 23:59:59",
        "page": 1,
        "page_size": 10,
    }

    # 测试搜索信令数据
    res = client.get("/data_manage/search/signals", params=search_params, headers=admin_header)
    # 这个接口可能没有匹配数据，所以我们只检查状态码
    assert res.status_code == 200

    # 测试搜索视频数据
    res = client.get("/data_manage/search/videos", params=search_params, headers=admin_header)
    # 这个接口可能没有匹配数据，所以我们只检查状态码
    assert res.status_code == 200
