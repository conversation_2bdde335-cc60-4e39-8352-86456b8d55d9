<template>
  <div class="edit-popup">
    <el-drawer v-model="drawerShow" :title="popupTitle" :size="650" @closed="handleClose">
      <div class="h-full flex flex-col">
        <el-form ref="formRef" :model="formData" label-width="124px" :rules="formRules">
          <el-form-item :label="$t('vehicle.节点类型')" prop="node_type">
            <!-- <el-select
              class="w-[556px]"
              v-model="formData.node_type"
              :disabled="popupDisable"
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
              placeholder="请输入"
              @change="handleSelectChange($event)"
            >
              <el-option
                v-for="item in ROS_NODE_DATA"
                :key="item.node_type"
                :label="item.node_type"
                :value="item.node_type"
              />
            </el-select> -->
            <el-input v-model="formData.node_type" :disabled="popupDisable" :placeholder="$t('vehicle.请输入')" clearable />
          </el-form-item>
          <el-form-item :label="$t('vehicle.节点名称')" prop="node_name">
            <el-input v-model="formData.node_name" :disabled="popupDisable" :placeholder="$t('vehicle.请输入')" clearable />
          </el-form-item>
          <el-form-item :label="$t('vehicle.包名称')" prop="package_name">
            <el-input v-model="formData.package_name" :disabled="popupDisable" :placeholder="$t('vehicle.请输入')" clearable />
          </el-form-item>
          <el-form-item :label="$t('vehicle.是否自启')" prop="auto_start">
            <el-select v-model="formData.auto_start" placeholder="请选择" clearable>
              <el-option :label="$t('vehicle.是')" :value="1" />
              <el-option :label="$t('vehicle.否')" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('vehicle.节点参数')" prop="params">
            <JsonEditorVue
              v-if="drawerShow"
              class="h-[400px] w-[556px]"
              v-model="params"
              mode="text"
              :mainMenuBar="false"
              :onChange="onJsonSave"
            />
          </el-form-item>
        </el-form>
        <div class="flex flex-grow justify-end items-end">
          <el-button type="primary" @click="handleSubmit">确定</el-button>
          <el-button @click="handleClose">取消</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import feedback from "@/utils/feedback";
import JsonEditorVue from "json-editor-vue";
import { ROS_NODE_DATA } from "@/utils/constants";
import { editRos, addNode } from "@/api/device";
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n();

const props = defineProps({
  vehicleId: {
    type: String,
    require: true,
  },
  deviceId: {
    type: String,
    require: true,
  },
});

const params: any = ref({});
const onJsonSave = (value: any) => {
  formData.params = JSON.parse(value.text);
};

const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const mode = ref("add");
const drawerShow = ref(false);

const popupTitle = computed(() => {
  return mode.value == "edit" ? t('vehicle.编辑节点') : t('vehicle.添加节点');
});
const popupDisable = computed(() => {
  return mode.value == "edit";
});

const formData: any = reactive({
  program_type: "",
  node_name: "",
  package_name: "",
  node_type: "",
  auto_start: 1,
  params: {},
});

const formRules = reactive({
  program_type: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
  node_name: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
  package_name: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
  node_type: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
  params: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
});

const handleSelectChange = (value: any) => {
  const node = ROS_NODE_DATA.find((item) => item.node_type == value);
  if (node) {
    formData.node_name = node.node_name;
    formData.package_name = node.package_name;
    formData.program_type = node.program_type;
    params.value = node.params;
  }
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  if (popupDisable.value) {
    const params = {
      vehicle_id: props.vehicleId,
      node_name: formData.node_name,
      auto_start: formData.auto_start,
      params: formData.params,
    };
    await editRos(params);
    drawerShow.value = false;
    feedback.msgSuccess("操作成功");
    emit("success");
  } else {
    feedback.loading("");
    addNode({
      vehicle_id: props.vehicleId,
      data: formData,
    })
      .then((data) => {
        feedback.msgSuccess("操作成功");
        emit("success");
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        feedback.closeLoading();
        handleClose();
      });
  }
};

const open = (type = "add") => {
  mode.value = type;
  drawerShow.value = true;
};

const setFormData = async (row: any) => {
  const data = row;
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
  params.value = formData.params;
};

const handleClose = () => {
  formData.node_name = "";
  formData.package_name = "";
  formData.node_type = "";
  formData.params = {};
  params.value = {};
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
