""" 变量命名风格转换  """

import re


class NameStyleConverter:
    """
    变量命名风格转换，要求处理的是字符串不是字典，请使用使用 json.dumps(data_dict)
    """

    _snake_key_name = r"(\,\s?[\"])(\S)(\S+?_\S+?)([\"]\:)"
    _camel_key_name = r"(\,\s?[\"])(\S)(\S+?[A-Z]\S+?)([\"]\:)"

    @classmethod
    def snake_to_camel(cls, data):
        """下划线转换驼峰"""

        def _conv_fun(x):
            b = re.sub(r"_([a-z])", lambda m: m.group(1).upper(), x.group(0))
            return b

        a = re.sub(cls._snake_key_name, _conv_fun, data)
        return a

    @classmethod
    def snake_to_camel2(cls, data):
        """下划线转换大驼峰"""

        def _conv_fun(x):
            new_key_name = re.sub(r"_([a-z])", lambda m: m.group(1).upper(), x.group(3))
            return f"{x.group(1)}{x.group(2).upper()}{new_key_name}{x.group(4)}"

        return re.sub(cls._snake_key_name, _conv_fun, data)

    @classmethod
    def camel_to_snake(cls, data):
        """驼峰转换下划线"""

        def _conv_fun(x):
            new_key_name = re.sub(
                r"([A-Z][a-z])", lambda m: f"_{m.group(1)}", x.group(3)
            )
            return f"{x.group(1)}{x.group(2)}{new_key_name}{x.group(4)}".lower()

        return re.sub(cls._camel_key_name, _conv_fun, data)
