import os
from functools import lru_cache
from typing import Optional

from dotenv import load_dotenv
from pydantic_settings import BaseSettings


__all__ = ["get_settings"]

ENV_FILES = (".env", ".env.prod")
ROOT_PATH = os.path.dirname(os.path.abspath(os.path.join(__file__, "..")))


class DBSettings(BaseSettings):
    """数据库配置"""

    # """Redis配置"""
    redis_host: str = "localhost"
    redis_password: str = ""
    redis_port: int = 6379
    redis_db: int = 0
    rdb_prefix: str = "oms:"  # Redis键前缀

    # mongo 配置
    mongo_db_name: str = "builderx-oms"
    mongo_username: str = "root"
    mongo_password: str = "builderx"
    mongo_host: str = "localhost"
    mongo_port: int = 27017

    # influx 配置
    influxdb_username: str = "root"
    influxdb_password: str = "builderx"
    influxdb_org: str = "builderx"
    influxdb_bucket: str = "oms"
    influxdb_admin_token: str = ""
    influxdb_host: str = "localhost"
    influxdb_port: int = 8086


class AppSettings(DBSettings):
    """应用配置
    server目录为后端项目根目录, 在该目录下创建 ".env" 文件, 写入环境变量(默认大写)会自动加载, 并覆盖同名配置(小写)
        eg.
        .env 文件内写入:
            UPLOAD_DIRECTORY='/tmp/test/'
            REDIS_URL='redis://localhost:6379'
            DATABASE_URL='mysql+pymysql://root:root@localhost:3306/likeadmin?charset=utf8mb4'
            上述环境变量会覆盖 upload_directory 和 redis_url
    """

    # 模式
    mode: str = "prod"  # dev, test, prod

    # 全局配置
    root_path: str = ROOT_PATH  # 项目根路径
    request_timeout: int = 15  # 默认请求超时
    timezone: str = "Asia/Shanghai"  # 时区
    datetime_fmt: str = "%Y-%m-%d %H:%M:%S"  # 日期时间格式
    secret: str = "XrEdLiUb"  # 系统加密字符
    domain: str = "http://127.0.0.1:8000"  # 当前域名

    video_directory: str = "/data/video"  # 视频存放目录


@lru_cache()
def get_settings(run_mode: Optional[str] = None) -> AppSettings:
    """获取并缓存应用配置"""
    if run_mode is None:
        print("run_mode is not setting, get from env")
        run_mode = os.getenv("OMS_MODE")
    if run_mode == "prod":
        print("load_dotenv .env.prod", os.path.join(ROOT_PATH, ".env.prod"))
        print("run_mode is", run_mode)
        load_dotenv(os.path.join(ROOT_PATH, ".env.prod"), override=True)
        return AppSettings(mode=run_mode)
    if run_mode == "test":
        print("load_dotenv .env.test", os.path.join(ROOT_PATH, ".env.test"))
        print("run_mode is", run_mode)
        load_dotenv(os.path.join(ROOT_PATH, ".env.test"), override=True)
        return AppSettings(mode=run_mode)
    # 默认模式
    for f in ENV_FILES:
        if os.path.exists(os.path.join(ROOT_PATH, f)):
            print("load_dotenv", os.path.join(ROOT_PATH, f))
            load_dotenv(os.path.join(ROOT_PATH, f), override=True)
            print("run_mode is", run_mode)
            return AppSettings()
    return AppSettings()
