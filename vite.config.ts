import { fileURLToPath, URL } from "url";
import fs from "fs";
import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import { createStyleImportPlugin, ElementPlusResolve } from "vite-plugin-style-import";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import { visualizer } from "rollup-plugin-visualizer";
import vueSetupExtend from "vite-plugin-vue-setup-extend";
// https://vitejs.dev/config/

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  return {
    base: "/",
    server: {
      host: "0.0.0.0",
      port: 3001,
      proxy: {
        "^/api/": {
          // target: "http://*************:8000", // 日哥
          // target: "http://***************:8080", // 线上oms
          // target: "http://************:58080", // 公司正在部署的现场
          target: "http://127.0.0.1:8000", // 本地
          changeOrigin: true, // 开发模式，默认的origin是真实的 origin:localhost:3000 代理服务会把origin修改为目标地址
          // secure: true, // 是否https接口
          // ws: true, // 是否代理websockets
          // rewrite: (path) => path.replace(/^\/api/, '') // 路径重写，本项目不需要重写
        },
      },
    },
    plugins: [
      vue(),
      vueJsx(),
      AutoImport({
        imports: ["vue", "vue-router"],
        resolvers: [ElementPlusResolver()],
        eslintrc: {
          enabled: true,
        },
      }),
      Components({
        directoryAsNamespace: true,
        resolvers: [ElementPlusResolver()],
      }),
      createStyleImportPlugin({
        resolves: [ElementPlusResolve()],
      }),
      createSvgIconsPlugin({
        // 配置路径在你的src里的svg存放文件
        iconDirs: [fileURLToPath(new URL("./src/assets/icons", import.meta.url))],
        symbolId: "local-icon-[dir]-[name]",
      }),
      vueSetupExtend(),
      visualizer({ open: true }),
      scssVariablesPlugin(mode),
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    build: {
      rollupOptions: {
        external(id) {
          // 精简版不需要构建的文件
          const liteArr = [
            // "node_modules/highlight",
            // "node_modules/vue3-video-play",
            // "src/views/workbench/area.json",
            "node_modules/three",
            "node_modules/vuedraggable",
            "node_modules/@wangeditor",
            "src/views/consumer",
            "src/views/location",
            "src/views/material",
            "src/views/package",
            "src/views/setting",
            "src/views/webgl",
            "src/assets/images/project",
            "src/views/data_show/image/wajivideo.mp4",
          ];
          if (mode === "lite" && liteArr.some((item) => id.includes(item))) {
            return true;
          }
          return false;
        },
        manualChunks(id:any) {
          if (id.includes("node_modules")) {
            return id.toString().split("node_modules/")[1].split("/")[0].toString();
          }
        },
      },
    },
    define: {
      __APP_BUILD_TIME__: JSON.stringify(new Date().toLocaleString()),
    },
  };
});

// 自定义插件，用于在构建开始时写入 SCSS 变量文件
const scssVariablesPlugin = (mode: string) => {
  return {
    name: 'vite-plugin-scss-variables',
    buildStart() {
      const variablesContent = `$buildMode: "${mode}";`;
      const filePath = fileURLToPath(new URL('./src/styles/variables.scss', import.meta.url));
      fs.writeFileSync(filePath, variablesContent);
    },
  };
};
