<template>
  <div class="admin">
    <el-card class="mt-4 !border-none" shadow="never">
      <BuxForm
        v-if="temp"
        ref="buxFormRef"
        :formConfig="formData.formConfig"
        :widgetList="formData.widgetList"
      ></BuxForm>

      <div class="mb-3 flex justify-between">
        <el-button type="primary" @click="() => feedback.msgSuccess('先不设置')"> 表单设置 </el-button>
        <div>
          <el-button type="primary" @click="handleSave"> 保存 </el-button>
          <el-button type="primary" @click=""> 预览 </el-button>
        </div>
      </div>
      <el-table :data="formData.widgetList" size="large" :height="calcTableHeight()" row-key="field">
        <el-table-column label="字段名称" prop="title" />
        <el-table-column label="字段键名" prop="field" />
        <el-table-column label="组件类型" prop="type">
          <template #default="{ row }">
            <span>{{ FORM_TYPE_LIST.find((item) => item.value === row.type)?.label }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否显示" prop="display">
          <template #default="{ row }">
            <el-switch v-model="row.display" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template #default="{ row, $index }">
            <el-button link type="primary" @click="handleShowAdd($index)"> 添加 </el-button>
            <el-button link type="primary" @click="handleShowEdit(row, $index)"> 编辑 </el-button>
            <el-button link type="danger" @click="handleDelete($index)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @edit="handleEdit" @add="handleAdd" @close="showEdit = false" />
  </div>
</template>

<script lang="ts" setup name="admin">
import feedback from "@/utils/feedback";
import EditPopup from "./edit.vue";
import BuxForm from "@/components/bux-form/index.vue";
import { FORM_TYPE_LIST } from "@/utils/constants";
import Sortable from "sortablejs";

const editRef = shallowRef<InstanceType<typeof EditPopup>>();
const showEdit = ref(false);

const formData: any = reactive({});
const buxFormRef = shallowRef<InstanceType<typeof BuxForm>>();

const curAddIndex = ref(0);

const temp = ref(true);

const calcTableHeight = () => {
  return window.innerHeight - 91 - 16 * 5 - 74 - 20 * 2 - 32 * 1 + 32 * 3;
};

const handleDelete = async (index: any) => {
  await feedback.confirm("确定要删除？");
  formData.widgetList.splice(index, 1);
  feedback.msgSuccess("删除成功");
};

const handleShowAdd = async (index: any) => {
  curAddIndex.value = index;
  showEdit.value = true;
  await nextTick();
  editRef.value?.open();
};

const handleAdd = async (row: any) => {
  formData.widgetList.splice(curAddIndex.value + 1, 0, row);
  feedback.msgSuccess("添加成功");
  showEdit.value = false;

  tempChangeKey();
};

const handleShowEdit = async (data: any, index: any) => {
  curAddIndex.value = index;
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(data);
};

const handleEdit = async (row: any) => {
  formData.widgetList[curAddIndex.value] = row;
  feedback.msgSuccess("编辑成功");
  showEdit.value = false;
  tempChangeKey();
};

const tempChangeKey = () => {
  temp.value = false;
  setTimeout(() => {
    temp.value = true;
  }, 200);
};

const handleDetail = async (id: number) => {
  let mockData = {
    widgetList: [
      {
        display: true,
        props: {},
        type: "title",
        title: "基本信息",
      },
      {
        display: true,
        props: {
          required: true,
        },
        type: "input",
        field: "projectName",
        title: "项目名称",
        info: "请输入项目名称",
      },
      {
        display: true,
        props: {},
        type: "input",
        field: "modelName",
        title: "设备名称",
      },
      {
        display: true,
        props: {
          required: true,
        },
        type: "input",
        title: "播放流地址",
        field: "easyPlayUrl1",
      },
      {
        display: true,
        props: {
          required: true,
        },
        type: "input",
        title: "音频流地址",
        field: "easyPlayUrl6",
      },
      {
        display: true,
        props: {},
        type: "input",
        title: "平台Token",
        field: "oms_token",
      },
      {
        display: true,
        props: {
          required: false,
        },
        type: "input",
        title: "平台设备ID",
        field: "oms_id",
      },
      {
        display: true,
        props: {},
        type: "newline",
      },
      {
        display: true,
        props: {
          defaultValue: '{"value":"0"}',
          required: true,
        },
        type: "radio",
        options: [
          {
            label: "RTSP",
            value: "0",
            disabled: false,
          },
          {
            label: "声网",
            value: "1",
            disabled: true,
          },
        ],
        title: "视频类型",
        field: "videoType",
      },
      {
        display: true,
        props: {},
        type: "newline",
      },
      {
        display: true,
        props: {
          required: true,
          defaultValue: '{"value":"1"}',
        },
        type: "radio",
        options: [
          {
            label: "挖掘机",
            value: "1",
            disabled: false,
          },
          {
            label: "电铲",
            value: "2",
            disabled: false,
          },
          {
            label: "装载机",
            value: "4",
            disabled: false,
          },
          {
            label: "钻机",
            value: "5",
            disabled: false,
          },
          {
            label: "徐工700吨",
            value: "6",
            disabled: true,
          },
        ],
        title: "铲端类型",
        field: "excavatorType",
      },
      {
        display: true,
        props: {},
        type: "title",
        title: "MQTT 设置",
      },
      {
        display: true,
        props: {
          required: true,
        },
        field: "mqttName",
        type: "input",
        title: "用户名",
      },
      {
        display: true,
        props: {
          required: true,
          defaultValue: "",
        },
        title: "密码",
        type: "input",
        field: "mqttPassword",
        info: "请输入密码",
      },
      {
        display: true,
        props: {
          required: true,
        },
        type: "input",
        title: "连接地址",
        field: "mqttToAndroid",
      },
      {
        display: true,
        props: {
          required: true,
        },
        type: "input",
        field: "mqttClientID",
        title: "mqttClientID",
      },
      {
        display: true,
        props: {},
        type: "title",
        title: "上电设置",
      },
      {
        display: true,
        props: {
          defaultValue: '{"value":true}',
        },
        type: "switch",
        title: "远程启动",
        field: "needPowerOn",
      },
      {
        display: true,
        props: {
          defaultValue: false,
        },
        title: "泥人上电",
        type: "switch",
        field: "niRenDTO",
      },
      {
        display: true,
        props: {},
        type: "newline",
      },
      {
        display: true,
        props: {},
        type: "switch",
        title: "允许上电",
        field: "allowPowerOn",
      },
      {
        display: true,
        props: {},
        field: "allowPowerOff",
        title: "允许下电",
        type: "switch",
      },
      {
        display: true,
        props: {},
        title: "强制下电",
        type: "switch",
        field: "showPowerOffSwitch",
      },
      {
        display: true,
        props: {},
        title: "需要上电状态查询",
        type: "switch",
        field: "powerQuery",
      },
    ],
    formConfig: {
      rulesName: "rules",
      labelPosition: "right",
      labelWidth: "auto",
      inline: true,
      onFormDataChange: "",
    },
  };
  Object.assign(formData, mockData);
};

const handleSave = async () => {
  console.log(buxFormRef.value?.formModel);
  console.log(formData.widgetList);
};

const initDropTable = () => {
  let tbody = document.querySelector(".el-table__body-wrapper tbody");
  Sortable.create(tbody, {
    animation: 150, // ms, number 单位：ms，定义排序动画的时间
    onEnd(e: any) {
      if (e.oldIndex !== e.newIndex) {
        let currRow = formData.widgetList.splice(e.oldIndex, 1)[0];
        formData.widgetList.splice(e.newIndex, 0, currRow);
      }
    },
  });
};

onMounted(() => {
  handleDetail(1);
  initDropTable();
});
</script>
<style lang="scss" scoped>
.text_hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
</style>
