from fastapi import APIRouter

from .parameter import router as __parameter_router
from .service import router as __service_router
from .base import router as __vehicles_router
from .t_party import router as __t_party_router


router = APIRouter(prefix="")

router.include_router(__parameter_router)
router.include_router(__service_router)
router.include_router(__vehicles_router)
router.include_router(__t_party_router)
