import json
import secrets
from typing import List, Optional, Dict, Any

import arrow
from bson import ObjectId
from fastapi import Request
from motor.core import AgnosticCollection
from pymongo.errors import DuplicateKeyError
from starlette.exceptions import HTTPException

from apps.db import MongoDB
import apps.models.cache as Cache
from apps.common import HttpResp, AppException
from apps.utils import singleton, sha256_str, make_token, list_to_tree
import apps.models.user as UserModel
import apps.services.role as RoleService


MENU_COLL: AgnosticCollection = MongoDB.get_collection("menus")
USER_COLL: AgnosticCollection = MongoDB.get_collection("users")
ROLE_COLL: AgnosticCollection = MongoDB.get_collection("roles")
API_KEY_COLL: AgnosticCollection = MongoDB.get_collection("api_keys")


@singleton
class MenusService:
    """菜单管理服务"""

    cache_id = Cache.MenuMaxId()

    def __init__(self, _=None):
        pass

    async def get_next_id(self):
        """获取下一个菜单ID"""
        if not await self.cache_id.exist():
            cursor = MENU_COLL.find().sort("_id", -1).limit(1)
            data = await cursor.to_list(None)
            await self.cache_id.setnx(int(data[0]["_id"]))
        return await self.cache_id.incr()

    async def menu_create(self, menu: UserModel.MenuCreate) -> dict:
        menu_json = menu.model_dump()
        next_id = await self.get_next_id()
        menu_json.update(
            {
                "_id": next_id,
                "create_time": arrow.utcnow().datetime,
                "update_time": arrow.utcnow().datetime,
            }
        )
        try:
            res = await MENU_COLL.insert_one(menu_json)
        except DuplicateKeyError as exc:
            raise AppException(HttpResp.MENU_NAME_EXISTS) from exc
        assert res.inserted_id, "Menu info insert failed."
        return {"name": menu.menu_name}

    async def menu_list(self):
        cursor = MENU_COLL.find({"_id": {"$ne": "menus"}}).sort("menu_sort", -1)
        menu_list = await cursor.to_list(None)
        res_data = [UserModel.MenuOut(**item).model_dump() for item in menu_list]
        return list_to_tree(res_data, "id", "pid", "children")

    async def menu_detail(self, id_):
        menu = await MENU_COLL.find_one({"_id": id_})
        if not menu:
            raise AppException(HttpResp.MENU_NOT_EXIST)
        return UserModel.MenuOut(**menu).model_dump()

    async def menu_update(self, menu: UserModel.MenuUpdate):
        data = json.loads(menu.model_dump_json())
        data["update_time"] = arrow.utcnow().datetime
        del data["id"]
        await MENU_COLL.update_one({"_id": menu.id}, {"$set": data})
        return {"name": menu.menu_name, "msg": "修改成功"}

    async def menu_delete(self, id_):
        res = await MENU_COLL.delete_one({"_id": id_})
        if res.deleted_count == 0:
            raise AppException(HttpResp.MENU_NOT_EXIST)
        return {"msg": "删除成功"}

    async def menu_route(self, req: Request):
        user_info: UserModel.CacheInfo = req.state.user
        query = {"menu_type": {"$in": ["M", "C"]}, "is_disable": {"$ne": 1}}
        if user_info.is_super_admin is False:
            menu_ids = await RoleService.get_roles_menus(user_info.role_ids)
            query["_id"] = {"$in": menu_ids}
        menu_list = await MENU_COLL.find(query).to_list(None)

        menu_list = [UserModel.MenuOut(**item).model_dump() for item in menu_list]

        menus_tree = list_to_tree(menu_list, "id", "pid", "children")
        return sorted(menus_tree, key=lambda x: x["menu_sort"], reverse=True)

    async def export_data(self):
        """导出菜单数据"""
        cursor = MENU_COLL.find()
        res_data = await cursor.to_list(None)
        return res_data

    async def import_data(self, data: List[dict]):
        """导入菜单数据"""
        if len(data) == 0:
            return {"msg": "导入数据为空"}
        await MENU_COLL.delete_many({})
        for i in data:
            await MENU_COLL.update_one({"_id": i["_id"]}, {"$set": i}, upsert=True)
        return {"msg": "导入成功"}


@singleton
class UserService:
    """用户管理服务"""

    def __init__(self, _=None):
        pass

    async def find_user_by_name(self, name: str) -> dict:
        user = await USER_COLL.find_one({"username": name})
        if not user:
            raise HTTPException(status_code=404, detail="User does not exist.")
        return user

    async def list(self, q: UserModel.Query):
        filter_d = {}
        if q.username:
            filter_d["username"] = q.username
        if q.nickname:
            filter_d["nickname"] = q.nickname
        if q.role:
            filter_d["role_ids"] = {"$in": [q.role]}
        filter_d.update({"status": {"$ne": UserModel.Status.DELETED.value}})

        user_count = await USER_COLL.count_documents(filter_d)
        res_data = UserModel.UserListOut(count=user_count, lists=[])
        if user_count == 0:
            return res_data

        skip = (q.page_no - 1) * q.page_size
        user_docs = USER_COLL.aggregate(
            [
                {"$match": filter_d},
                {"$skip": skip},
                {"$limit": q.page_size},
                {"$lookup": {"from": "roles", "localField": "role_ids", "foreignField": "_id", "as": "R"}},
                {
                    "$addFields": {
                        "roles": {
                            "$map": {
                                "input": "$R",
                                "as": "role",
                                "in": {"id": {"$toString": "$$role._id"}, "name": "$$role.name"},
                            }
                        }
                    }
                },
                {"$project": {"R": 0, "role_ids": 0}},
            ]
        )
        async for user in user_docs:
            res_data.lists.append(UserModel.UserOut(**user))
        return res_data

    async def create(self, user: UserModel.Create):
        user_json = user.model_dump()
        user_json.update(
            {
                "password": sha256_str(user.password),
                "create_time": arrow.utcnow().datetime,
                "update_time": arrow.utcnow().datetime,
                "last_login_time": arrow.utcnow().datetime,
                "status": UserModel.Status.NORMAL.value,
            }
        )
        try:
            res = await USER_COLL.insert_one(user_json)
            assert res.inserted_id, "User info insert failed."

            # 创建用户的同时创建一个同名角色并关联用户
            try:
                role_info = await RoleService.role_create(
                    UserModel.RoleCreate(name=user.username, remark=f"{user.nickname}用户角色"),
                    obj_id=res.inserted_id,
                )
                await USER_COLL.update_one(
                    {"_id": res.inserted_id},
                    {"$set": {"role_id": role_info[id]}},
                )
            except Exception:
                pass

            return {"username": user.username, "id": res.inserted_id}
        except DuplicateKeyError as exc:
            raise AppException(HttpResp.USERNAME_OR_EMAIL_EXISTS) from exc

    async def delete(self, uid: ObjectId):
        if uid == UserModel.SUPER_ADMIN_ID:
            raise AppException(HttpResp.NO_PERMISSION)
        res = await USER_COLL.find_one_and_delete({"_id": uid})
        assert res, "User info delete failed."
        return {}

    async def update(self, user: UserModel.Update):
        user_data: Dict = user.model_dump(exclude=set(["id"]), exclude_none=True, exclude_unset=True)
        user_data.update({"update_time": arrow.utcnow().datetime})
        res = await USER_COLL.update_one(
            {"_id": user.id},
            {"$set": user_data},
        )
        if res.modified_count != 1:
            raise AppException(HttpResp.USER_NOT_EXIST)
        await Cache.UserInfo(user.id).delete()
        return {}

    async def update_status(self, uid: ObjectId, status: UserModel.Status):
        """修改用户状态"""
        res = await USER_COLL.update_one({"_id": uid}, {"$set": {"status": status}})
        if res.modified_count == 0:
            raise HTTPException(status_code=404, detail="User does not exist.")
        await Cache.UserInfo(uid).delete()
        return {}

    async def update_password(self, uid: ObjectId, new_password: str, old_password: str):
        """修改密码, 有旧密码则校验旧密码, 否则不需要
        @param uid: 用户ID
        @param new_password: 新密码
        @param old_password: 旧密码
        @return:
        """
        filter_d: Dict[Any, Any] = {
            "_id": uid,
            "password": sha256_str(old_password),
        }
        res = await USER_COLL.update_one(filter_d, {"$set": {"password": sha256_str(new_password)}})
        if res.modified_count == 0:
            raise AppException(HttpResp.PASSWORD_INCORRECT)

        await Cache.UserInfo(uid).delete()
        return {}

    async def send_reset_password(self, uid: ObjectId):
        """重置密码"""
        token_value = secrets.token_urlsafe(24)
        await Cache.ResetPassToken(token_value).set(str(uid), ex=360)
        # TODO 后续可以考虑使用邮件服务发送重置密码链接
        return {"token": token_value}

    async def reset_password(self, token_value: str, password: str):
        """重置密码"""
        cache_token = Cache.ResetPassToken(token_value)
        user_id = await cache_token.get()
        if not user_id:
            raise AppException(HttpResp.TOKEN_INVALID)
        await cache_token.delete()

        user_id = ObjectId(user_id)
        res = await USER_COLL.update_one(
            {"_id": user_id},
            {"$set": {"password": sha256_str(password)}},
        )
        if res.modified_count == 0:
            raise AppException(HttpResp.USER_NOT_EXIST)
        return {}

    async def info(self, uid: ObjectId):
        """查询用户详情"""
        user = await USER_COLL.find_one({"_id": uid})
        if not user:
            raise AppException(HttpResp.USER_NOT_EXIST)
        user_info = UserModel.CacheInfo(**user)
        res_data = user_info.model_dump(exclude={"is_super_admin"})
        return res_data

    async def detail(self, req: Request):
        """获取用户详情, 包含权限信息"""
        user: UserModel.CacheInfo = req.state.user
        assert user, "User info is required."
        data = user.model_dump(by_alias=True)
        result = UserModel.UserProfile(
            user=UserModel.BaseInfo(**data),
            permissions=[],
        )

        if user.is_super_admin:
            result.permissions = ["*"]
            return result

        role_results = await ROLE_COLL.aggregate(
            [
                {"$match": {"_id": {"$in": user.role_ids}}},
                {"$unwind": "$menus"},
                {"$group": {"_id": None, "all_menus": {"$addToSet": "$menus"}}},
            ]
        ).to_list(length=None)

        if len(role_results) == 0:
            menu_id_list = []
        else:
            menu_id_list = role_results[0].get("all_menus", [])

        menu_results = MENU_COLL.find(
            {
                "_id": {"$in": menu_id_list},
                "menu_type": {"$in": ["A", "C"]},
            }
        )
        async for menu in menu_results:
            if menu["perms"] == "":
                continue
            result.permissions.append(menu["perms"])
        return result

    async def login(self, data: UserModel.Login, req: Optional[Request] = None):
        q = {"username": data.username, "password": sha256_str(data.password)}
        user_data_doc = await USER_COLL.find_one(q)
        if user_data_doc is None:
            raise AppException(HttpResp.USER_AUTH_FAIL)
        if user_data_doc.get("status") != UserModel.Status.NORMAL.value:
            raise AppException(HttpResp.LOGIN_DISABLED)

        await USER_COLL.update_one(
            {"_id": user_data_doc["_id"]},
            {"$set": {"last_login_time": arrow.utcnow().datetime}},
        )

        user_data_doc["role_ids"] = [str(role_id) for role_id in user_data_doc.get("role_ids", [])]
        user = UserModel.BaseInfo(**user_data_doc)
        if req:
            # 注入用户信息，用于后续记录审计日志调用记录登录日志
            req.state.user = user

        # 保存 token 和 user_info 缓存
        token = f"u-{make_token()}"
        await Cache.UserInfo(user.id).set(user.model_dump_json())
        await Cache.UserToken(token).set(str(user.id), ex=7200)

        return {"token": token}

    async def logout(self, req: Request):
        """退出登录，清除 token 和 user_info 缓存"""
        assert req.state.user_token, "Token is required."
        await Cache.UserToken(req.state.user_token).delete()
        await Cache.UserInfo(req.state.user.id).delete()
        return {}

    async def create_api_key(self, uid: ObjectId, data: UserModel.ApiKeyCreate) -> dict:
        token = f"t-{make_token(22)}"
        api_key = UserModel.ApiKey(token=token, **data.model_dump())
        res = await API_KEY_COLL.insert_one({"user_id": uid, **api_key.model_dump()})
        if not res.inserted_id:
            raise AppException(HttpResp.API_KEY_CREATE_FAIL)
        return {"token": token}

    async def delete_api_key(self, uid: ObjectId, token: str) -> dict:
        res = await API_KEY_COLL.delete_one({"user_id": uid, "token": token})
        if res.deleted_count == 0:
            raise AppException(HttpResp.API_KEY_CREATE_FAIL)
        return {}

    async def list_api_key(self, uid: ObjectId) -> List[UserModel.ApiKey]:
        docs = API_KEY_COLL.find({"user_id": uid}, {"_id": 0, "user_id": 0})
        data = []
        async for doc_ in docs:
            data.append(UserModel.ApiKey(**doc_))
        return data

    async def find_api_key(self, token: str) -> Optional[str]:
        """根据长期 token 查询用户信息"""
        api_token = Cache.TriPartyToken(token)
        user_id = await api_token.get()
        if user_id is None:
            doc = await API_KEY_COLL.find_one({"token": token})
            if not doc:
                return None
            # key_info = UserModel.UserApiKey(**doc["api_keys"][0])
            user_id = str(doc["user_id"])
            # 缓存 token
            await api_token.set(user_id, ex=86400)
        return user_id
