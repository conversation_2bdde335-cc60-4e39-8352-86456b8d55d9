import os
import json

import arrow

from apps.db import MongoDB
from apps.utils import singleton, sha256_str
from apps.models.user import SUPER_ADMIN_ID, SUPER_ADMIN_ROLE_ID, DEFAULTS_ROLE_ID
from apps.models.user import Status as UserStatus
from apps.models.permissions import Func<PERSON>d, RoleResourceFlag
from apps.config import get_settings


@singleton
class InitDataBase:
    def __init__(self, _=None):
        super().__init__()
        self.user_col = MongoDB.get_collection("users")
        self.roles_col = MongoDB.get_collection("roles")
        self.menus_col = MongoDB.get_collection("menus")
        self.metadata_col = MongoDB.get_collection("meta_data")

    async def is_first_run(self):
        """判断是否是第一次初始化"""
        user = await self.user_col.find_one({"username": "admin"})
        return user is None

    async def init_data(self) -> bool:
        if not await self.is_first_run():
            return False
        print("init data ......")
        await self.init_role()
        await self.init_user()
        await self.init_menu()
        await self.init_metadata()
        return True

    async def init_role(self):
        # fmt: off
        admin_role_menus = [1, 776, 818, 811, 701, 806, 779, 778, 780, 781, 801, 802, 803, 810, 819, 807, 808, 809, 100, 101, 102, 103, 104, 105, 106, 110, 111, 112, 113, 114, 120, 121, 122, 123, 124] # noqa
        staff_role_menus = [1, 776, 818, 811, 701, 806, 779, 778, 780, 781, 801, 803, 810, 819, 807, 808, 809] # noqa
        # fmt: on
        admin_role = {
            "_id": SUPER_ADMIN_ROLE_ID,
            "name": "super admin",
            "remark": "拥有所有权限",
            "sort": 0,
            "is_disable": 0,
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
            "menus": admin_role_menus,
            "apis": [],
            "resource_flag": RoleResourceFlag.ADMIN.value,
            "opconsoles": [],
            "vehicles": [],
        }
        staff_role = {
            "_id": DEFAULTS_ROLE_ID,
            "name": "司机",
            "remark": "除权限管理外，可使用平台基本功能",
            "sort": 0,
            "is_disable": 0,
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
            "menus": staff_role_menus,
            "apis": [FuncId.VehicleUpdate, FuncId.VehicleView, FuncId.OpcView, FuncId.OpcUpdate],
            "resource_flag": RoleResourceFlag.OPERATOR.value,
            "opconsoles": [],
            "vehicles": [],
        }
        await self.roles_col.insert_many([admin_role, staff_role])

    async def init_user(self):
        admin_user = {
            "_id": SUPER_ADMIN_ID,
            "username": "admin",
            "nickname": "管理员",
            "password": sha256_str("BuilderX@2024"),
            "role_ids": [SUPER_ADMIN_ROLE_ID],
            "avatar": "",
            "email": "<EMAIL>",
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
            "status": UserStatus.NORMAL.value,
        }
        staff_user = {
            "username": "staff",
            "nickname": "司机",
            "password": sha256_str("Builder@X"),
            "role_ids": [DEFAULTS_ROLE_ID],
            "avatar": "",
            "email": "<EMAIL>",
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
            "status": UserStatus.NORMAL.value,
        }
        await self.user_col.insert_many([admin_user, staff_user])

    async def init_menu(self):
        path = os.path.join(get_settings().root_path, "resources/menu.json")
        with open(path, "r") as f:
            data = json.loads(f.read())
        await self.menus_col.insert_many(data)

    async def init_metadata(self):
        path = os.path.join(get_settings().root_path, "resources/meta_data.json")
        with open(path, "r") as f:
            data = json.loads(f.read())
        await self.metadata_col.insert_many(data)
