<template>
  <div class="edit-popup">
    <el-card v-if="false" class="!border-none" shadow="never">
      <el-page-header class="mb-3 mt-2" :icon="ArrowLeft" @back="goBack">
        <template #content>
          <div class="flex items-center">
            <span class="text-large font-600 mr-3">
              {{ pageData.vehicle_name || $t("vehicle.车辆名称") }}
            </span>
          </div>
        </template>
        <template #extra>
          <div class="flex items-center">
            <!-- <el-button type="primary">编辑</el-button> -->
          </div>
        </template>
      </el-page-header>
      <el-divider />
      <el-descriptions class="ml-2" :column="3">
        <el-descriptions-item :label="$t('vehicle.车辆名称')">{{
          pageData.vehicle_name
        }}</el-descriptions-item>
        <el-descriptions-item :label="$t('vehicle.类型')">
          {{ pageData.vehicleTypeList[pageData.vehicle_type] }}
        </el-descriptions-item>
        <el-descriptions-item :label="$t('vehicle.状态')">
          <el-tag size="small" :type="pageData.is_online ? 'success' : 'info'">
            {{ pageData.is_online ? $t("vehicle.在线") : $t("vehicle.离线") }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
    <el-card class="!border-none relative" style="height: calc(100vh - 82px)" shadow="never">
      <div class="relative h-12 flex justify-center items-center">
        <div class="flex-1 flex justify-start items-center ml-[0px]">
          <span class="text-3xl font-bold ml-3">{{ pageData.vehicle_name }}</span>
          <el-tag class="ml-2" :type="pageData.is_online ? 'success' : 'info'">
            {{ pageData.is_online ? $t("vehicle.在线") : $t("vehicle.离线") }}
          </el-tag>
        </div>
        <div class="absolute right-1 flex justify-end items-center">
          <el-tooltip
            effect="light"
            v-if="pageData.currentIndex === 1"
            placement="top"
            :content="$t('vehicle.导出节点')"
          >
            <el-button type="default" circle @click="handleDownloadNode">
              <template #icon> <icon name="local-icon-xiazai" /> </template>
            </el-button>
          </el-tooltip>

          <el-upload
            class="upload-demo inline-block ml-3 mr-3"
            :auto-upload="false"
            :limit="99"
            :on-change="handleImportNode"
            :show-file-list="false"
            accept=".json"
          >
            <template #trigger>
              <el-tooltip
                effect="light"
                v-if="pageData.currentIndex === 1"
                placement="top"
                :content="$t('vehicle.导入节点')"
              >
                <el-button type="default" circle>
                  <template #icon> <icon name="local-icon-shangchuan" /> </template>
                </el-button>
              </el-tooltip>
            </template>
          </el-upload>

          <el-tooltip effect="light" placement="top">
            <template #content>
              {{ $t("vehicle.车辆数据同步") }}<br />
              <div v-for="item in syncStatusItems" :key="item.label">
                {{ `${item.label}：  ${item.status}` }}
              </div>
            </template>
            <el-button
              type="primary"
              :icon="Refresh"
              :disabled="!isSync"
              circle
              @click="handleSyncStatus"
            />
          </el-tooltip>
          <el-tooltip
            effect="light"
            v-if="pageData.currentIndex === 1"
            placement="top"
            :content="$t('vehicle.添加节点')"
          >
            <el-button type="primary" :icon="Plus" circle @click="handleAddNode" />
          </el-tooltip>

          <el-tooltip
            v-if="pageData.currentIndex === 12"
            effect="light"
            placement="top"
            :content="$t('vehicle.添加数据')"
          >
            <el-button type="primary" :icon="Plus" circle @click="handleAddMetaData" />
          </el-tooltip>

          <el-tooltip
            v-if="[6, 7, 8].includes(pageData.currentIndex)"
            effect="light"
            placement="top"
            :content="$t('vehicle.保存')"
          >
            <el-button type="primary" :icon="FolderChecked" circle @click="handleSaveCamera" />
          </el-tooltip>

          <el-tooltip
            v-if="pageData.currentIndex === 7"
            effect="light"
            placement="top"
            :content="$t('vehicle.添加网络相机')"
          >
            <el-button type="primary" :icon="Plus" circle @click="handleAddNetworkCamera" />
          </el-tooltip>
        </div>
      </div>

      <el-tabs
        class="node-tab mt-3"
        tabPosition="left"
        v-model="pageData.activeName"
        @tab-change="handleTabChange"
      >
        <el-tab-pane :name="14" label="辅助画面">
          <StreamConsole
            v-if="pageData.currentIndex === 14"
            :vehicleId="pageData.vehicle_id"
          ></StreamConsole>
        </el-tab-pane>

        <el-tab-pane v-if="hasVehicleManagePermission" :name="1" :label="$t('vehicle.节点设置')">
          <Node
            v-if="pageData.currentIndex === 1"
            ref="nodeRef"
            :vehicleId="pageData.vehicle_id"
            :vehicleOnline="pageData.is_online"
            @getDetail="getVehicleDetail"
          ></Node>
        </el-tab-pane>

        <el-tab-pane v-if="hasVehicleManagePermission" :name="6" :label="$t('vehicle.GMSL相机')">
          <GMSL
            ref="gmslRef"
            v-if="pageData.currentIndex === 6"
            :vehicleId="pageData.vehicle_id"
            :cameraType="1"
            @getDetail="getVehicleDetail"
          ></GMSL>
        </el-tab-pane>

        <el-tab-pane v-if="hasVehicleManagePermission" :name="7" :label="$t('vehicle.网络相机')">
          <GMSL
            ref="gmslRef"
            v-if="pageData.currentIndex === 7"
            :vehicleId="pageData.vehicle_id"
            :cameraType="2"
            @getDetail="getVehicleDetail"
          ></GMSL>
        </el-tab-pane>

        <el-tab-pane v-if="hasVehicleManagePermission" :name="8" :label="$t('vehicle.相机配置')">
          <GMSL
            ref="gmslRef"
            v-if="pageData.currentIndex === 8"
            :vehicleId="pageData.vehicle_id"
            :cameraType="99"
            @getDetail="getVehicleDetail"
          ></GMSL>
        </el-tab-pane>
        <el-tab-pane v-if="hasVehicleManagePermission" :name="9" :label="$t('vehicle.CAN口')">
          <CAN
            v-if="pageData.currentIndex === 9"
            :vehicleId="pageData.vehicle_id"
            @getDetail="getVehicleDetail"
          ></CAN>
        </el-tab-pane>

        <el-tab-pane v-if="hasVehicleManagePermission" :name="12" label="元数据">
          <MetaData
            ref="metaDataRef"
            v-if="pageData.currentIndex === 12"
            :vehicleId="pageData.vehicle_id"
          >
          </MetaData>
        </el-tab-pane>

        <el-tab-pane v-if="pageData.metaData['VIDEO.HLS.URL']" :name="11" label="实时预览">
          <HLS
            ref="hlsRef"
            :token="userStore.token"
            :isControls="true"
            :height="scrollHeight + 'px'"
            :url="hlsUrl"
          ></HLS>
        </el-tab-pane>

        <el-tab-pane v-if="pageData.metaData.DASHBOARD" :name="15" label="数据大盘">
          <Dashboard :dashboard-url="pageData.metaData.DASHBOARD" />
        </el-tab-pane>

        <el-tab-pane
          v-if="hasVehicleManagePermission && pageData.metaData.DASHBOARD"
          :name="13"
          label="数据展示"
        >
          <div class="h-[76vh]">
            <DataShow
              v-if="pageData.currentIndex === 13"
              :vehicleId="pageData.vehicle_id"
              :isFrom="1"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ArrowLeft, Plus, Refresh, FolderChecked } from "@element-plus/icons-vue";
import feedback from "@/utils/feedback";
import router from "@/router";
import useWebsocketStore from "@/stores/modules/websocket";
import useUserStore from "@/stores/modules/user";
import useMetadataStore from "@/stores/modules/metadata";
import { vehiclesDetail, syncData, metaDataList, metaDataDetail } from "@/api/device";

import HLS from "@/components/hls-player/index.vue";
import DataShow from "@/views/data_show/index.vue";
import GMSL from "./components/gmsl.vue";
import CAN from "./components/can.vue";
import MetaData from "./components/meta-data.vue";
import Node from "./components/node.vue";
import StreamConsole from "./stream-console.vue";
import Topic from "./components/topic.vue";
import Mse from "./components/mse-player.vue";
import Dashboard from "./components/dashboard.vue";

const route = useRoute();
const userStore = useUserStore();
const websocketStore = useWebsocketStore();
const metadataStore = useMetadataStore();

const metaDataRef = shallowRef();
const hlsRef = shallowRef();
const nodeRef = shallowRef();
const gmslRef = shallowRef();

const pageData: any = reactive({
  jsonFile: [],
  currentIndex: 14,
  activeName: 14,
  vehicleTypeList: {},
  vehicle_id: route.query?.id as string,
  vehicle_name: "",
  vehicle_type: 0,
  is_online: false,
  is_synced: false,
  metaData: { DASHBOARD: "", "VIDEO.HLS.URL": "" },
  sync_params_detail: {
    eth_can: true,
    gmsl_cameras: true,
    ip_cameras: true,
    ros_nodes: true,
  },
});

const hasVehicleManagePermission = computed(() => {
  const permissions = userStore.perms;
  const all_permission = "*";
  return permissions.some((key: string) => key === all_permission || key === "vehicle:manage");
});

const syncStatusItems = computed(() => {
  const details = pageData.sync_params_detail;
  // 定义配置数组，方便维护和扩展
  const itemsConfig = [
    { key: "ros_nodes", label: "ROS节点" },
    { key: "gmsl_cameras", label: "GMSL相机" },
    { key: "ip_cameras", label: "网络相机" },
    { key: "eth_can", label: "CAN口" },
  ];

  return itemsConfig.map((item) => ({
    label: item.label,
    // 使用可选链操作符 `?.` 防止 details 为 null 或 undefined 时出错
    // 如果状态为 true，显示“已同步”，否则显示“未同步”
    status: details?.[item.key] ? "已同步" : "未同步",
  }));
});

const getMetaDataDetail = async () => {
  const { lists } = await metaDataList(pageData.vehicle_id);
  if (lists.length) {
    lists.forEach((item: any) => {
      pageData.metaData[item.key] = item.value;
    });
  }
};

const getMetaDataItemDetail = async (key: string) => {
  const { value } = await metaDataDetail({
    vehicleId: pageData.vehicle_id,
    key,
  });
  return value;
};

onMounted(async () => {
  if (hasVehicleManagePermission.value) {
    pageData.currentIndex = 1;
    pageData.activeName = 1;
  }
  try {
    pageData.metaData["VIDEO.HLS.URL"] = await getMetaDataItemDetail("VIDEO.HLS.URL");
    pageData.metaData["DASHBOARD"] = await getMetaDataItemDetail("DASHBOARD");
  } catch (error) {
    console.log(error);
  }

  await getVehicleTypeList();
  await getVehicleDetail();
  websocketStore.send({
    cmd: "room.join",
    data: { vehicle_id: pageData.vehicle_id },
  });
});

onUnmounted(() => {
  websocketStore.send({
    cmd: "room.leave",
    data: { vehicle_id: pageData.vehicle_id },
  });
});

const isSync = computed(() => {
  return pageData.is_online === true && pageData.is_synced === false;
});

const getVehicleDetail = async () => {
  const res = await vehiclesDetail(pageData.vehicle_id);
  Object.assign(pageData, res);
};

const scrollHeight = computed(() => {
  return document.documentElement.clientHeight - 158;
});

const hlsUrl = computed(() => {
  const host = window.location.host;
  return `${location.protocol}//${host}/api/v2/vehicles/${pageData.vehicle_id}/service/live_stream/index.m3u8`;
});

const getVehicleTypeList = async () => {
  pageData.vehicleTypeList = await metadataStore.fetchMetadata("VEHICLE_TYPE", "kv");
};

const handleAddMetaData = async () => {
  metaDataRef.value.handleShowDialog({}, "add");
};

const goBack = async () => {
  await router.push({ path: "/device" });
};

const handleTabChange = async (index: any) => {
  pageData.currentIndex = index;
  if (pageData.currentIndex !== 11) {
    hlsRef.value.close();
  } else {
    hlsRef.value.play();
  }
};

const handleDownloadNode = async () => {
  nodeRef.value.handleDownloadNode();
};

const handleImportNode = async (file: any) => {
  nodeRef.value.handleImportNode(file);
};

const handleSyncStatus = async () => {
  await feedback.confirm("确定要同步状态？");
  feedback.loading("");
  await syncData(pageData.vehicle_id);
  await getVehicleDetail();
  feedback.msgSuccess("操作成功");
  feedback.closeLoading();
};

const handleAddNode = async () => {
  nodeRef.value.handleAddNode();
};

const handleAddNetworkCamera = () => {
  gmslRef.value.addNetworkItem();
};

const handleSaveCamera = () => {
  gmslRef.value.handleSave();
};

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
.node-tab {
  :deep(.el-tabs__content) {
    padding-top: 8px;
  }
}
</style>
