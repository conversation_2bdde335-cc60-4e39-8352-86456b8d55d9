from typing import List, Optional
from pydantic import BaseModel, Field
from .common import ObjectIdStr, SortFlag


class PagingModel(BaseModel):
    """分页模型"""

    page_no: int = Field(default=1, ge=1)
    page_size: int = Field(default=10, ge=3, le=100)


class ProdCountEvent(BaseModel):
    """生产计数事件模型"""

    timestamp: int = Field(..., description="时间戳")
    vehicle_id: ObjectIdStr = Field(..., description="车辆ID")
    vehicle_name: str = Field(..., description="车辆名称")
    group_name: str = Field(..., description="班组名称")
    driver_name: str = Field(..., description="司机名称")
    mode: str = Field(..., description="模式 simple complex 简单/复杂")
    event: str = Field(..., description="事件名称")
    start_time: Optional[str] = Field(None, description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")
    content: Optional[str] = Field(None, description="事件内容")

class ProdCountEventOut(ProdCountEvent):
    id: ObjectIdStr = Field(..., alias="_id")

class Group(BaseModel):
    name: str = Field(..., description="班组名称")
    driver_name: str = Field(..., description="司机名称")
    start_time: str = Field(..., description="开始时间")
    end_time: str = Field(..., description="结束时间")


class ProdCountConfig(BaseModel):
    vehicle_id: ObjectIdStr = Field(..., description="车辆ID")
    mode: str = Field(..., description="模式")
    load_threshold: int = Field(..., description="装车阈值")
    group: List[Group] = Field(..., description="班组")
    name: Optional[str] = Field(None, description="车辆名称")


class Query(PagingModel):
    """生产计数查询"""

    vehicle_id: Optional[ObjectIdStr] = Field(None, description="车辆ID")


class HistoryLoadOut(BaseModel):
    count: int
    lists: List[ProdCountEventOut]


class TeamRecordOut(BaseModel):
    group_name: str
    latest_timestamp: int
    count: int
    driver_name: Optional[str] = None


class HistoryTeamQuery(BaseModel):
    vehicle_id: Optional[ObjectIdStr] = None
    page_no: int = 1
    page_size: int = 10
    start_time: Optional[str] = None
    end_time: Optional[str] = None


class TeamRecordListOut(BaseModel):
    count: int
    lists: List[TeamRecordOut]
