from typing import List, Union, Tuple


__all__ = ["list_to_tree", "vehicle_type_conv", "FileLock"]


def list_to_tree(arr: List[dict], id_: str, pid: str, child: str) -> List[dict]:
    dict_list = []
    id_dict_map = {i.get(id_): i for i in arr}
    for i in arr:
        p_node = id_dict_map.get(i.get(pid))
        if p_node:
            if child in p_node and p_node[child] is not None:
                p_node[child].append(i)
            else:
                p_node[child] = [i]
        else:
            dict_list.append(i)
    return dict_list


def vehicle_type_conv(_type_id: Union[int, float]) -> Tuple[int, int]:
    """将车辆类型ID拆分为子类型"""
    if isinstance(_type_id, int):
        return (_type_id, 0)
    elif isinstance(_type_id, float):
        parts = str(_type_id).split(".")
        # 提取整数部分和第一个小数部分
        integer_part = int(parts[0]) if parts[0] else 0
        fractional_part = int(parts[1]) if len(parts) > 1 and parts[1] else 0
        return (integer_part, fractional_part)
    return (0, 0)


class FileLock:
    def __init__(self, filepath) -> None:
        self.filepath = filepath
        self.lockfile = None

    def acquire(self, block=False) -> bool:
        self.lockfile = open(self.filepath, "w")
       

    def release(self) -> None:
        if self.lockfile:
            self.lockfile.close()
            self.lockfile = None

    def __enter__(self):
        self.acquire()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()
