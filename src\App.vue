<script setup lang="ts">
import { useDark, useWindowSize, useThrottleFn } from "@vueuse/core";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import ja from "element-plus/es/locale/lang/ja";
import en from "element-plus/es/locale/lang/en";
import useAppStore from "./stores/modules/app";
import useWebsocketStore from "./stores/modules/websocket";
import useSettingStore from "./stores/modules/setting";
import { ScreenEnum } from "./enums/appEnums";
import useUserStore from "./stores/modules/user";
import useLocaleStore from "./stores/modules/locale";

const appStore = useAppStore();
const localeStore = useLocaleStore();
const websocketStore = useWebsocketStore();
const settingStore = useSettingStore();
const userStore = useUserStore();

const localeMap: any = {
  "zh-CN": zhCn,
  ja: ja,
  en: en,
};

const elConfig = reactive({
  zIndex: 3000,
  locale: localeMap[localeStore.getLocale] || zhCn,
});
localStorage.setItem("vueuse-color-scheme", "light");
const isDark = useDark();
onMounted(async () => {
  //设置主题色
  settingStore.setTheme(isDark.value);
  //@ts-ignore
  console.log(`构建时间：${__APP_BUILD_TIME__}`);
});

const { width } = useWindowSize();
watch(
  width,
  useThrottleFn((value: number) => {
    if (value > ScreenEnum.SM) {
      appStore.setMobile(false);
      appStore.toggleCollapsed(false);
    } else {
      appStore.setMobile(true);
      appStore.toggleCollapsed(true);
    }
    if (value < ScreenEnum.MD) {
      appStore.toggleCollapsed(true);
    }
  }),
  {
    immediate: true,
  }
);
watch(
  () => userStore.userInfo.id,
  (value) => {
    if (value) websocketStore.init();
  }
);

watch(
  () => localeStore.localInfo.locale,
  (value) => {
    elConfig.locale = localeMap[value] || zhCn;
  }
);
</script>

<template>
  <el-config-provider :locale="elConfig.locale" :z-index="elConfig.zIndex">
    <router-view />
  </el-config-provider>
</template>

<style></style>
