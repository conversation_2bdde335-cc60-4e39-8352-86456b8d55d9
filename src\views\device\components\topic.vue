<template>
  <el-scrollbar :height="scrollHeight">
    <div class="topic-wrap">
      <el-descriptions class="margin-top" :column="3" border>
        <el-descriptions-item v-for="(item, index) in testData" :key="index">
          <template #label>
            <div class="cell-item">{{ item.label }}</div>
          </template>
          <div class="topic-value" :class="{ 'is-change': item.isChange }">{{ item.value }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup camera_name="gmsl">


const scrollHeight = computed(() => {
  return document.documentElement.clientHeight - 360;
});

const testData = ref([
  {
    key: "forward",
    label: "前进",
    value: "0",
    isChange: false,
  },
  {
    key: "back",
    label: "后退",
    value: "0",
    isChange: false,
  },
  {
    key: "left",
    label: "左转",
    value: "0",
    isChange: false,
  },
  {
    key: "right",
    label: "右转",
    value: "0",
    isChange: false,
  },
  {
    key: "up",
    label: "上升",
    value: "0",
    isChange: false,
  },
  {
    key: "down",
    label: "下降",
    value: "0",
    isChange: false,
  },
  {
    key: "yaw",
    label: "偏航",
    value: "0",
    isChange: false,
  },
  {
    key: "pitch",
    label: "俯仰",
    value: "0",
    isChange: false,
  },
  {
    key: "roll",
    label: "滚转",
    value: "0",
    isChange: false,
  },
]);

const changeData = () => {
  const random = (min: number, max: number) => Math.floor(Math.random() * (max - min + 1) + min);

  setInterval(() => {
    let indexList: any = [];
    for (let i = 0; i < 4; i++) {
      const changeIndex = random(0, testData.value.length - 1);
      indexList.push(changeIndex);
    }
    testData.value.forEach((item, index) => {
      if (indexList.includes(index)) {
        item.value = String(random(0, 100));
        item.isChange = true;
      } else {
        item.isChange = false;
      }
    });
  }, 1000);
};

const client: any = ref(null); // mqtt客户端

/**订阅列表**/
const subAlarmTopicType = () => {
  const topics1 = ["test_zjFMJxkJ/to_excavator", "topic/mqttx"];
  client.value.subscribe(topics1, { qos: 1 }, (err: any, granted: any) => {
    if (err) {
      console.log("订阅失败", err);
    } else {
      console.log("订阅成功", granted);
    }
  });
};

onMounted(() => {
  changeData();
});
</script>

<style lang="scss" scoped>
.topic-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
}
.topic-value {
  min-width: 60px;
  text-align: center;
}
.is-change {
  background-color: #ff5f00;
}
</style>
