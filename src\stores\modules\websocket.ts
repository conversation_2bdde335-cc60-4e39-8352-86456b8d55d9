import { defineStore } from "pinia";
import feedback from "@/utils/feedback";
import useUserStore from "@/stores/modules/user";
import { timeFormat } from "@/utils/util";

const HEARTBEAT_INTERVAL = 5000; // Heartbeat interval in ms
const RECONNECT_INTERVAL = 15000; // Reconnect interval in ms
const MAX_RECONNECT_COUNT = 3; // Maximum number of reconnection attempts

type MessageHandler = (data: any) => void;

interface VehicleInfo {
  emergency_brake_status: number;
  console_emergency_stop_status: number;
  remote_control_enable_status: number;
  local_remote_emergency_stop_status: number;
  function_button_status: number;
  fuel_level: number;
  fuel_delivery_pressure: number;
  coolant_level: number;
  engine_working_hours: number;
  left_main_pump_pressure: number;
  right_main_pump_pressure: number;
  oil_pressure: number;
  battery_voltage: number;
  water_temperature: number;
  ambient_temperature: number;
  hydraulic_oil_temperature: number;
  engine_rpm: number;
  oil_temperature: number;
  cab_temperature: number;
}

interface VehicleControl {
  left_joystick_x: number;
  left_joystick_y: number;
  left_joystick_z_or_kx: number;
  left_joystick_ky: number;
  right_joystick_x: number;
  right_joystick_y: number;
  right_joystick_z_or_kx: number;
  right_joystick_ky: number;
  left_pedal: number;
  right_pedal: number;
}

interface NodeStatusData {
  name: string;
  value: number;
}

interface ControlChartData {
  name: string;
  value: [string, number];
}

interface WebsocketState {
  ws: WebSocket | null;
  isConnect: boolean;
  heartbeatTimer: ReturnType<typeof setInterval> | null;
  reconnectTimer: ReturnType<typeof setTimeout> | null;
  reconnectCount: number;
  nodeStatus: Record<string, NodeStatusData[]>;
  vehicleInfo: VehicleInfo;
  vehicleControl: VehicleControl;
  controlChartData: ControlChartData[];
  messageHandlers: Record<string, MessageHandler[]>;
}

const useWebsocketStore = defineStore({
  id: "websocket",
  state: (): WebsocketState => ({
    ws: null,
    isConnect: false,
    heartbeatTimer: null,
    reconnectTimer: null,
    reconnectCount: 0,
    nodeStatus: {},
    vehicleInfo: {
      emergency_brake_status: 0, // 紧急刹车状态
      console_emergency_stop_status: 0, // 控制台紧急停止状态
      remote_control_enable_status: 2, // 远程控制启用状态
      local_remote_emergency_stop_status: 0, // 本地远程紧急停止状态
      function_button_status: 4352, // 功能按钮状态
      fuel_level: 0, // 油量
      fuel_delivery_pressure: 0, // 燃油输送压力
      coolant_level: 0, // 冷却液水平
      engine_working_hours: 0, // 发动机工作小时数
      left_main_pump_pressure: 0, // 左主泵压力
      right_main_pump_pressure: 0, // 右主泵压力

      oil_pressure: 404, // 油压
      battery_voltage: 28.1, // 电池电压

      water_temperature: 59, // 冷却液温度
      ambient_temperature: 2.3, // 环境温度
      hydraulic_oil_temperature: 25, // 液压油温度

      engine_rpm: 800, // 发动机转速
      oil_temperature: 0, // 油温
      cab_temperature: 0, // 驾驶室温度 ? 不显示
    },
    vehicleControl: {
      left_joystick_x: 0,
      left_joystick_y: 0,
      left_joystick_z_or_kx: 0,
      left_joystick_ky: 0,
      right_joystick_x: 0,
      right_joystick_y: 0,
      right_joystick_z_or_kx: 0,
      right_joystick_ky: 0,
      left_pedal: 0,
      right_pedal: 0,
    },
    controlChartData: [
      {
        name: new Date().toString(),
        value: [timeFormat(null, "yyyy-mm-dd hh:MM:ss"), 0],
      },
    ],
    messageHandlers: {},
  }),
  actions: {
    _handleAlarm(res: any) {
      if (res.data.level === "error") {
        feedback.notifyError(res.data.msg, "websocket_alarm_error");
      } else {
        feedback.notifySuccess(res.data.msg);
      }
    },

    _handleRosNodeStatus(res: any) {
      const id = res.room_id.split(".")[1];
      if (!id) return;
      this.nodeStatus[id] = Object.entries(res.data).map(([key, value]: [string, any]) => ({
        name: key,
        value: value?.is_live ? 1 : 0,
      }));
    },

    _handleVehicleInfo(res: any) {
      Object.keys(this.vehicleInfo).forEach((key) => {
        if (res.data[key] != null) {
          this.vehicleInfo[key as keyof VehicleInfo] = res.data[key];
        }
      });
    },

    _handleVehicleControl(res: any) {
      Object.keys(this.vehicleControl).forEach((key) => {
        if (res.data[key] != null) {
          this.vehicleControl[key as keyof VehicleControl] = res.data[key];
        }
      });

      const sumOfAbsoluteValues = Object.values(this.vehicleControl)
        .map((item) => Math.abs(item))
        .reduce((sum, current) => sum + current, 0);

      this.controlChartData.push({
        name: new Date().toString(),
        value: [timeFormat(null, "yyyy-mm-dd hh:MM:ss"), sumOfAbsoluteValues || 0],
      });
    },

    _onOpen() {
      this.isConnect = true;
      this.reconnectCount = 0;
    },

    _onMessage(event: MessageEvent) {
      try {
        const res = JSON.parse(event.data);
        const coreHandlers: Record<string, (data: any) => void> = {
          "alarm.ros": this._handleAlarm,
          "alarm.system": this._handleAlarm,
          "status.ros_node": this._handleRosNodeStatus,
          "status.vehicle.info": this._handleVehicleInfo,
          "status.vehicle.control": this._handleVehicleControl,
        };

        const coreHandler = coreHandlers[res.cmd];
        if (coreHandler) {
          coreHandler.call(this, res);
        }

        // Execute dynamic handlers
        const dynamicHandlers = this.messageHandlers[res.cmd];
        if (dynamicHandlers) {
          dynamicHandlers.forEach(handler => handler(res));
        }

      } catch (error) {
        console.error("Failed to parse WebSocket message:", error);
      }
    },

    _onClose() {
      this.isConnect = false;
      this._clearHeartbeat();
      this.reconnect();
    },

    _onError(event: Event) {
      console.error("WebSocket Error:", event);
    },

    init() {
      if (this.ws && this.isConnect) return;
      if (!("WebSocket" in window)) {
        feedback.msgWarning("当前浏览器不支持WebSocket");
        return;
      }

      const userStore = useUserStore();
      if (!userStore.token) {
        feedback.msgWarning("未登录，无法建立WebSocket连接");
        return;
      }

      const host = import.meta.env.PROD ? window.location.host : import.meta.env.VITE_APP_SOCKET_URL;
      const protocol = window.location.protocol === "https:" ? "wss" : "ws";
      const wsUrl = `${protocol}://${host}/api/v2/ws/user?authorization=${userStore.token}`;

      this.ws = new WebSocket(wsUrl);
      this.ws.onopen = this._onOpen.bind(this);
      this.ws.onmessage = this._onMessage.bind(this);
      this.ws.onclose = this._onClose.bind(this);
      this.ws.onerror = this._onError.bind(this);
    },

    send(data: object) {
      if (this.ws && this.isConnect) {
        this.ws.send(JSON.stringify(data));
      } else {
        feedback.msgWarning("WebSocket未连接，消息发送失败");
      }
    },

    _clearHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },
    
    heartbeat() {
      this._clearHeartbeat();
      this.heartbeatTimer = setInterval(() => {
        this.send({ content: "ping_web" });
      }, HEARTBEAT_INTERVAL);
    },

    reconnect() {
      if (this.reconnectCount >= MAX_RECONNECT_COUNT) {
        console.error("WebSocket重连次数已达上限");
        return;
      }
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }
      this.reconnectTimer = setTimeout(() => {
        this.reconnectCount++;
        this.init();
      }, RECONNECT_INTERVAL);
    },

    close() {
        if (this.ws) {
            this.ws.onclose = null;
            this.ws.close();
            this._clearHeartbeat();
            this.isConnect = false;
        }
    },

    registerMessageHandler(cmd: string, handler: MessageHandler) {
        if (!this.messageHandlers[cmd]) {
            this.messageHandlers[cmd] = [];
        }
        this.messageHandlers[cmd].push(handler);
    },

    unregisterMessageHandler(cmd: string, handler: MessageHandler) {
        const handlers = this.messageHandlers[cmd];
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
  },
});

export default useWebsocketStore;
