import request from "@/utils/request";

const API_VERSION = 'v2';

// 车辆列表
export function vehiclesList(params?: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/vehicles`, params });
}

// 车辆详情
export function vehiclesDetail(id: string) {
  return request.get({ url: `/${API_VERSION}/vehicles/${id}` });
}

// 车辆 =》设备列表
export function devicesList(id: string) {
  return request.get({ url: `/${API_VERSION}/vehicles/${id}` });
}

// 删除车辆
export function vehicleDelete(id: string) {
  return request.delete({ url: `/${API_VERSION}/vehicles/${id}` });
}

// 增加车辆
export function vehicleAdd(params?: Record<string, any>) {
  return request.post({ url: `/${API_VERSION}/vehicles/`, params });
}

// 编辑车辆
export function vehicleUpdate(params?: any) {
  const { id_, ...data } = params;
  return request.put({ url: `/${API_VERSION}/vehicles/${id_}`, data });
}

// 设备列表
export function deviceList(params: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/devices/`, params });
}

// 删除设备
export function deviceDelete(params: Record<string, any>) {
  const { vehicle_id, device_id } = params;
  return request.delete({ url: `/${API_VERSION}/vehicles/${vehicle_id}/devices/${device_id}` });
}

// 未连接设备列表
export function unRegisterDeviceList(params: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/devices/vehicles/un_register_list`, params });
}

// 更新注册设备
export function unRegisterDeviceUpdate(params: Record<string, any>) {
  const { vehicle_id } = params;
  return request.put({ url: `/${API_VERSION}/vehicles/${vehicle_id}/device_bind`, params });
}

/* 节点管理 */

// 设备数据同步
export function syncData(vehicle_id: string) {
  return request.put({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/data_sync` });
}

// ros节点列表
export function nodeList(vehicle_id: string) {
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/program_node` });
}

// 添加节点
export function addNode(params: Record<string, any>) {
  const { vehicle_id, data } = params;
  return request.post({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/program_node`, data });
}

export function importNode(params: Record<string, any>) {
  const { vehicle_id, data } = params;
  return request.post({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/program_node/import_nodes`, data });
}

// 设置参数
export function editRos(params: Record<string, any>) {
  const { vehicle_id, device_id, ...data } = params;
  return request.put({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/program_node`, data });
}
// 设置混流接口
export function editMixLayout(params: Record<string, any>) {
  const { vehicle_id, ...data } = params;
  return request.put({ url: `/${API_VERSION}/vehicles/${vehicle_id}/service/mix_layout`, data });
}
// 设置混流接口
export function getMixLayout(vehicle_id: string) {
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/service/mix_layout` });
}

// 删除ros节点
export function delRosNode(params: Record<string, any>) {
  const { vehicle_id, node_name } = params;
  return request.delete({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/program_node?node_name=${node_name}` });
}

// 节点控制
export function controlNode(params: Record<string, any>) {
  const { vehicle_id, data } = params;
  return request.post({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/program_node/control`, data });
}

/* 模板管理 */

// 混流模板列表
export function layoutList(vehicle_id: string) {
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/service/mix_template` });
}
// 新建混流模板
export function layoutAdd(params: Record<string, any>) {
  return request.post({ url: `/${API_VERSION}/vehicles/${params.vehicle_id}/service/mix_template`, params });
}
// 混流模板删除
export function layoutDelete(params: Record<string, any>) {
  const { vehicle_id, template_name } = params;
  return request.delete(
    { url: `/${API_VERSION}/vehicles/${vehicle_id}/service/mix_template`, params: { template_name } },
    { isParamsToData: true }
  );
}
// 混流模板编辑
export function layoutEdit(params: Record<string, any>) {
  return request.put({ url: `/${API_VERSION}/vehicles/${params.vehicle_id}/service/mix_template`, params });
}

/* 设备型号 */

// 设备型号列表
export function deviceModelLists(params?: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/devices/model`, params });
}

// 添加设备
export function deviceModelAdd(params: Record<string, any>) {
  return request.post({ url: `/${API_VERSION}/devices/model`, params });
}

// 设备详情
export function deviceModelDetail(params: Record<string, any>) {
  return request.get({ url: `/${API_VERSION}/devices/model/detail`, params });
}

// 编辑设备
export function deviceModelEdit(params: Record<string, any>) {
  return request.put({ url: `/${API_VERSION}/devices/model`, params });
}

// 删除设备
export function deviceModelDelete(params: Record<string, any>) {
  return request.delete({ url: `/${API_VERSION}/devices/model`, params });
}

// 获取相机列表信息
export function cameraListApi(data: Record<string, any>) {
  const { vehicle_id, camera_type } = data;
  const params = { camera_type: camera_type } as Record<string, any>;
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/cameras`, params });
}

// 保存相机配置
export function cameraEdit(params: Record<string, any>) {
  const { vehicle_id, camera_type, camera_list: data } = params;
  return request.put({
    url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/cameras?camera_type=${camera_type}`,
    data,
  });
}

// 获取相机状态
export function getCameraStatus(params: Record<string, any>) {
  const { vehicle_id } = params;
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/cameras/status` });
}

// 获取相机状态
export function setCameraStatus(params: Record<string, any>) {
  const { vehicle_id, camera_list: data } = params;
  return request.put({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/cameras/status`, data });
}

// 获取启用相机列表
export function getEnableCameraList(params: Record<string, any>) {
  const { vehicle_id } = params;
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/service/cameras` });
}

// 获取can列表信息
export function canListApi(params: Record<string, any>) {
  const { vehicle_id } = params;
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/can` });
}

// 保存can配置
export function canEdit(params: Record<string, any>) {
  const { vehicle_id, can_list: data } = params;
  return request.put({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/can`, data });
}

/* 车辆元数据 */

// 元数据列表
export function metaDataList(vehicle_id: string) {
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/metadata` });
}

// 添加元数据
export function metaDataAdd(params: Record<string, any>) {
  const { vehicleId: vehicle_id, ...rest } = params;
  return request.post({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/metadata`, params: rest });
}

// 编辑元数据
export function metaDataEdit(params: Record<string, any>) {
  const { vehicleId: vehicle_id, ...rest } = params;
  return request.put({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/metadata`, params: rest });
}

// 删除元数据
export function metaDataDelete(params: Record<string, any>) {
  const { vehicleId: vehicle_id, key } = params;
  return request.delete({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/metadata?key_name=${key}` });
}

// 数据详情
export function metaDataDetail(params: Record<string, any>) {
  const { vehicleId: vehicle_id, key } = params;
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/metadata/${key}` });
}

// 获取安卓参数
export function getAndroidParams(vehicle_id: string) {
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/android` });
}

export function setAndroidParams(params: Record<string, any>) {
  const { vehicle_id, params: data } = params;
  return request.put({ url: `/${API_VERSION}/vehicles/${vehicle_id}/parameter/android`, params: data });
}

// 获取手柄操控数据
export function getControllerData(params: Record<string, any>) {
  const { vehicle_id } = params;
  return request.get({ url: `/${API_VERSION}/vehicles/${vehicle_id}/service/signal/joystick`, params });
}
