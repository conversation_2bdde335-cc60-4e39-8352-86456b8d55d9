<template>
  <popup
    custom-class="no-footer"
    ref="popupRef"
    title="项目编辑"
    width="550px"
    @confirm="handleSubmit"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
      <el-form-item label="项目名称" prop="prj_name">
        <el-input v-model="formData.prj_name" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="项目描述" prop="prj_desc">
        <el-input v-model="formData.prj_desc" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="销售经理" prop="salesperson">
        <el-select v-model="salespersonModel" placeholder="请选择" multiple clearable @change="handlePersonChange">
          <el-option
            v-for="item in salespersonList"
            :key="item.username"
            :label="item.nickname"
            :value="item.username"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="甲方单位" prop="first_party">
        <el-input v-model="formData.first_party" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="是否公网" prop="is_public_network">
        <el-switch v-model="formData.is_public_network" />
      </el-form-item>
      <el-form-item label="是否接入平台" prop="is_oms">
        <el-switch v-model="formData.is_oms" />
      </el-form-item>
    </el-form>
  </popup>
</template>

<script lang="ts" setup>
import Popup from "@/components/popup/index.vue";
import { getUserList } from "../util";

const emit = defineEmits(["success", "close"]);
const popupRef = shallowRef<InstanceType<typeof Popup>>();

const formData: any = reactive({
  id: "",
  prj_name: "",
  prj_desc: "",
  salesperson: [],
  first_party: "",
  is_public_network: false,
  is_oms: false,
});

const salespersonList: any = ref([]);
const salespersonModel: any = ref([]);

const formRules = reactive({
  prj_name: [
    {
      required: true,
      message: "请输入项目名称",
      trigger: "blur",
    },
  ],
});

const open = () => {
  popupRef.value?.open();
};

const handleClose = () => {
  emit("close");
};

const handleSubmit = () => {
  emit("success", formData);
  handleClose();
};

const setFormData = async (data: any) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handlePersonChange = (e: any) => {
  formData.salesperson = salespersonList.value.filter((item: any) => e.includes(item.username));
};

onMounted(async () => {
  salespersonList.value = await getUserList();
  salespersonModel.value = formData.salesperson.map((item: any) => item.username);
});

onUnmounted(() => {});

defineExpose({
  open,
  setFormData,
});
</script>

<style scoped></style>
