<template>
  <div class="bg-white rounded">
    <!-- gmsl相机 -->
    <el-table
      v-if="cameraType === 1"
      :data="gmslList"
      border
      style="width: 100%"
      :height="scrollHeight"
      :header-cell-style="{ background: 'var(--table-header-bg-color)' }"
    >
      <el-table-column prop="id" label="ID" width="100" align="center" />
      <!-- <el-table-column :label="$t('vehicle.预览')" width="120" align="center">
        <template #default="scope">
          <el-image
            style="width: 50px; height: 50px"
            v-if="scope.row.type !== 'MVG2CB-NONE'"
            :src="camera"
            fit="fill"
          />
          <div v-else class="leading-[50px]">{{ $t("vehicle.请选择") }}</div>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('vehicle.名称')" align="center">
        <template #default="scope">
          <el-input v-model="scope.row.camera_name" :placeholder="$t('vehicle.请输入相机名称')" />
        </template>
      </el-table-column>
      <el-table-column label="ID" align="center">
        <template #default="scope">
          <el-input
            v-model="scope.row.camera_id"
            :placeholder="$t('vehicle.请输入相机ID')"
            @input="(val) => (scope.row.camera_id = val.replace(/[^A-Za-z0-9_]/g, ''))"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('vehicle.格式')" align="center">
        <template #default="scope">
          <el-select v-model="scope.row.format" :placeholder="$t('vehicle.请选择')">
            <el-option
              v-for="opt in ['UYVY', 'VYUY', 'YUYV', 'YVYU']"
              :key="opt"
              :label="opt"
              :value="opt"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="$t('vehicle.类型')" align="center">
        <template #default="scope">
          <el-select v-model="scope.row.type" :placeholder="$t('vehicle.请选择')">
            <el-option
              v-for="option in CAMERA_TYPE_LIST"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>

    <!-- 网络相机 -->
    <el-table
      v-if="cameraType === 2"
      :data="networkList"
      border
      style="width: 100%"
      :height="scrollHeight"
      :header-cell-style="{ background: 'var(--table-header-bg-color)' }"
    >
      <el-table-column prop="id" label="ID" width="120" align="center" />
      <!-- <el-table-column :label="$t('vehicle.预览')" width="90" align="center">
        <template #default="scope">
          <el-image
            style="width: 50px; height: 50px"
            v-if="scope.row.uri !== ''"
            :src="camera"
            fit="fill"
          />
          <div v-else class="leading-[50px]">{{ $t("vehicle.请配置") }}</div>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('vehicle.名称')" align="center">
        <template #default="scope">
          <el-input v-model="scope.row.camera_name" :placeholder="$t('vehicle.请输入相机名称')" />
        </template>
      </el-table-column>
      <el-table-column label="ID" align="center">
        <template #default="scope">
          <el-input
            v-model="scope.row.camera_id"
            :placeholder="$t('vehicle.请输入相机ID')"
            @input="(val) => (scope.row.camera_id = val.replace(/[^A-Za-z0-9_]/g, ''))"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('vehicle.格式')" align="center" width="120">
        <template #default="scope">
          <el-select v-model="scope.row.format" :placeholder="$t('vehicle.请选择')">
            <el-option v-for="opt in ['h264', 'h265']" :key="opt" :label="opt" :value="opt" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column :label="$t('vehicle.地址')" align="center" min-width="150">
        <template #default="scope">
          <el-input
            type="textarea"
            :rows="3"
            v-model="scope.row.uri"
            :placeholder="$t('vehicle.请输入相机地址')"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('stream.宽度')" align="center">
        <template #default="scope">
          <el-input-number v-model="scope.row.width" controls-position="right" :min="0" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('stream.高度')" align="center">
        <template #default="scope">
          <el-input-number v-model="scope.row.height" controls-position="right" :min="0" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.操作')" align="center" width="100">
        <template #default="scope">
          <el-button
            v-if="scope.$index === networkList.length - 1"
            type="danger"
            link
            @click="handleDeleteCan(scope.row.id)"
            >{{ $t("vehicle.删除") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 相机状态 -->
    <el-table
      v-if="cameraType === 99"
      :data="statusList"
      border
      style="width: 100%"
      :height="scrollHeight"
      :header-cell-style="{ background: 'var(--table-header-bg-color)' }"
    >
      <el-table-column prop="id" label="ID" width="180" align="center" />
      <!-- <el-table-column :label="$t('vehicle.预览')" width="120" align="center">
        <template #default="scope">
          <el-image
            style="width: 50px; height: 50px"
            v-if="scope.row.camera_name && scope.row.camera_name !== ''"
            :src="camera"
            fit="fill"
          />
          <div v-else class="leading-[50px]">{{ $t("vehicle.请配置") }}</div>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('vehicle.名称')" prop="camera_name" align="center" >
        <template #default="scope">
          {{ scope.row.camera_name || "--" }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('vehicle.是否启动')" align="center">
        <template #default="scope">
          <el-switch
            :disabled="scope.row.camera_name === '' && !scope.row.is_enable"
            v-model="scope.row.is_enable"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script lang="ts" setup camera_name="gmsl">
import { CAMERA_TYPE_LIST } from "@/utils/constants";
import feedback from "@/utils/feedback";
import camera from "@/assets/images/sensing_gmsl2.png";

import { cameraListApi, cameraEdit, getCameraStatus, setCameraStatus } from "@/api/device";
import { useI18n } from "vue-i18n";

const { t, locale } = useI18n();

const props = defineProps<{
  vehicleId: string;
  cameraType: number;
}>();

const scrollHeight = computed(() => {
  return document.documentElement.clientHeight - 180;
});

const emit = defineEmits(["getDetail"]);

const handleSave = async () => {
  const flag = checkObj();
  if (!flag) return;

  switch (props.cameraType) {
    case 1:
      if (!checkDuplicateCamera(gmslList.value)) return;
      await cameraEdit({
        vehicle_id: props.vehicleId,
        camera_type: props.cameraType,
        camera_list: gmslList.value,
      });
      emit("getDetail");
      break;

    case 2:
      if (!checkDuplicateCamera(networkList.value)) return;
      await cameraEdit({
        vehicle_id: props.vehicleId,
        camera_type: props.cameraType,
        camera_list: networkList.value,
      });
      emit("getDetail");
      break;

    case 99:
      await setCameraStatus({
        vehicle_id: props.vehicleId,
        camera_list: statusList.value,
      });
      emit("getDetail");
      break;

    default:
      break;
  }

  feedback.msgSuccess("保存成功");
};

const checkObj = () => {
  let flag = true;
  gmslList.value.forEach((item) => {
    if (
      !(
        !(item.camera_id == "" || item.camera_name == "" || item.type == "MVG2CB-NONE") ||
        (item.camera_id == "" && item.camera_name == "" && item.type == "MVG2CB-NONE")
      )
    ) {
      feedback.msgError(`${item.id} 相机ID、名称和类型不能为空`);
      flag = false;
    }
  });
  networkList.value.forEach((item: any) => {
    if (
      !(
        !(item.camera_id == "" || item.camera_name == "" || item.type == "MVG2CB-NONE") ||
        (item.camera_id == "" && item.camera_name == "" && item.type == "MVG2CB-NONE")
      )
    ) {
      feedback.msgError(`${item.id} 相机ID、名称和类型不能为空`);
      flag = false;
    }
  });
  return flag;
};

const checkDuplicateCamera = (cameraList: any) => {
  const cameraIds = new Set();
  const cameraNames = new Set();

  for (const camera of cameraList) {
    if (camera.camera_id && cameraIds.has(camera.camera_id)) {
      feedback.msgError(`相机ID重复: ${camera.camera_id}`);
      return false;
    }
    if (camera.camera_name && cameraNames.has(camera.camera_name)) {
      feedback.msgError(`相机名称重复: ${camera.camera_name}`);
      return false;
    }
    cameraIds.add(camera.camera_id);
    cameraNames.add(camera.camera_name);
  }

  return true;
};

const gmslList = ref([
  // 网络摄像头 地址 名字 宽高 是否启动
  {
    id: "VIDEO0",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "UYVY",
  },
  {
    id: "VIDEO1",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "UYVY",
  },
  {
    id: "VIDEO2",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "UYVY",
  },
  {
    id: "VIDEO3",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "UYVY",
  },
  {
    id: "VIDEO4",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "UYVY",
  },
  {
    id: "VIDEO5",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "UYVY",
  },
  {
    id: "VIDEO6",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "UYVY",
  },
  {
    id: "VIDEO7",
    type: "MVG2CB-NONE",
    is_enable: false,
    camera_id: "",
    camera_name: "",
    format: "UYVY",
  },
]);

const networkList: any = ref([]);

const statusList: any = ref([
  {
    id: "VIDEO0",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO1",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO2",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO3",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO4",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO5",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO6",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
  {
    id: "VIDEO7",
    camera_name: "",
    camera_id: "",
    is_enable: false,
  },
]);

const getCameraList = async () => {
  switch (props.cameraType) {
    case 1:
      const { lists: list } = await cameraListApi({
        vehicle_id: props.vehicleId,
        camera_type: props.cameraType,
      });
      list.forEach((item: any) => {
        gmslList.value.forEach((camera) => {
          if (camera.id === item.id) {
            Object.assign(camera, item);
          }
        });
      });
      break;

    case 2:
      const { lists: list1 } = await cameraListApi({
        vehicle_id: props.vehicleId,
        camera_type: props.cameraType,
      });
      networkList.value = list1;
      break;

    case 99:
      const { lists: list2 } = await getCameraStatus({
        vehicle_id: props.vehicleId,
      });
      if (list2.length > 0) statusList.value = list2;
      break;

    default:
      break;
  }
};

const addNetworkItem = () => {
  networkList.value.push({
    id: "NETWORK" + networkList.value.length,
    camera_id: "",
    camera_name: "",
    format: "h264",
    uri: "",
    is_enable: false,
    height: 0,
    width: 0,
  });
};

const handleDeleteCan = (id: string) => {
  const flag = networkList.value.findIndex((item: any) => item.id == id);
  if (flag !== -1) networkList.value.splice(flag, 1);
};

onMounted(() => {
  getCameraList();
});

defineExpose({
  addNetworkItem,
  handleSave,
});
</script>
<style lang="scss" scoped></style>
