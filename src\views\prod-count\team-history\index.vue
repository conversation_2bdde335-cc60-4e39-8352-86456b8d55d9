<!-- 历史班组记录 -->
<template>
  <div class="team-history">
    <el-card id="search" class="!border-none" shadow="never">
      <el-form class="ls-form" :model="formData" inline>
        <el-form-item label="车辆ID">
          <el-input
            class="w-[220px]"
            placeholder="请输入"
            v-model="formData.vehicle_id"
            clearable
          />
        </el-form-item>

        <el-form-item label="记录时间">
          <daterange-picker
            v-model:startTime="startTime"
            v-model:endTime="endTime"
            type="daterange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="resetPageFormat">查询</el-button>
          <el-button @click="resetParams">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="!border-none mt-4" shadow="never" v-loading="pager.loading">
      <div>
        <el-table :height="tableHeight" :data="pager.lists" size="large">
          <el-table-column label="班组名称" prop="group_name" />
          <el-table-column label="装车数" prop="count" />
          <el-table-column label="最后更新时间" prop="latest_timestamp">
            <template #default="{ row }">
              {{ dayjs(row.latest_timestamp).local().format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="teamHistory">
import { getHistoryTeam } from "@/api/prod-count";
import { usePaging } from "@/hooks/usePaging";
import { formatDate2ISO } from "@/utils/util";
import dayjs from "dayjs";

// 查询表单
const formData = ref({
  vehicle_id: "",
  start_time: "",
  end_time: "",
});

const startTime = ref("");
const endTime = ref("");
const tableHeight = ref(0);

const calcTableHeight = () => {
  const searchHeight = document.getElementById("search")?.clientHeight || 0;
  tableHeight.value = window.innerHeight - searchHeight - 50 - 64 - 48 - 16;
};

onMounted(() => {
  calcTableHeight();
  window.addEventListener("resize", calcTableHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", calcTableHeight);
});

const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: getHistoryTeam,
  params: formData.value,
});

const resetPageFormat = () => {
  if (startTime.value) {
    formData.value.start_time = formatDate2ISO(startTime.value);
    formData.value.end_time = formatDate2ISO(endTime.value);
  }
  resetPage();
};

</script>

<style lang="scss" scoped></style>
