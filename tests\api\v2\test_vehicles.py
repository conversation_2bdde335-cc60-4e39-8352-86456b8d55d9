"""
车辆管理相关接口测试
"""

import json
import faker
from bson import ObjectId
from starlette.testclient import TestClient


def build_vehicle_data() -> dict:
    """构建车辆数据"""
    fake = faker.Faker("zh_CN")
    return {
        "vehicle_name": f"测试车辆_{fake.license_plate()}",
        "vehicle_type": 1,
        "description": fake.sentence(),
    }


def build_metadata() -> dict:
    """构建元数据"""
    fake = faker.Faker("zh_CN")
    key = f"test_key_{fake.word()}"
    return {"key": key, "value": fake.sentence(), "description": f"测试描述 {fake.sentence()}", "type": "string"}


def build_camera_data(camera_type: int = 1) -> list[dict]:
    """构建摄像头数据"""
    fake = faker.Faker("zh_CN")
    cam_list = []
    for i in range(8):
        cam_list.append(
            {
                "id": f"VIDEO{i}",
                "type": "MVG2CB-001D",
                "camera_id": f"{fake.word()}",
                "camera_name": f"{fake.word()}",
                "format": "UYVY",
                "is_enable": True,
            }
        )
    return cam_list


def build_ip_camera_data() -> list[dict]:
    """构建IP摄像头数据"""
    fake = faker.Faker("zh_CN")
    cam_list = []
    for i in range(3):
        cam_list.append(
            {
                "id": f"NETWORK{i}",
                "camera_id": f"{fake.word()}",
                "camera_name": f"{fake.word()}",
                "uri": f"rtsp://{fake.word()}",
                "width": 1280,
                "height": 720,
                "format": "h264",
                "is_enable": True,
            }
        )
    return cam_list


def assert_response_success(res):
    """断言响应成功"""
    res_json = res.json()
    assert res_json["code"] == 1000
    return res_json


def test_vehicle_crud(client: TestClient, admin_header, mdb):
    """测试车辆增删改查"""
    # 创建车辆
    vehicle_data = build_vehicle_data()
    res = client.post("/vehicles", json=vehicle_data, headers=admin_header)
    res_json = assert_response_success(res)
    vehicle_id = str(res_json["data"]["id"])

    # 查询车辆列表
    res = client.get("/vehicles", params={"page": 1, "page_size": 10}, headers=admin_header)
    res_json = assert_response_success(res)
    vehicle_list = res_json["data"]["lists"]
    is_found = False
    for vehicle in vehicle_list:
        if vehicle["vehicle_name"] == vehicle_data["vehicle_name"]:
            is_found = True
            break
    assert is_found

    # 获取单个车辆详情
    res = client.get(f"/vehicles/{vehicle_id}", headers=admin_header)
    res_json = assert_response_success(res)
    assert res_json["data"]["vehicle_name"] == vehicle_data["vehicle_name"]
    assert res_json["data"]["vehicle_type"] == vehicle_data["vehicle_type"]

    # 更新车辆
    fake = faker.Faker("zh_CN")
    new_name = f"更新车辆_{fake.license_plate()}"
    update_data = vehicle_data.copy()
    update_data["vehicle_name"] = new_name
    res = client.put(f"/vehicles/{vehicle_id}", content=json.dumps(update_data), headers=admin_header)
    assert_response_success(res)

    # 验证更新成功
    res = client.get(f"/vehicles/{vehicle_id}", headers=admin_header)
    res_json = assert_response_success(res)
    assert res_json["data"]["vehicle_name"] == new_name

    # 删除车辆
    res = client.delete(f"/vehicles/{vehicle_id}", headers=admin_header)
    assert_response_success(res)

    # 验证删除成功
    assert mdb["vehicles"].find_one({"_id": ObjectId(vehicle_id)}) is None


def test_vehicle_metadata_curd(client: TestClient, admin_header, mdb):
    """测试车辆参数相关接口"""
    # 创建一个测试车辆
    vehicle_data = build_vehicle_data()
    res = client.post("/vehicles", json=vehicle_data, headers=admin_header)
    res_json = assert_response_success(res)
    vehicle_id = str(res_json["data"]["id"])
    try:
        # 测试获取车辆元数据参数
        res = client.get(f"/vehicles/{vehicle_id}/parameter/metadata", headers=admin_header)
        assert_response_success(res)

        # 测试设置车辆元数据参数
        metadata = build_metadata()
        res = client.post(f"/vehicles/{vehicle_id}/parameter/metadata", json=metadata, headers=admin_header)
        assert_response_success(res)

        # 测试获取单个元数据
        res = client.get(f"/vehicles/{vehicle_id}/parameter/metadata/{metadata['key']}", headers=admin_header)
        assert_response_success(res)

        # 测试更新元数据
        metadata["value"] = "更新后的值"
        res = client.put(f"/vehicles/{vehicle_id}/parameter/metadata", json=metadata, headers=admin_header)
        assert_response_success(res)

        # 测试删除元数据
        res = client.delete(f"/vehicles/{vehicle_id}/parameter/metadata/{metadata['key']}", headers=admin_header)
        assert_response_success(res)

    finally:
        # 清理：删除测试车辆
        client.delete(f"/vehicles/{vehicle_id}", headers=admin_header)


def test_vehicle_program_node(client: TestClient, admin_header, mdb):
    """测试车辆程序节点相关接口"""
    # 创建一个测试车辆
    vehicle_data = build_vehicle_data()
    res = client.post("/vehicles", json=vehicle_data, headers=admin_header)
    res_json = assert_response_success(res)
    vehicle_id = str(res_json["data"]["id"])
    try:
        # 测试获取车辆程序节点列表
        res = client.get(f"/vehicles/{vehicle_id}/parameter/program_node", headers=admin_header)
        assert_response_success(res)

        # 测试增加车辆程序节点
        node_data = {
            "package_name": "test_package",
            "node_name": "test_node",
            "node_type": "test_type",
            "auto_start": 1,
            "params": {"test_param": "test_value"},
        }
        res = client.post(f"/vehicles/{vehicle_id}/parameter/program_node", json=node_data, headers=admin_header)
        assert_response_success(res)

        # 测试更新车辆程序节点
        node_data["params"]["test_param"] = "updated_value"
        res = client.put(f"/vehicles/{vehicle_id}/parameter/program_node", json=node_data, headers=admin_header)
        assert_response_success(res)

        # 测试删除车辆程序节点
        res = client.delete(
            f"/vehicles/{vehicle_id}/parameter/program_node?node_name={node_data['node_name']}", headers=admin_header
        )
        assert_response_success(res)
    finally:
        # 清理：删除测试车辆
        client.delete(f"/vehicles/{vehicle_id}", headers=admin_header)


def test_vehicle_can(client: TestClient, admin_header, mdb):
    """测试车辆CAN相关接口"""
    # 创建一个测试车辆
    vehicle_data = build_vehicle_data()
    res = client.post("/vehicles", json=vehicle_data, headers=admin_header)
    res_json = assert_response_success(res)
    vehicle_id = str(res_json["data"]["id"])
    try:
        # 测试获取车辆CAN列表
        res = client.get(f"/vehicles/{vehicle_id}/parameter/can", headers=admin_header)
        assert_response_success(res)

        # 测试更新车辆CAN配置
        can_data = [{"id": "CAN0", "bitrate": 500000, "ext_fd": "", "is_enable": True}]
        res = client.put(f"/vehicles/{vehicle_id}/parameter/can", json=can_data, headers=admin_header)
        assert_response_success(res)

        # 测试获取更新后的车辆CAN列表
        res = client.get(f"/vehicles/{vehicle_id}/parameter/can", headers=admin_header)
        assert_response_success(res)
        can_list = res.json()["data"]
        assert can_list["lists"] == can_data

    finally:
        # 清理：删除测试车辆
        client.delete(f"/vehicles/{vehicle_id}", headers=admin_header)


def test_vehicle_gmsl_cameras(client: TestClient, admin_header, mdb):
    """测试车辆摄像头相关接口"""
    # 创建一个测试车辆
    vehicle_data = build_vehicle_data()
    res = client.post("/vehicles", json=vehicle_data, headers=admin_header)
    res_json = assert_response_success(res)
    vehicle_id = str(res_json["data"]["id"])
    try:
        # 测试获取车辆GMSL摄像头列表
        res = client.get(f"/vehicles/{vehicle_id}/parameter/cameras?camera_type=1", headers=admin_header)
        assert_response_success(res)

        # 测试更新车辆GMSL摄像头配置
        gmsl_data = build_camera_data()
        res = client.put(
            f"/vehicles/{vehicle_id}/parameter/cameras?camera_type=1", json=gmsl_data, headers=admin_header
        )
        assert_response_success(res)

        # 测试获取更新后的车辆GMSL摄像头列表
        res = client.get(f"/vehicles/{vehicle_id}/parameter/cameras?camera_type=1", headers=admin_header)
        assert_response_success(res)
        gmsl_list = res.json()["data"]
        assert gmsl_list["lists"] == gmsl_data

    finally:
        # 清理：删除测试车辆
        client.delete(f"/vehicles/{vehicle_id}", headers=admin_header)


def test_vehicle_ip_cameras(client: TestClient, admin_header, mdb):
    """测试车辆IP摄像头相关接口"""
    # 创建一个测试车辆
    vehicle_data = build_vehicle_data()
    res = client.post("/vehicles", json=vehicle_data, headers=admin_header)
    res_json = assert_response_success(res)
    vehicle_id = str(res_json["data"]["id"])
    try:
        # 测试获取车辆IP摄像头列表
        res = client.get(f"/vehicles/{vehicle_id}/parameter/cameras?camera_type=2", headers=admin_header)
        assert_response_success(res)

        # 测试更新车辆IP摄像头配置
        ip_data = build_ip_camera_data()
        res = client.put(f"/vehicles/{vehicle_id}/parameter/cameras?camera_type=2", json=ip_data, headers=admin_header)
        assert_response_success(res)

        # 测试获取更新后的车辆IP摄像头列表
        res = client.get(f"/vehicles/{vehicle_id}/parameter/cameras?camera_type=2", headers=admin_header)
        assert_response_success(res)
        ip_list = res.json()["data"]
        print(ip_list["lists"])
        print(ip_data)
        assert ip_list["lists"] == ip_data

    finally:
        # 清理：删除测试车辆
        client.delete(f"/vehicles/{vehicle_id}", headers=admin_header)


# def test_vehicle_third_party(client: TestClient, admin_header):
#     """测试车辆三方服务接口"""
#     # 创建车辆
#     vehicle_data = build_vehicle_data()
#     res = client.post("/vehicles", json=vehicle_data, headers=admin_header)
#     res_json = assert_response_success(res)
#     vehicle_id = str(res_json["data"]["id"])

#     try:
#         # 测试天津港 volvo 车辆状态查询
#         res = client.get(f"/vehicles/{vehicle_id}/3party/tj_volvo_status", headers=admin_header)
#         # 这个接口可能需要特定配置，所以可能会失败，我们只检查状态码
#         assert res.status_code in [200, 404, 400]

#         # 测试天津港 volvo 云平台的车辆状态回调
#         volvo_data = {
#             "vehicle_id": "test_volvo_id",
#             "status": 1,
#             "battery": 80,
#             "position": {"x": 100, "y": 200},
#             "timestamp": 1625097600,
#         }
#         res = client.post(f"/vehicles/{vehicle_id}/3party/tj_volvo_status", json=volvo_data, headers=admin_header)
#         # 这个接口可能需要特定配置，所以可能会失败，我们只检查状态码
#         assert res.status_code in [200, 404, 400]
#     finally:
#         # 清理：删除车辆
#         client.delete(f"/vehicles/{vehicle_id}", headers=admin_header)
