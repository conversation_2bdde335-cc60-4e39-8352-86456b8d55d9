import os
from datetime import datetime
from typing import Optional, List

import arrow
from pydantic import BaseModel, Field

from .common import PagingModel, ObjectIdStr, SortFlag
from apps.config import get_settings


class TimeRangeQuery(BaseModel):
    """时间段条件查询"""

    start_time: datetime
    end_time: datetime


class RecordVideoTask(BaseModel):
    """单个车辆的视频录制任务"""

    vehicle_id: str
    rtsp_uri_list: List[str] = []
    segment_time: int = 300
    save_days: int = 30


class RecordMqttTask(BaseModel):
    """单个车辆的 Mqtt 信令录制任务"""

    vehicle_id: str
    topic_name_list: list[str] = []


class VideoQuery(PagingModel):
    vehicle_id: Optional[ObjectIdStr] = None
    stream_name: Optional[str] = None
    time_order: SortFlag = SortFlag.DESC
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class VideoSegment(BaseModel):
    id: ObjectIdStr = Field(..., alias="_id")
    vehicle_id: ObjectIdStr
    stream_name: str
    segment_start_time: datetime
    path: str = Field(exclude=True)
    size: int

    @property
    def abs_path(self) -> str:
        return os.path.join(get_settings().video_directory, self.path)

    @property
    def cover_abs_path(self) -> str:
        _path = self.path.replace(self.stream_name, f"{self.stream_name}/cover")
        _path = _path.replace(".mp4", ".jpg")
        return os.path.join(get_settings().video_directory, _path)

    @property
    def exists(self) -> bool:
        return os.path.exists(self.abs_path)

    @property
    def cover_exists(self) -> bool:
        return os.path.exists(self.cover_abs_path)

    def iterfile(self, start: int, end: int):
        with open(self.abs_path, mode="rb") as file:
            file.seek(start)
            while start <= end:
                bytes_to_read = min(1024 * 1024, end - start + 1)
                data = file.read(bytes_to_read)
                if not data:
                    break
                start += len(data)
                yield data
