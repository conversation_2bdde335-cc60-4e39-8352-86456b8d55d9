<template>
  <div class="admin">
    <el-card class="!border-none" shadow="never">
      <el-form class="mb-[-16px]" :model="formData" inline>
        <el-form-item label="作业指导名称">
          <el-input v-model="formData.id" class="w-[180px]" clearable @keyup.enter="resetPage" />
        </el-form-item>
        <el-form-item>
          <!-- resetPage -->
          <el-button type="primary" @click="">查询</el-button>
          <el-button @click="resetParams">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card v-loading="pager.loading" class="mt-4 !border-none" shadow="never">
      <div>
        <el-button type="primary" @click="handleShowAdd">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          新建作业指导
        </el-button>
      </div>
      <div class="mt-4">
        <el-table :data="pager.lists" size="large" :height="calcTableHeight()">
          <el-table-column label="作业指导名称" prop="name" />
          <el-table-column label="描述" prop="desc" />
          <el-table-column label="创建时间" prop="create_time">
            <template #default="{ row }">
              <span>{{ dayjs(row.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleDetail(row)"> 详情 </el-button>
              <el-button link type="primary" @click="handleShowEdit(row)"> 编辑 </el-button>
              <el-button link type="danger" @click="handleDelete(row.id)"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>

    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>

<script lang="ts" setup name="admin">
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import EditPopup from "./edit.vue";
import { guideList, guideDelete } from "@/api/guide-man";

const editRef = shallowRef<InstanceType<typeof EditPopup>>();

const router = useRouter();
// 表单数据
const formData = reactive({
  id: "",
});
const showEdit = ref(false);

const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: guideList,
  params: formData,
});

const calcTableHeight = () => {
  return window.innerHeight - 91 - 16 * 5 - 74 - 20 * 2 - 32 * 1;
};

const handleDelete = async (id: number) => {
  await feedback.confirm("确定要删除？");
  await guideDelete({ id });
  feedback.msgSuccess("删除成功");
  getLists();
};

const handleDetail = async (row: any) => {
  router.push({ path: "/detail/guide", query: { id: row.id } });
};

const handleShowAdd = async (index: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open();
};

const handleShowEdit = async (data: any, index: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(data);
};

onMounted(() => {
  getLists();
});
</script>
<style lang="scss" scoped>
.text_hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
</style>
