import time
from dataclasses import dataclass
from typing import Dict

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import Receive

from apps.db.mongo_db import MongoDB
from apps.models.user import CacheInfo
from apps.models.audit import Record


__all__ = ["AuditMiddleware"]


@dataclass
class ReceiveProxy:
    """Proxy to starlette.types.Receive.__call__ with caching first receive call."""

    receive: Receive
    cached_body: bytes
    _is_first_call: bool = True

    async def __call__(self):
        # First call will be for getting request body => returns cached result
        if self._is_first_call:
            self._is_first_call = False
            return {"type": "http.request", "body": self.cached_body, "more_body": False}

        return await self.receive()


class AuditMiddleware(BaseHTTPMiddleware):
    coll = MongoDB.get_collection("audit")
    sensitive_msg = "sensitive msg"
    sensitive_msg_url = (
        "/api/v1/user/login",
        "/api/v1/user/auth",
        "/api/v1/user/me/reset_password",
        "/api/v2/user/login",
        "/api/v2/user/auth",
        "/api/v2/user/me/reset_password",
    )
    record_method = ("POST", "PUT", "DELETE")

    def __init__(self, app):
        super().__init__(app)

    async def dispatch(self, request, call_next):
        start_time = time.perf_counter()
        if request.method in self.record_method:
            # 记录日志
            body_str = await self.get_request_body(request)
            response = await call_next(request)
            await self.record_action(request, body_str)
        else:
            # 不记录日志
            response = await call_next(request)

        process_time = time.perf_counter() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response

    async def get_request_body(self, request: Request) -> str:
        body_bytes = await request.body()
        request._receive = ReceiveProxy(receive=request.receive, cached_body=body_bytes)
        return body_bytes.decode("utf-8")

    async def record_action(self, request: Request, body_str: str):
        # 过滤敏感信息
        if request.url.path.startswith(self.sensitive_msg_url):
            body_str = '{"msg": "sensitive msg"}'

        try:
            user_info: CacheInfo = request.state.user
            track_info: Dict = request.state.track_info
        except AttributeError:
            return

        try:
            client_ip = request.headers["x-real-ip"]
        except KeyError:
            if request.client is not None:
                client_ip = request.client.host

        record = Record(
            ip=client_ip,
            client=request.headers.get("User-Agent", ""),
            path=request.url.path,
            method=request.method,
            user_id=user_info.id,
            code=track_info["code"],
            msg=track_info["msg"],
            body=body_str,
            params=f"{request.query_params}",
        )
        await self.coll.insert_one(record.model_dump())
