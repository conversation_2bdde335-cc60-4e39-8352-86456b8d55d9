<template>
  <el-card class="!border-none" shadow="never">
    <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" :inline="true">
      <el-form-item class="w-[300px]" label="设备选择">
        <el-select  v-model="queryParams.deviceId" placeholder="请选择设备" class="w-[180px]">
          <el-option
            v-for="item in deviceData"
            :key="item.device_id"
            :label="item.device_name"
            :value="item.device_id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          class="grow-0 !w-[330px]"
          v-model="queryParams.dateValue"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="play">查询</el-button>
        <el-button @click="play">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
  <el-card class="!border-none mt-4" shadow="never">
    <Map ref="mapRef" />
  </el-card>
</template>

<script setup lang="ts">
import Map from "./map.vue";
import { deviceList } from "@/api/device";
import feedback from "@/utils/feedback";

const queryParams = reactive({
  deviceId: "",
  dateValue: "",
});
const mapRef = ref<any>(null);

const play = () => {
  console.log(mapRef.value);

  if (mapRef.value) {
    mapRef.value.play();
  }
};

type DeviceData = {
  device_id: string;
  device_name: string;
};
const deviceData = ref<DeviceData[]>([]);

const handleGetDeviceLists = async () => {
  const data = await deviceList({ status: 1 });
  deviceData.value = data.lists;
};
handleGetDeviceLists();
</script>

<style scoped lang="scss"></style>
