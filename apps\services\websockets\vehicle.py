"""
WebSocket 服务
"""

import time
import json
from typing import <PERSON>ple

import anyio
from bson import ObjectId
from fastapi import WebSocket

import apps.models.cache as Cache
import apps.models.websocket as WSModel
import apps.services.vehicles as VehicleService
from apps.services.common import get_header_info
from .base import WebSockClient


class VehicleSockClient(WebSockClient):
    """设备端客户端链接主要处理车端设备的链接"""

    def __init__(self, wsock: WebSocket, device_id: str, vehicle_id: str):
        super().__init__(wsock)
        self.id = vehicle_id
        self.device_id = device_id
        self.room_id = f"room.{vehicle_id}"

        self.dev_cache = Cache.VehicleDevice(device_id)
        self.cache = Cache.Vehicle(vehicle_id)

    async def init(self):
        """设置连接上的时间"""
        await self.wsock.accept()
        await self.cache.status.online_status.set(1, ex=30)
        await self.cache.status.online_time.lpush(time.time())

    async def clean(self):
        self.rooms.clear()
        await self.cache.status.online_status.delete()
        await self.cache.status.offline_time.lpush(time.time())

    async def process_msg(self):
        """处理消息，房间消息和客户端消息"""
        async with anyio.create_task_group() as tg:
            # 订阅房间消息
            tg.start_soon(self.sub_room_msg, self.id)
            tg.start_soon(self.sub_room_msg, self.room_id)
            # 处理客户端消息
            async for msg in self.recv_socket_msg():
                # 更新在线状态
                await self.cache.status.online_status.set(1, ex=30)
                # 需要发送到房间的消息
                if msg.room_id:
                    msg.user = self.device_id
                    msg.room_id = self.room_id
                    await self.send_room_msg(msg)
                    continue
                if msg.cmd == "ping":
                    await self.send_pong_msg(msg)
                if msg.cmd == "status.ros_node":
                    pass
                elif msg.cmd == "status.vehicle.system":
                    pass
                elif msg.cmd == "sync.check":
                    await self.sync_data_check(msg.data)
                elif msg.cmd == "alarm.ros":
                    print("alarm message: ", msg.data)
                elif msg.cmd == "alarm.sys":
                    print("alarm message: ", msg.data)
                else:
                    print(f"unknown cmd: {msg.cmd}")
            # 关闭任务组
            tg.cancel_scope.cancel()

    async def send_room_msg(self, msg: WSModel.MsgModel):
        """发送消息到房间, 只能发送消息到本车辆房间"""
        msg.user = self.device_id
        msg.room_id = self.room_id
        await self.broadcast_room_msg(msg.room_id, msg)

    async def process_room_msg(self, msg: WSModel.MsgModel):
        """处理房间消息, sub_room_msg 任务组中收到消息会调用此方法"""
        if msg.user == self.device_id or msg.user == "data_manager":
            # 过滤自己，数据管理模块，发送的消息
            return
        # 其他消息转发到客户端
        await self.send_msg(msg.model_dump_json())

    async def sync_data_check(self, data):
        """同步检查, 对比所有数据，不同步则更新标志"""
        server_run_params_ = await VehicleService.get_run_params(ObjectId(self.id))
        assert server_run_params_, "device is not register"
        sync_code = WSModel.DeviceDataSyncCode.synced.value
        try:
            server_run_params = WSModel.DeviceRunData(**server_run_params_)
            device_run_params = WSModel.DeviceRunData(**data)
            result = server_run_params.compare(device_run_params)
            if all(result.values()):
                print("device data is syncd")
            else:
                sync_code = WSModel.DeviceDataSyncCode.nosync.value
                await self.cache.status.params_sync_detail.set(json.dumps(result))
        except Exception as e:  # pylint: disable=broad-except
            print(f"device data is not sync: {e}")
            sync_code = WSModel.DeviceDataSyncCode.nosync.value

        await self.cache.status.params_sync_status.set(sync_code)


async def vehicle_device_isregistered(wsock: WebSocket, device_id: str) -> Tuple[bool, str]:
    """是否已经注册"""

    # 缓存设备信息
    sys_info = get_header_info(wsock)
    if sys_info:
        dev_cache = Cache.VehicleDevice(device_id)
        await dev_cache.status.hsetall(sys_info)

    # 检查设备是否已经注册
    un_register_set = Cache.VehicleDevice.un_register_set
    index_ = await un_register_set.zindex(device_id)
    if index_ is not None:
        return False, ""

    # 检查设备是否已经注册
    result = await VehicleService.find_by_device_id(device_id)
    if result is None:
        await un_register_set.zadd(device_id, int(time.time()))
        return False, ""

    # 缓存车辆Key信息
    vehicle_id = str(result["_id"])
    assert vehicle_id, "vehicle_id is None"
    return True, vehicle_id


async def process_vehicle(device_id: str, wsock: WebSocket):
    """处理车辆设备连接, 通过设备ID查询车辆ID"""
    is_ok, vehicle_id = await vehicle_device_isregistered(wsock, device_id)
    if not is_ok:
        print("device is not register")
        return

    wsock.state.client_type = "vehicle"
    wsock.state.client_id = device_id

    wc = VehicleSockClient(wsock, device_id, vehicle_id)
    try:
        await wc.init()
        await wc.process_msg()
    except Exception as e:  # pylint: disable=broad-except
        print(e)
    finally:
        await wc.clean()
    del wc
