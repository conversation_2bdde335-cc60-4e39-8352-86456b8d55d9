<template>
  <div class="w-full h-full mx-0 my-auto" ref="chartRef"></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import geoCoordMap from "@/views/workbench/area.json";

import { mapOptions } from "./options";
import { onMounted, onUnmounted, ref } from "vue";

const chartRef = ref();
const chart = ref<any>();

onMounted(() => {
  setTimeout(() => {
    chart.value = echarts.init(chartRef.value);
    echarts.registerMap("china", geoCoordMap as any);
    chart.value.setOption(mapOptions);
  }, 100);
});

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
});
</script>
<style scoped lang="scss"></style>
