<template>
  <div class="admin">
    <el-card class="!border-none" shadow="never">
      <el-form class="mb-[-16px]" :model="formData" inline>
        <el-form-item :label="$t('common.设备ID')">
          <el-input v-model="formData.id" class="w-[180px]" clearable @keyup.enter="resetPage" />
        </el-form-item>
        <!-- <el-form-item label="人员名称">
          <el-input v-model="formData.nickname" class="w-[180px]" clearable @keyup.enter="resetPage" />
        </el-form-item> -->
        <el-form-item>
          <!-- resetPage -->
          <el-button type="primary" @click="">{{ $t("vehicle.查询") }}</el-button>
          <el-button @click="resetParams">{{ $t("vehicle.重置") }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card v-loading="pager.loading" class="mt-4 !border-none" shadow="never">
      <div class="mt-4">
        <el-table :data="pager.lists" size="large" :height="calcTableHeight()">
          <el-table-column :label="$t('common.设备ID')" prop="device_id" />
          <el-table-column :label="$t('common.设备名称')" prop="device_name" />
          <el-table-column :label="$t('common.设备IP')" prop="ip" />
          <el-table-column :label="$t('common.私网IP')" prop="local_ip">
            <template #default="{ row }">
              <span class="text_hidden" v-if="row.local_ip" v-for="item in row.local_ip.split(',')">{{ item }}</span>
            </template>
          </el-table-column>
          <el-table-column label="请求时间" prop="online_time">
            <template #default="{ row }">
              <span>{{ row.online_time ? timeFormat(row.online_time, "yyyy-mm-dd hh:MM:ss") : "--" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="持续时长" prop="uptime" />
          <el-table-column align="center" :label="$t('common.操作')" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" @click="handleEdit(row)">{{ $t("common.确认连接") }}</el-button>
              <!-- <el-button type="danger" @click="handleDelete(row.id)"> 删除 </el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex mt-4 justify-end">
        <pagination v-model="pager" @change="getLists" />
      </div>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>

<script lang="ts" setup name="admin">
import { unRegisterDeviceList } from "@/api/device";
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import EditPopup from "./confirm.vue";
import { timeFormat } from "@/utils/util";

const router = useRouter();
const editRef = shallowRef<InstanceType<typeof EditPopup>>();
// 表单数据
const formData = reactive({
  id: "",
});
const showEdit = ref(false);
const { pager, getLists, resetParams, resetPage } = usePaging({
  fetchFun: unRegisterDeviceList,
  params: formData,
});

const calcTableHeight = () => {
  return window.innerHeight - 270;
};

const handleEdit = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open();
  editRef.value?.setFormData(data);
};

const handleJump = async () => {
  router.push({ path: "/device" });
};

const handleDelete = async (id: number) => {
  await feedback.confirm("确定要删除？");
  // await Delete({ id });
  feedback.msgSuccess("删除成功");
  getLists();
};

onMounted(() => {
  getLists();
});
</script>
<style lang="scss" scoped>
.text_hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
</style>
