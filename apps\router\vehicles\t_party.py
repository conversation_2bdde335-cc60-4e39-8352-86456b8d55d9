"""
平台用于三方服务回调接口部分
"""

from fastapi import APIRouter, Request

from apps.common import permission_dp, unified_resp
from apps.models.common import ObjectIdStr
import apps.services.vehicles as VService
import apps.models.vehicle as VModels
from apps.models.permissions import FuncId, RoleResourceFlag


router = APIRouter(prefix="/vehicles/{vehicle_id}/3party", tags=["三方服务"])


@router.get("/tj_volvo_status", dependencies=permission_dp(FuncId.VehicleTpartyProbe, RoleResourceFlag.VIEWER))
@unified_resp
async def tianjin_volvo_info(_: Request, vehicle_id: ObjectIdStr):
    """天津港 volvo 车辆状态查询"""
    return await VService.tianjin_volvo_cloud_get(vehicle_id)


@router.post("/tj_volvo_status", dependencies=permission_dp(FuncId.VehicleTpartyProbe, RoleResourceFlag.VIEWER))
@unified_resp
async def tianjin_volvo_recv(_: Request, vehicle_id: ObjectIdStr, data: VModels.TJVolvoState):
    """天津港 volvo 云平台的车辆状态回调"""
    return await VService.tianjin_volvo_cloud_call(vehicle_id, data)
