/**
 * @description: default script
 * @param {any} value - Payload
 * @param {string} msgType - Message type, value is 'received' or 'publish'
 * @param {number} index - Index of the message, valid only when script is used in the publish message and timed message is enabled
 * @return {any} - Payload after script processing
 */

function Str2Bytes(str) {
  if (str.length <= 0) {
    return;
  }
  if (str.length % 2 != 0) {
    str = "0" + str;
  }
  var pos = 0;
  var len = str.length;
  len /= 2;
  var hex_data = new Array();
  for (var i = 0; i < len; i++) {
    var s = str.substr(pos, 2);
    var v = parseInt(s, 16);
    hex_data.push(v);
    pos += 2;
  }
  return hex_data;
}

const parseObj = {
  head: [
    { key: "", label: "特征位固定值", type: "", len: 2, isShow: true },
    { key: "", label: "传输协议版本", type: "", len: 1, isShow: true },
    { key: "", label: "车辆类型标识", type: "", len: 1, isShow: true },
    { key: "", label: "LEN=20 + N", type: "", len: 2, isShow: true },
    { key: "", label: "目标地址", type: "", len: 1, isShow: true },
    { key: "", label: "源地址", type: "", len: 1, isShow: true },
    { key: "", label: "传输计数", type: "", len: 1, isShow: true },
    { key: "", label: "数据单元ID个数", type: "", len: 1, isShow: true },
    { key: "", label: "数据单元长度", type: "", len: 2, isShow: true },
    { key: "", label: "时间戳(毫秒级)", type: "time", len: 8, isShow: true },
    { key: "", label: "保留", type: "", len: 0, isShow: true },
  ],
  a2r: [
    { key: "", label: "ID=8", type: "", len: 2, isShow: true },
    { key: "", label: "LEN=56(700吨后64)+4", type: "", len: 2, isShow: true },
    { key: "", label: "紧急制动", type: "", len: 1, isShow: true },
    { key: "", label: "操作台急停", type: "", len: 1, isShow: true },
    { key: "", label: "保留", type: "", len: 1, isShow: true },
    { key: "", label: "保留", type: "", len: 1, isShow: true },
    { key: "", label: "左手柄X轴", type: "", len: 4, isShow: true },
    { key: "", label: "左手柄Y轴", type: "", len: 4, isShow: true },
    { key: "", label: "左手柄Z轴或KX轴", type: "", len: 4, isShow: true },
    { key: "", label: "左手柄KY轴", type: "", len: 4, isShow: true },
    { key: "", label: "左手柄按钮", type: "", len: 2, isShow: true },
    { key: "", label: "右手柄X轴", type: "", len: 4, isShow: true },
    { key: "", label: "右手柄Y轴", type: "", len: 4, isShow: true },
    { key: "", label: "右手柄Z轴或KX轴", type: "", len: 4, isShow: true },
    { key: "", label: "右手柄KY轴", type: "", len: 4, isShow: true },
    { key: "", label: "右手柄按钮", type: "", len: 2, isShow: true },
    { key: "", label: "左踏板", type: "", len: 4, isShow: true },
    { key: "", label: "右踏板", type: "", len: 4, isShow: true },
    { key: "", label: "功能按钮", type: "", len: 8, isShow: true },
    { key: "", label: "模拟左脚踏", type: "", len: 4, isShow: true },
    { key: "", label: "模拟右脚踏", type: "", len: 4, isShow: true },
  ],
  r2a: [
    { key: "", label: "ID=16384", type: "", len: 2, isShow: true },
    { key: "", label: "LEN", type: "", len: 2, isShow: true },
    { key: "", label: "紧急制动状态", type: "", len: 1, isShow: true },
    { key: "", label: "操作台急停状态", type: "", len: 1, isShow: true },
    { key: "", label: "遥控使能状态", type: "", len: 1, isShow: true },
    { key: "", label: "本地远程急停状态", type: "", len: 1, isShow: true },
    { key: "", label: "功能按钮状态", type: "", len: 4, isShow: true },
    { key: "", label: "发动机转速", type: "", len: 4, isShow: true },
    { key: "", label: "燃油液位", type: "", len: 4, isShow: true },
    { key: "", label: "机油压力", type: "", len: 4, isShow: true },
    { key: "", label: "电池电压", type: "", len: 4, isShow: true },
    { key: "", label: "水温", type: "", len: 4, isShow: true },
    { key: "", label: "油温", type: "", len: 4, isShow: true },
    { key: "", label: "燃油输送压力", type: "", len: 4, isShow: true },
    { key: "", label: "冷却剂液位", type: "", len: 4, isShow: true },
    { key: "", label: "驾驶室温度", type: "", len: 4, isShow: true },
    { key: "", label: "环境气温", type: "", len: 4, isShow: true },
    { key: "", label: "液压油温度", type: "", len: 4, isShow: true },
  ],
};

function getTimestampFromBytes(byteArray) {
  if (!byteArray || byteArray.length !== 8) {
    throw new Error("Invalid byte array for timestamp conversion");
  }
  const bigEndianBytes = byteArray.reverse();
  let timestamp = 0;
  for (let i = 0; i < bigEndianBytes.length; i++) {
    timestamp |= bigEndianBytes[i] << (24 - i * 8);
  }
  const millisecondTimestamp = timestamp * 1000;
  const formattedDate = new Date(millisecondTimestamp).toLocaleString();
  return formattedDate;
}

function Bytes2Number(bytes) {
  let number = 0;
  for (let i = 0; i < bytes.length; i++) {
    number = (number << 8) | bytes[i];
  }
  return number;
}

function parseBytes(data) {
  let sliceIndex = 0;

  const retainLen = data.slice(5, 6)[0];
  parseObj.head[parseObj.head.length - 1]["len"] = retainLen;

  let getTopicType = data.slice(20 + retainLen, 20 + retainLen + 2);
  console.log(getTopicType);
  console.log("-------------");

  // TODO: 主题判断，根据LEN=20 + N定义head长度
  let topicType = "r2a";
  if (getTopicType === "384") {
    topicType = "r2a";
  } else if (getTopicType === "2048") {
    topicType = "a2r";
  }
  let headAndBody = parseObj.head.concat(parseObj[topicType]);

  console.log(headAndBody);

  return headAndBody.reduce((pre, cur) => {
    if (cur.isShow) {
      // TODO: 显示类型判断，开关，数值，枚举值等 if (cur.type) {  }
      if (cur.type === "time") {
        pre[cur.label] = getTimestampFromBytes(data.slice(sliceIndex, sliceIndex + cur.len));
      } else {
        pre[cur.label] = data.slice(sliceIndex, sliceIndex + cur.len);
        // pre[cur.label] = Bytes2Number(data.slice(sliceIndex, sliceIndex + cur.len));
      }
      console.log(cur.label, pre[cur.label]);
    }
    sliceIndex += cur.len;
    return pre;
  }, {});
}

function formatBytes(data) {
  const lineShow = 2; // 每行显示两条数据，可以手动修改
  const dataArray = Object.entries(data).map(([key, value]) => ({ [key]: value }));
  const chunkedArray = [];
  for (let i = 0; i < dataArray.length; i += lineShow) {
    chunkedArray.push(dataArray.slice(i, i + lineShow));
  }
  const jsonString = chunkedArray.map((pair) => JSON.stringify(pair)).join("\n");
  return jsonString;
}

function handlePayload(value, msgType, index) {
  var new_value = value.replace(/\s*/g, "");
  var data = Str2Bytes(new_value);
  console.log(data);
  let id = data.map((item, index) => {
    return `${index + 1}-${item}`;
  });
  console.log(id);
  // console.log("数据长度", data.length);
  return formatBytes(parseBytes(data));
}

// execute(handlePayload) mqttx客户端

let req =
  "0c10 2201 1400 0281 4503 4400 d74a 3f66 0000 0000 0040 1000 0200 0200 0011 0000 0000 0000 0020 0600 0002 0180 2e00 0101 0100 6500 5c75 c33b f416 7b3b 1746 7ebf 3439 50bc 98bc 1bbd 407c dd3c 5238 80be e5a0 d03e 89ca ab43";

// a2r
// let req =
//   "0c10 2201 1400 8102 fe01 3800 b43b 369f 8f01 0000 0800 4400 0001 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 1000 0000 0000 0000 0000 0111 0000 0000 0008 0000 0000 0000 0000 ";
console.log(handlePayload(req));
