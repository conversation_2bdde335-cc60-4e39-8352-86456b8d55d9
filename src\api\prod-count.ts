import request from "@/utils/request";

const API_VERSION = 'v2';

// 获取历史装车记录
export function getHistoryLoad(params: any) {
  return request.get({ url: `/${API_VERSION}/prod_count/history_load`, params });
}

// 获取历史班组记录
export function getHistoryTeam(params: any) {
  return request.get({ url: `/${API_VERSION}/prod_count/history_team`, params });
}


// 获取每日统计数据
export function getDailyStats(params: any) {
  return request.get({ url: `/${API_VERSION}/prod_count/stats/daily`, params });
}

// 获取每月统计数据
export function getMonthlyStats(params: any) {
  return request.get({ url: `/${API_VERSION}/prod_count/stats/monthly`, params });
}