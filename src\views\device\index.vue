<template>
  <div class="post-lists">
    <el-card class="!border-none" shadow="never">
      <el-form
        ref="formRef"
        class="mb-[-16px]"
        :model="queryParams"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item :label="$t('vehicle.车辆名称')">
          <el-input
            class="w-[280px]"
            v-model="queryParams.vehicle_name"
            clearable
            @keyup.enter="resetPage"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="resetPage">{{ $t("vehicle.查询") }}</el-button>
          <el-button @click="resetParams">{{ $t("vehicle.重置") }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="!border-none mt-4 table-card" shadow="never">
      <div>
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          {{ $t("vehicle.添加车辆") }}
        </el-button>
        <el-button @click="handleJump"> {{ $t("vehicle.未连接设备") }} </el-button>
      </div>
      <div>
        <el-table
          class="mt-4 table-warp"
          size="large"
          :height="calcTableHeight()"
          v-loading="pager.loading"
          :data="pager.lists"
          table-layout="auto"
        >
          <el-table-column type="index" width="50" />
          <el-table-column
            align="center"
            :label="$t('vehicle.车辆名称')"
            prop="vehicle_name"
            min-width="100"
          />
          <el-table-column
            align="center"
            :label="$t('vehicle.车辆类型')"
            prop="vehicle_type"
            min-width="110"
          >
            <template #default="{ row }">
              {{ pageData.vehicleTypeList[row.vehicle_type] || "--" }}
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" label="已绑定设备ID" prop="devices">
            <template #default="{ row }">
              <span class="text_hidden" v-if="row.devices" v-for="item in row.devices">{{ item }}</span>
              <span v-if="!row.devices || !row.devices.length">--</span>
            </template>
          </el-table-column> -->

          <el-table-column
            align="center"
            :label="$t('vehicle.状态')"
            prop="is_online"
            min-width="100"
          >
            <template #default="{ row }">
              <div class="flex flex-row justify-center items-center">
                <el-tag v-if="row.is_online" type="success">{{ $t("vehicle.在线") }}</el-tag>
                <el-tag v-else type="info">{{ $t("vehicle.离线") }}</el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" label="是否绑定" prop="is_locked" width="90">
            <template #default="{ row }">
              {{ row.is_locked ? $t("vehicle.是") : $t("vehicle.否") }}
            </template>
          </el-table-column>

          <el-table-column align="center" label="操作台名称" prop="locked_by">
            <template #default="{ row }">
              {{ row.locked_by_name || "--" }}
            </template>
          </el-table-column>

          <el-table-column align="center" :label="$t('vehicle.操作')" fixed="right" width="260">
            <template #default="{ row }">
              <div class="">
                <el-button class="margin-left0" type="primary" link @click="handleRos(row)"
                  >{{ $t("vehicle.车辆详情") }}
                </el-button>
                <el-button
                  v-perms="['vehicle:params:android']"
                  class="margin-left0"
                  type="primary"
                  link
                  @click="handleAndroidParams(row)"
                >
                  {{ $t("vehicle.安卓参数") }}
                </el-button>
                <!-- <el-button class="margin-left0" type="primary" link @click="handleConsole(row)"
                  >{{ $t("vehicle.安卓配置") }}
                </el-button>
                <el-button class="margin-left0" type="primary" link @click="handleHandle(row)">
                  {{ $t("vehicle.手柄死区") }}
                </el-button>-->
                <el-button
                  v-perms="['vehicle:params:handle']"
                  class="margin-left0"
                  type="primary"
                  link
                  @click="handleHandleParams(row)"
                >
                  {{ $t("vehicle.手柄参数") }}
                </el-button>
                <el-button
                  v-perms="['vehicle:params:intelligent']"
                  class="margin-left0"
                  type="primary"
                  link
                  @click="getVehicleIntelligentParams(row)"
                >
                  智能参数
                </el-button>
                <el-button
                  v-perms="['vehicle:edit']"
                  class="margin-left0"
                  type="primary"
                  link
                  @click="handleEdit(row)"
                >
                  {{ $t("vehicle.编辑") }}
                </el-button>
                <el-button
                  v-perms="['vehicle:delete']"
                  class="margin-left0"
                  type="danger"
                  link
                  @click="handleDelete(row)"
                >
                  {{ $t("vehicle.删除") }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-end mt-4">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </div>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
    <JsonEditor
      ref="androidJsonEditorRef"
      title="安卓参数编辑"
      :jsonParams="vehicleDetail.android"
      @success="updateAndroidParams"
    />
    <JsonEditor
      ref="handleJsonEditorRef"
      title="手柄参数编辑"
      :jsonParams="vehicleDetail.handleConfig"
      @success="updateHandleParams"
    />
    <JsonEditor
      ref="intelligentJsonEditorRef"
      title="智能参数编辑"
      :jsonParams="vehicleDetail.intelligent"
      @success="updateIntelligentParams"
    />
  </div>
</template>
<script lang="ts" setup name="post">
import {
  getAndroidParams,
  setAndroidParams,
  vehicleDelete,
  vehiclesDetail,
  vehiclesList,
  vehicleUpdate,
} from "@/api/device";
import useMetadataStore from "@/stores/modules/metadata";
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import EditPopup from "./edit.vue";
import JsonEditor from "@/components/json-editor/index.vue";
import { useI18n } from "vue-i18n";

const router = useRouter();
const metadataStore = useMetadataStore();

const editRef = shallowRef<InstanceType<typeof EditPopup>>();
const handleJsonEditorRef = shallowRef<InstanceType<typeof JsonEditor>>();
const androidJsonEditorRef = shallowRef<InstanceType<typeof JsonEditor>>();
const intelligentJsonEditorRef = shallowRef<InstanceType<typeof JsonEditor>>();

const showEdit = ref(false);
const carId = ref<any>("");
const queryParams = reactive({
  vehicle_name: undefined,
  vehicle_type: undefined,
});
const { pager, getLists, resetPage, resetParams } = usePaging({
  fetchFun: vehiclesList,
  params: queryParams,
});

const vehicleDetail: any = reactive({
  id: "",
  android: {},
  handleConfig: {},
  intelligent: {},
  vehicle_name: "",
  vehicle_type: 1,
  restart_time: 0,
  description: "",
});

const pageData: any = reactive({
  vehicleTypeList: {},
});

const getVehicleTypeList = async () => {
  pageData.vehicleTypeList = await metadataStore.fetchMetadata("VEHICLE_TYPE", "kv");
};

const calcTableHeight = () => {
  return window.innerHeight - 308;
};

const handleJump = async () => {
  await router.push({ path: "/link/car" });
};

const handleAdd = async () => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("add");
};

const handleEdit = async (row: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(row);
};

const handleRos = async (row: any) => {
  router.push({ path: "/vehicle/detail", query: { id: row.id, title: row.vehicle_name } });
};

const handleConsole = async (row: any) => {
  router.push({ path: "/console/detail", query: { id: row.id, title: row.vehicle_name + "安卓" } });
};

const handleHandle = async (row: any) => {
  router.push({ path: "/console/handle", query: { id: row.id, title: row.vehicle_name + "手柄" } });
};

const handleHandleParams = async (row: any) => {
  carId.value = row.id;
  await getVehicleAndroidDetail();
  await nextTick();
  handleJsonEditorRef.value?.open();
};

const handleAndroidParams = async (row: any) => {
  carId.value = row.id;
  await getVehicleAndroidDetail();
  await nextTick();
  androidJsonEditorRef.value?.open();
};

const getVehicleIntelligentParams = async (row: any) => {
  carId.value = row.id;
  await getVehicleAndroidDetail();
  await nextTick();
  intelligentJsonEditorRef.value?.open();
};

const getVehicleAndroidDetail = async () => {
  const res = await getAndroidParams(carId.value);
  Object.assign(vehicleDetail, res);
};

const updateAndroidParams = async (data: any) => {
  let params = {
    android: data.value,
    handleConfig: vehicleDetail.handleConfig,
    intelligent: vehicleDetail.intelligent,
    restart_time: Math.floor(Date.now() / 1000),
  };
  await setAndroidParams({ vehicle_id: carId.value, params });
  feedback.msgSuccess("修改成功");
};

const updateHandleParams = async (data: any) => {
  vehicleDetail.handleConfig = data;
  let params = {
    android: vehicleDetail.android,
    handleConfig: data.value,
    intelligent: vehicleDetail.intelligent,
    restart_time: Math.floor(Date.now() / 1000),
  };
  await setAndroidParams({ vehicle_id: carId.value, params });
  feedback.msgSuccess("修改成功");
};

const updateIntelligentParams = async (data: any) => {
  vehicleDetail.intelligent = data;
  let params = {
    android: vehicleDetail.android,
    handleConfig: vehicleDetail.handleConfig,
    intelligent: data.value,
    restart_time: Math.floor(Date.now() / 1000),
  };
  await setAndroidParams({ vehicle_id: carId.value, params });
  feedback.msgSuccess("修改成功");
};

const handleDelete = async (row: any) => {
  const { id, vehicle_name} = row;
  await feedback.confirm(`确定要删除 ${vehicle_name}？`);
  await vehicleDelete(id);
  feedback.msgSuccess("删除成功");
  getLists();
};

getLists();
getVehicleTypeList();
</script>
<style lang="scss" scoped>
.margin-left0 {
  margin-left: 0 !important;
}
.text_hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
</style>
