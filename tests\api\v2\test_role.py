"""
角色管理相关接口测试
"""

import json
import faker
from starlette.testclient import TestClient


def build_role_data() -> dict:
    """构建角色数据"""
    fake = faker.Faker("zh_CN")
    return {
        "name": f"测试角色_{fake.word()}",
        "remark": fake.sentence(),
        "sort": 100,
    }


def assert_response_success(res):
    """断言响应成功"""
    res_json = res.json()
    assert res_json["code"] == 1000
    return res_json


def test_role_crud(client: TestClient, admin_header, mdb):
    """测试角色增删改查"""
    # 创建角色
    role_data = build_role_data()
    res = client.post("/user/role", json=role_data, headers=admin_header)
    res_json = assert_response_success(res)
    role_id = str(res_json["data"]["id"])

    # 查询角色列表
    res = client.get("/user/role", headers=admin_header)
    res_json = assert_response_success(res)
    role_list = res_json["data"]["lists"]
    is_found = False
    for role in role_list:
        if role["name"] == role_data["name"]:
            is_found = True
            assert role["remark"] == role_data["remark"]
            break
    assert is_found

    # 获取单个角色详情
    res = client.get(f"/user/role/{role_id}", headers=admin_header)
    res_json = assert_response_success(res)
    assert res_json["data"]["name"] == role_data["name"]
    assert res_json["data"]["remark"] == role_data["remark"]

    # 更新角色
    fake = faker.Faker("zh_CN")
    new_name = f"更新角色_{fake.word()}"
    new_description = fake.sentence()
    update_data = {"name": new_name, "remark": new_description}
    res = client.put(f"/user/role/{role_id}", content=json.dumps(update_data), headers=admin_header)
    assert_response_success(res)

    # 验证更新成功
    res = client.get(f"/user/role/{role_id}", headers=admin_header)
    res_json = assert_response_success(res)
    assert res_json["data"]["name"] == new_name
    assert res_json["data"]["remark"] == new_description

    # 删除角色
    res = client.delete(f"/user/role/{role_id}", headers=admin_header)
    assert_response_success(res)

    # 验证删除成功
    assert mdb["roles"].find_one({"_id": role_id}) is None
