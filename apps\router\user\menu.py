from fastapi import APIRouter, Request, Query, Body

from apps.common import permission_dp, unified_resp
import apps.models.user as UserModel
from apps.services.user import MenusService
from apps.models.permissions import FuncId


router = APIRouter(prefix="/user/menu", tags=["菜单管理"])


@router.post("/", dependencies=permission_dp(FuncId.MenusManage))
@unified_resp
async def menu_add(_: Request, menu: UserModel.MenuCreate):
    menu_srv = MenusService()
    return await menu_srv.menu_create(menu)


@router.get("/", dependencies=permission_dp(FuncId.MenusManage))
@unified_resp
async def menu_list(_: Request):
    menu_srv = MenusService()
    return await menu_srv.menu_list()


@router.get("/detail", dependencies=permission_dp(FuncId.MenusManage))
@unified_resp
async def menu_detail(_: Request, id_: int = Query(None)):
    menu_srv = MenusService()
    return await menu_srv.menu_detail(id_=id_)


@router.put("/", dependencies=permission_dp(FuncId.MenusManage))
@unified_resp
async def menu_update(_: Request, menu: UserModel.MenuUpdate = Body(...)):
    menu_srv = MenusService()
    return await menu_srv.menu_update(menu=menu)


@router.delete("/", dependencies=permission_dp(FuncId.MenusManage))
@unified_resp
async def menu_delete(_: Request, id_: int = Query(None)):
    menu_srv = MenusService()
    return await menu_srv.menu_delete(id_=id_)


@router.get("/route")
@unified_resp
async def menu_route(req: Request):
    menu_srv = MenusService()
    return await menu_srv.menu_route(req)


@router.post("/import", dependencies=permission_dp(FuncId.MenusManage))
@unified_resp
async def menu_import(_: Request, menu_data: list = Body(...)):
    menu_srv = MenusService()
    return await menu_srv.import_data(menu_data)


@router.get("/export", dependencies=permission_dp(FuncId.MenusManage))
@unified_resp
async def menu_export(_: Request):
    menu_srv = MenusService()
    return await menu_srv.export_data()
