"""
用户管理相关接口测试
"""

import faker
from bson import ObjectId
from starlette.testclient import TestClient


def build_user_data(username: str = "", password: str = "Test@123456") -> dict:
    """构建用户数据"""
    fake = faker.Faker("zh_CN")
    username = username or f"test_{fake.user_name()}"
    return {
        "username": username,
        "password": password,
        "nickname": fake.name(),
        "email": fake.email(),
        "phone": fake.phone_number(),
        "role_ids": [],
    }


def assert_response_success(res):
    """断言响应成功"""
    res_json = res.json()
    assert res_json["code"] == 1000
    return res_json


def test_user_login(client: TestClient):
    """测试用户登录"""
    # 测试正确的用户名和密码
    res = client.post("/user/login", json={"username": "admin", "password": "BuilderX@2024"})
    res_json = assert_response_success(res)
    assert "token" in res_json["data"]

    # 测试错误的密码
    res = client.post("/user/login", json={"username": "admin", "password": "wrong_password"})
    res_json = res.json()
    assert res_json["code"] != 1000

    # 测试不存在的用户
    res = client.post("/user/login", json={"username": "nonexistent_user", "password": "password"})
    res_json = res.json()
    assert res_json["code"] != 1000


# def test_user_logout(client: TestClient, admin_header):
#     """测试用户登出"""
#     res = client.get("/user/logout")
#     assert_response_success(res)


def test_user_crud(client: TestClient, admin_header, mdb):
    """测试用户增删改查"""
    fake = faker.Faker("zh_CN")
    username = f"test_user_{fake.user_name()}"
    user_data = build_user_data(username)

    # 创建用户
    res = client.post("/user", json=user_data, headers=admin_header)
    res_json = assert_response_success(res)
    user_id = str(res_json["data"]["id"])

    # 查询用户列表
    res = client.get("/user", params={"page": 1, "page_size": 10}, headers=admin_header)
    res_json = assert_response_success(res)
    user_list = res_json["data"]["lists"]
    is_found = False
    for user in user_list:
        if user["username"] == username:
            is_found = True
            break
    assert is_found

    # 更新用户
    update_data = user_data.copy()
    new_name = f"Updated {fake.name()}"
    update_data["id"] = user_id
    update_data["nickname"] = new_name
    res = client.put("/user", json=update_data, headers=admin_header)
    assert_response_success(res)

    # 验证更新成功
    res = client.get(f"/user/detail?uid={user_id}", headers=admin_header)
    res_json = assert_response_success(res)
    user = res_json["data"]
    assert user["username"] == username
    assert user["nickname"] == new_name

    # 删除用户
    res = client.delete(f"/user?uid={user_id}", headers=admin_header)
    assert_response_success(res)

    # 验证删除成功
    assert mdb["users"].find_one({"_id": ObjectId(user_id)}) is None


def test_user_info(client: TestClient, admin_header):
    """测试获取用户信息"""
    # 测试 /user/info 接口
    res = client.get("/user/info", headers=admin_header)
    res_json = assert_response_success(res)
    assert "user" in res_json["data"]
    assert "permissions" in res_json["data"]
    assert res_json["data"]["user"]["username"] == "admin"

    # 测试 /user/me 接口
    res = client.get("/user/me", headers=admin_header)
    res_json = assert_response_success(res)
    assert "user" in res_json["data"]
    assert "permissions" in res_json["data"]
    assert res_json["data"]["user"]["username"] == "admin"


def test_user_change_password(client: TestClient, admin_header):
    """测试修改密码"""
    # 创建一个测试用户
    fake = faker.Faker("zh_CN")
    username = f"pwd_test_{fake.user_name()}"
    old_password = "Test@123456"
    new_password = "NewTest@123456"

    user_data = build_user_data(username, old_password)
    res = client.post("/user", json=user_data, headers=admin_header)
    res_json = assert_response_success(res)
    user_id = str(res_json["data"]["id"])

    # 使用该用户登录
    res = client.post("/user/login", json={"username": username, "password": old_password})
    res_json = assert_response_success(res)
    token = res_json["data"]["token"]

    # 设置认证头
    headers = {"Authorization": f"Bearer {token}"}

    # 修改密码
    change_data = {"old_password": old_password, "new_password": new_password}
    res = client.put("/user/me/password", headers=headers, json=change_data)
    assert_response_success(res)

    # 使用旧密码登录应该失败
    res = client.post("/user/login", json={"username": username, "password": old_password})
    res_json = res.json()
    assert res_json["code"] != 1000

    # 使用新密码登录应该成功
    res = client.post("/user/login", json={"username": username, "password": new_password})
    res_json = assert_response_success(res)
    assert "token" in res_json["data"]

    # 清理：删除测试用户
    client.delete(f"/user/{user_id}", headers=admin_header)


def test_admin_reset_user_password(client: TestClient, admin_header):
    """测试重置密码"""
    # 创建一个测试用户
    fake = faker.Faker("zh_CN")
    username = f"reset_pwd_{fake.user_name()}"
    password = "Test@123456"

    user_data = build_user_data(username, password)
    res = client.post("/user", json=user_data, headers=admin_header)
    res_json = assert_response_success(res)
    user_id = str(res_json["data"]["id"])

    # 使用该用户登录
    res = client.post("/user/login", json={"username": username, "password": password})
    res_json = assert_response_success(res)
    assert res_json["data"]["token"]

    try:
        # 管理员发送重置密码请求
        res = client.post("/user/reset_password", json={"id": user_id}, headers=admin_header)
        res_json = assert_response_success(res)
        reset_token = res_json["data"]["token"]

        res = client.post(
            "/user/me/reset_password",
            json={"token": reset_token, "password": "new_password"},
        )
        assert_response_success(res)

        # 使用旧密码登录应该失败
        res = client.post("/user/login", json={"username": username, "password": password})
        res_json = res.json()
        assert res_json["code"] != 1000

        # 使用新密码登录应该成功
        res = client.post("/user/login", json={"username": username, "password": "new_password"})
        res_json = assert_response_success(res)
        assert "token" in res_json["data"]

    finally:
        # 清理：删除测试用户
        client.delete(f"/user/{user_id}", headers=admin_header)


def test_user_api_keys(client: TestClient, admin_header):
    """测试API Keys相关接口"""
    # 创建API Key
    fake = faker.Faker()
    api_key_data = {"description": f"Test API Key {fake.word()}"}

    res = client.post("/user/api_keys", json=api_key_data, headers=admin_header)
    res_json = assert_response_success(res)
    token = res_json["data"]["token"]

    # 获取API Key列表
    res = client.get("/user/api_keys", headers=admin_header)
    res_json = assert_response_success(res)

    assert res_json["data"]["count"] > 0
    assert res_json["data"]["data"]

    # 验证创建的API Key在列表中
    is_found = False
    for api_key in res_json["data"]["data"]:
        if token == api_key["token"]:
            is_found = True
            break
    assert is_found

    # 删除API Key
    res = client.delete("/user/api_keys", params={"token": token}, headers=admin_header)
    assert_response_success(res)

    # 验证API Key已删除
    res = client.get("/user/api_keys", headers=admin_header)
    res_json = assert_response_success(res)
    is_deleted = True
    for api_key in res_json["data"]["data"]:
        if token == api_key["token"]:
            is_deleted = False
            break
    assert is_deleted
